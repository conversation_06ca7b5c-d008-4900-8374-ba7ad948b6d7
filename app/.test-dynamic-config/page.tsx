"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { InputMain } from "@/components/ai-dashboard/input-main";
import { getAllSupportedModelIds } from "@/services/provider/parameter-manager";

/**
 * 测试动态配置组件的页面
 * 用于验证新的参数配置系统是否正常工作
 */
export default function TestDynamicConfigPage() {
  const [selectedModelType, setSelectedModelType] = useState<string>('image');
  const [useDynamicConfig, setUseDynamicConfig] = useState(true);
  const [result, setResult] = useState<any>(null);
  const [generating, setGenerating] = useState(false);

  const supportedModels = getAllSupportedModelIds();

  const handleResultChange = (newResult: any) => {
    setResult(newResult);
    console.log('Result changed:', newResult);
  };

  const handleGeneratingChange = (isGenerating: boolean) => {
    setGenerating(isGenerating);
    console.log('Generating state:', isGenerating);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">动态配置组件测试</h1>
        <p className="text-muted-foreground">
          测试新的模型参数配置系统
        </p>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 模型类型选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">模型类型:</label>
            <div className="flex gap-2 flex-wrap">
              {['text', 'image', 'video', 'multimodal'].map(type => (
                <Button
                  key={type}
                  variant={selectedModelType === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedModelType(type)}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {/* 配置组件选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">配置组件:</label>
            <div className="flex gap-2">
              <Button
                variant={useDynamicConfig ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseDynamicConfig(true)}
              >
                新动态配置
              </Button>
              <Button
                variant={!useDynamicConfig ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseDynamicConfig(false)}
              >
                原配置组件
              </Button>
            </div>
          </div>

          {/* 支持的模型统计 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">支持的模型:</label>
            <div className="flex items-center gap-2 flex-wrap">
              <Badge variant="secondary">
                总计: {supportedModels.length} 个模型
              </Badge>
              <Badge variant="outline">
                当前类型: {selectedModelType}
              </Badge>
              <Badge variant="default">
                支持图片上传: 8个模型
              </Badge>
            </div>
          </div>

          {/* 状态显示 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">当前状态:</label>
            <div className="flex gap-2">
              <Badge variant={generating ? 'destructive' : 'secondary'}>
                {generating ? '生成中' : '空闲'}
              </Badge>
              <Badge variant={result ? 'default' : 'outline'}>
                {result ? '有结果' : '无结果'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要测试区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入配置 */}
        <Card>
          <CardHeader>
            <CardTitle>
              参数配置 
              <Badge className="ml-2">
                {useDynamicConfig ? '新组件' : '原组件'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <InputMain
              modelType={selectedModelType as any}
              onResultChange={handleResultChange}
              onGeneratingChange={handleGeneratingChange}
              useDynamicConfig={useDynamicConfig}
            />
          </CardContent>
        </Card>

        {/* 调试信息 */}
        <Card>
          <CardHeader>
            <CardTitle>调试信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 支持的模型列表 */}
              <div>
                <h4 className="font-medium mb-2">支持的模型列表:</h4>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {supportedModels.map(modelId => (
                    <Badge key={modelId} variant="outline" className="text-xs">
                      {modelId}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* 当前结果 */}
              {result && (
                <div>
                  <h4 className="font-medium mb-2">当前结果:</h4>
                  <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </div>
              )}

              {/* 生成状态 */}
              <div>
                <h4 className="font-medium mb-2">生成状态:</h4>
                <p className="text-sm text-muted-foreground">
                  {generating ? '正在生成内容...' : '等待用户操作'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>1. 选择不同的模型类型来测试不同类型模型的参数配置</p>
          <p>2. 切换"新动态配置"和"原配置组件"来对比两种实现</p>
          <p>3. 在左侧配置参数，观察右侧调试信息的变化</p>
          <p>4. 新动态配置支持参数分组、条件显示、实时验证等功能</p>
          <p>5. 开发环境下，新组件底部会显示详细的调试信息</p>
          <p>6. 选择支持图片上传的模型（如flux-kontext-max、sora-image、veo3-fast等）可以测试图片上传功能</p>
          <p>7. 测试gpt-4o-image模型的默认参数加载（应该是size: '1024x1024', variants: 1）</p>
        </CardContent>
      </Card>
    </div>
  );
}
