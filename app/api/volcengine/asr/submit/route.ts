import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider, VolcengineASRRequest } from "@/services/provider/volcengine";



export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { audio_url, model_type, language, options } = body;

    // 验证必需参数
    if (!audio_url) {
      return respErr("Missing required parameter: audio_url");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 验证音频URL格式
    try {
      new URL(audio_url);
    } catch {
      return respErr("Invalid audio URL format");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 构建ASR请求
    const asrRequest: VolcengineASRRequest = {
      url: audio_url,
      language: language || "zh",
      use_itn: options?.use_itn !== false,
      use_capitalize: options?.use_capitalize !== false,
      max_lines: options?.max_lines || 1,
      // 回调URL为可选，本地开发时可以不设置，使用轮询方式
      callback_url: options?.enable_callback ? options.callback_url : undefined
    };

    let result;
    
    // 根据模型类型选择不同的ASR服务
    switch (model_type) {
      case 'bigmodel':
        result = await volcengineProvider.submitASRBigModel(asrRequest);
        break;
      case 'standard':
        result = await volcengineProvider.submitASRStandard(asrRequest, 'standard');
        break;
      case 'fast':
      default:
        result = await volcengineProvider.submitASRStandard(asrRequest, 'fast');
        break;
    }

    if (result.code === 10000) {
      return respData({
        task_id: result.id,
        model_type: model_type || 'fast',
        status: 'submitted'
      });
    } else {
      return respErr(`ASR submission failed: ${result.message}`);
    }

  } catch (error) {
    console.error('Volcengine ASR submission error:', error);
    return respErr(`ASR service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
