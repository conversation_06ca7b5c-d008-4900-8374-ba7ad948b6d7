import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider, VolcengineAsyncTTSRequest } from "@/services/provider/volcengine";



export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { text, voice_type, options } = body;

    // 验证必需参数
    if (!text) {
      return respErr("Missing required parameter: text");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 验证文本长度
    if (text.length > 100000) {
      return respErr("Text length exceeds 100,000 characters limit for async TTS");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 构建异步TTS请求
    const asyncTTSRequest: VolcengineAsyncTTSRequest = {
      text: text,
      voice_type: voice_type || "BV701_streaming",
      format: options?.format || "mp3",
      sample_rate: options?.sample_rate || 24000,
      volume: options?.volume || 1.0,
      speed: options?.speed || 1.0,
      pitch: options?.pitch || 1.0,
      enable_subtitle: options?.enable_subtitle || 0,
      // 回调URL为可选，本地开发时可以不设置，使用轮询方式
      callback_url: options?.enable_callback ? options.callback_url : undefined
    };

    // 提交异步任务
    const result = await volcengineProvider.submitAsyncTTS(asyncTTSRequest);

    if (result.code === 20000000) {
      return respData({
        task_id: result.task_id,
        status: 'submitted'
      });
    } else {
      return respErr(`Async TTS submission failed: ${result.message}`);
    }

  } catch (error) {
    console.error('Volcengine Async TTS submission error:', error);
    return respErr(`Async TTS service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
