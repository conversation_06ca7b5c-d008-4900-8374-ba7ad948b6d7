import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider } from "@/services/provider/volcengine";



export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const taskId = searchParams.get('task_id');

    // 验证必需参数
    if (!taskId) {
      return respErr("Missing required parameter: task_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 查询任务结果
    const result = await volcengineProvider.queryAsyncTTS(taskId);

    if (result.code === 20000000) {
      return respData({
        task_id: taskId,
        status: result.status,
        audio_url: result.audio_url,
        subtitle_url: result.subtitle_url,
        duration: result.duration,
        file_size: result.file_size
      });
    } else {
      return respErr(`Query failed: ${result.message}`);
    }

  } catch (error) {
    console.error('Volcengine Async TTS query error:', error);
    return respErr(`Query service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
