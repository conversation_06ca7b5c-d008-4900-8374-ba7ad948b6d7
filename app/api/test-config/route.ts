/**
 * 测试配置加载的API端点
 */

import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 测试导入
    const {
      getModelParameterConfig,
      getAllSupportedModelIds,
      getModelDefaultParameters
    } = await import('@/services/provider/parameter-manager');

    const supportedModels = getAllSupportedModelIds();
    
    // 测试获取一个模型的配置
    const testModelId = supportedModels[0];
    const config = testModelId ? getModelParameterConfig(testModelId) : null;
    const defaults = testModelId ? getModelDefaultParameters(testModelId) : {};

    return NextResponse.json({
      code: 0,
      msg: 'success',
      data: {
        supportedModelsCount: supportedModels.length,
        supportedModels: supportedModels.slice(0, 5), // 只返回前5个
        testModel: {
          modelId: testModelId,
          hasConfig: !!config,
          parameterCount: config?.parameters.length || 0,
          defaults
        }
      }
    });

  } catch (error) {
    console.error('Config test error:', error);
    return NextResponse.json({
      code: -1,
      msg: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      data: null
    });
  }
}
