import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { GRSAIProvider } from "@/services/provider/grsai";
import { ReplicateAIService } from "@/services/provider/replicate";
import { getAIModelUsageByExternalRequestId, getAIModelUsageByTaskId, updateAIModelUsageByTaskId, getActiveAIModelById } from "@/models/ai-model";
import { getIsoTimestr } from "@/lib/time";
import { extractUrlsFromGRSAIResponse, transferMultipleFiles, updateResponseDataUrls } from "@/services/file-transfer";
import { decreaseCreditsForAIModel, refundCreditsForAIModel } from "@/services/credit";



export async function POST(req: Request) {
  try {
    const { task_id, external_id } = await req.json();
    console.log(`[Result API] Received request - task_id: ${task_id}, external_id: ${external_id}`);

    if (!task_id && !external_id) {
      return respErr("Missing required parameter: task_id or external_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    let usage: any;
    let externalTaskId: string;

    // 确定查询的任务ID和使用记录
    if (task_id) {
      // 优先使用task_id查找使用记录
      usage = await getAIModelUsageByTaskId(task_id);
      if (!usage) {
        return respErr("Task not found");
      }

      // 验证用户权限
      if (usage.user_uuid !== user_uuid) {
        return respErr("Access denied");
      }

      // 使用external_request_id作为外部任务ID
      if (usage.external_request_id) {
        externalTaskId = usage.external_request_id;
        console.log(`[Result API] Using external_request_id: ${externalTaskId}`);
      } else {
        return respErr("External task ID not found");
      }
    } else if (external_id) {
      // 直接使用external_id作为任务ID
      externalTaskId = external_id;
      console.log(`[Result API] Using external_id as task ID: ${externalTaskId}`);

      // 尝试查找使用记录以验证权限
      usage = await getAIModelUsageByExternalRequestId(external_id);
      if (usage && usage.user_uuid !== user_uuid) {
        return respErr("Access denied");
      }
    } else {
      return respErr("Missing required parameter: task_id or external_id");
    }

    try {
      // 确定提供商类型
      let result: any;
      let taskData: any;
      let provider = 'grsai'; // 默认提供商

      // 检查提供商类型
      if (usage && usage.provider) {
        provider = usage.provider;
      } else if (usage && usage.model_id) {
        const modelConfig = await getActiveAIModelById(usage.model_id);
        provider = modelConfig?.provider || 'grsai';
      }

      console.log(`[Result API] Using provider: ${provider}`);

      if (provider === 'replicate') {
        // 查询Replicate结果
        console.log(`[Result API] Querying Replicate result for prediction ID: ${externalTaskId}`);
        const replicateService = new ReplicateAIService();
        const replicateResult = await replicateService.getReplicateResult(externalTaskId);

        console.log(`[Result API] Replicate result response:`, replicateResult);

        // 转换Replicate响应格式
        taskData = {
          status: replicateResult.status,
          progress: replicateResult.progress,
          url: replicateResult.urls?.[0], // 取第一个URL
          urls: replicateResult.urls,
          output: replicateResult.output,
          error: replicateResult.error,
          logs: replicateResult.logs
        };
      } else {
        // 查询GRSAI结果
        console.log(`[Result API] Querying GRSAI result for task ID: ${externalTaskId}`);

        const grsaiProvider = new GRSAIProvider();
        result = await grsaiProvider.getResult(externalTaskId);

        console.log(`[Result API] GRSAI result response:`, result);

        if (result.code !== 0) {
          console.log(`[Result API] GRSAI API error: code=${result.code}, msg=${result.msg}`);
          return respErr(`GRSAI API error: ${result.msg}`);
        }

        taskData = result.data;
      }

      console.log(`[Result API] Task data:`, taskData);

      // 如果没有找到使用记录但有task_id，直接处理响应
      if (!usage && (task_id || external_id)) {
        console.log(`[Result API] Direct task query without usage record`);
        const directModelType = getTypeFromModel(externalTaskId);
        const response = {
          id: task_id || externalTaskId,
          task_id: task_id || externalTaskId,
          type: directModelType,
          status: mapProviderStatus(taskData.status),
          progress: taskData.progress,
          result: buildResult(taskData, directModelType),
          error: taskData.status === 'failed' ? {
            reason: taskData.failure_reason || 'error',
            detail: taskData.error || 'Unknown error'
          } : undefined
        };
        return respData(response);
      }

      // 构建统一响应格式
      const modelType = usage ? getTypeFromModel(usage.model_id) : getTypeFromModel(externalTaskId);
      const taskIdValue = usage?.task_id || task_id || externalTaskId;
      const response = {
        id: taskIdValue,
        task_id: taskIdValue,
        type: modelType,
        status: mapProviderStatus(taskData.status),
        progress: taskData.progress,
        result: buildResult(taskData, modelType),
        error: taskData.status === 'failed' ? {
          reason: taskData.failure_reason || 'error',
          detail: taskData.error || 'Unknown error'
        } : undefined
      };

      // 如果任务成功完成，进行积分扣减和文件转存
      if (response.status === 'success' && taskData.url) {
        console.log(`[Result API] Task completed successfully, starting credit deduction and file transfer`);

        // 扣减积分（如果还没有扣减过）
        if (usage && usage.status !== 'success') {
          try {
            await decreaseCreditsForAIModel({
              user_uuid,
              model_id: usage.model_id,
              request_id: taskIdValue,
              credits: usage.credits_consumed
            });
            console.log(`[Result API] Credits deducted: ${usage.credits_consumed} for task ${taskIdValue}`);
          } catch (creditError) {
            console.error(`[Result API] Failed to deduct credits:`, creditError);
            // 积分扣减失败不影响主流程，但要记录错误
          }
        }

        try {
          // 提取所有需要转存的URL
          const urlsToTransfer = extractUrlsFromGRSAIResponse(taskData);
          console.log(`[Result API] URLs to transfer:`, urlsToTransfer);

          if (urlsToTransfer.length > 0) {
            // 推断模型类型
            const inferredModelType = usage?.model_id?.includes('veo') ? 'video' : 'image';

            // 批量转存文件，使用task_id作为任务ID
            const transferResults = await transferMultipleFiles(urlsToTransfer, inferredModelType, taskIdValue);
            console.log(`[Result API] Transfer results:`, transferResults);

            // 检查是否有成功的转存
            const successfulTransfers = transferResults.filter(r => r.success);
            if (successfulTransfers.length > 0) {
              // 创建URL映射
              const urlMapping: Record<string, string> = {};
              successfulTransfers.forEach(result => {
                if (result.url) {
                  urlMapping[result.originalUrl] = result.url;
                }
              });

              // 更新响应数据中的URL
              const updatedTaskData = updateResponseDataUrls(taskData, urlMapping);
              console.log(`[Result API] Updated URLs in response data`);

              // 更新response中的结果
              response.result = buildResult(updatedTaskData, modelType);

              // 更新数据库记录
              if (usage) {
                await updateAIModelUsageByTaskId(taskIdValue, {
                  status: 'success',
                  response_data: { ...usage.response_data, ...updatedTaskData },
                  completed_at: getIsoTimestr()
                });
              }
            } else {
              console.log(`[Result API] File transfer failed, using original URLs`);
              // 即使转存失败，也要更新状态
              if (usage) {
                await updateAIModelUsageByTaskId(taskIdValue, {
                  status: 'success',
                  response_data: { ...usage.response_data, ...taskData },
                  completed_at: getIsoTimestr()
                });
              }
            }
          }
        } catch (transferError) {
          console.error(`[Result API] File transfer error:`, transferError);
          // 转存失败不影响主流程，继续使用原始URL
          if (usage) {
            await updateAIModelUsageByTaskId(taskIdValue, {
              status: 'success',
              response_data: { ...usage.response_data, ...taskData },
              completed_at: getIsoTimestr()
            });
          }
        }
      } else if (usage && usage.status !== response.status) {
        // 其他状态变化的更新

        // 如果任务失败且错误类型为"error"，退还积分
        if (response.status === 'failed' && response.error?.reason === 'error' && usage.status !== 'failed') {
          try {
            await refundCreditsForAIModel({
              user_uuid,
              model_id: usage.model_id,
              request_id: taskIdValue,
              credits: usage.credits_consumed,
              original_trans_no: `${taskIdValue}_deduction` // 简化的交易号
            });
            console.log(`[Result API] Credits refunded: ${usage.credits_consumed} for failed task ${taskIdValue}`);
          } catch (refundError) {
            console.error(`[Result API] Failed to refund credits:`, refundError);
          }
        }

        await updateAIModelUsageByTaskId(taskIdValue, {
          status: response.status === 'success' ? 'success' :
                  response.status === 'failed' ? 'failed' : 'pending',
          response_data: { ...usage.response_data, ...taskData },
          completed_at: response.status === 'success' || response.status === 'failed'
            ? getIsoTimestr() : undefined
        });
      }

      return respData(response);
    } catch (error) {
      console.error("Failed to query GRSAI result:", error);
      return respErr("Failed to query result");
    }
  } catch (error) {
    console.error("Request processing failed:", error);
    return respErr("Request processing failed");
  }
}

/**
 * 根据模型ID或任务ID推断类型
 */
function getTypeFromModel(modelId: string): string {
  if (modelId.includes('veo')) return 'video';
  if (modelId.includes('flux') || modelId.includes('image') || modelId.includes('sora')) return 'image';
  // 对于UUID格式的任务ID，默认返回image（因为大多数异步任务是图像生成）
  if (modelId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    return 'image';
  }
  return 'text';
}

/**
 * 映射提供商状态到统一状态
 */
function mapProviderStatus(status: string): string {
  switch (status) {
    // GRSAI状态
    case 'succeeded': return 'success';
    case 'failed': return 'failed';
    case 'running': return 'running';

    // Replicate状态
    case 'starting': return 'pending';
    case 'processing': return 'running';
    case 'canceled': return 'failed';

    default: return 'pending';
  }
}

/**
 * 根据URL扩展名判断文件类型
 */
function getFileTypeFromUrl(url: string): 'image' | 'video' | 'unknown' {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'webp', 'gif', 'bmp', 'svg', 'tiff', 'ico'];
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp'];

  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    const extension = pathname.split('.').pop();

    if (extension && imageExtensions.includes(extension)) {
      return 'image';
    }
    if (extension && videoExtensions.includes(extension)) {
      return 'video';
    }
  } catch (e) {
    // URL解析失败，返回unknown
  }

  return 'unknown';
}

/**
 * 构建结果对象
 */
function buildResult(taskData: any, modelType: string): any {
  if (taskData.status !== 'succeeded' && taskData.status !== 'success') {
    return undefined;
  }

  // 处理Replicate的输出格式
  if (taskData.urls && Array.isArray(taskData.urls)) {
    const images = taskData.urls.map((url: string) => ({
      url,
      width: 1024,
      height: 1024
    }));
    return { images };
  }

  // 处理GRSAI的输出格式
  if (!taskData.url) {
    return undefined;
  }

  // 首先根据模型类型判断，然后用文件扩展名作为辅助判断
  const fileType = getFileTypeFromUrl(taskData.url);
  let isVideo = false;

  if (modelType === 'video') {
    isVideo = true;
  } else if (modelType === 'image' || modelType === 'multimodal') {
    isVideo = false;
  } else {
    // 如果模型类型不明确，使用文件扩展名判断
    isVideo = fileType === 'video';
  }

  if (isVideo) {
    return {
      video: {
        url: taskData.url
      }
    };
  } else {
    // 图像结果
    const images = [];
    const addedUrls = new Set<string>(); // 用于去重

    // 处理单个图像
    if (taskData.url && !addedUrls.has(taskData.url)) {
      images.push({
        url: taskData.url,
        width: taskData.width || 1024,
        height: taskData.height || 1024
      });
      addedUrls.add(taskData.url);
    }

    // 处理多个结果（如果有results数组）
    if (taskData.results && Array.isArray(taskData.results)) {
      taskData.results.forEach((result: any) => {
        if (result.url && !addedUrls.has(result.url)) {
          images.push({
            url: result.url,
            width: result.width || 1024,
            height: result.height || 1024
          });
          addedUrls.add(result.url);
        }
      });
    }

    return { images };
  }
}
