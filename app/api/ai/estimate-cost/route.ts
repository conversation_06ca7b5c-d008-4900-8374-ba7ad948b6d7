import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getActiveAIModelById, calculateModelCost } from "@/models/ai-model";
import { getUserCredits } from "@/services/credit";



export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { model, type, prompt, options } = body as {
      model: string;
      type: string;
      prompt?: string;
      options?: { max_tokens?: number; variants?: number }
    };

    // 验证必需参数
    if (!model || !type) {
      return respErr("Missing required parameters: model, type");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 获取模型配置
    const modelConfig = await getActiveAIModelById(model);
    if (!modelConfig) {
      return respErr(`Model ${model} not found or inactive`);
    }

    // 获取用户积分信息
    const userCredits = await getUserCredits(user_uuid);

    let estimatedCost: number;
    let costBredown: any = {};

    try {
      switch (type) {
        case 'text':
          const inputTokens = prompt ? estimateTokens(prompt) : 0;
          const maxTokens = options?.max_tokens || 1000;
          estimatedCost = await calculateModelCost(model, inputTokens, maxTokens);
          
          costBredown = {
            input_tokens: inputTokens,
            estimated_output_tokens: maxTokens,
            total_tokens: inputTokens + maxTokens,
            credits_per_1k_tokens: modelConfig.credits_per_unit,
            calculation: `(${inputTokens} + ${maxTokens}) / 1000 * ${modelConfig.credits_per_unit}`
          };
          break;

        case 'image':
          const variants = options?.variants || 1;
          estimatedCost = modelConfig.credits_per_unit * variants;
          
          costBredown = {
            base_cost: modelConfig.credits_per_unit,
            variants: variants,
            calculation: `${modelConfig.credits_per_unit} * ${variants}`
          };
          break;

        case 'video':
          estimatedCost = modelConfig.credits_per_unit;
          
          costBredown = {
            base_cost: modelConfig.credits_per_unit,
            calculation: `${modelConfig.credits_per_unit} (fixed cost per video)`
          };
          break;

        default:
          estimatedCost = modelConfig.credits_per_unit;
          costBredown = {
            base_cost: modelConfig.credits_per_unit
          };
      }
    } catch (error) {
      return respErr("Failed to calculate cost");
    }

    // 检查是否有足够积分
    const canAfford = userCredits.left_credits >= estimatedCost;
    const shortfall = canAfford ? 0 : estimatedCost - userCredits.left_credits;

    return respData({
      model: {
        id: modelConfig.model_id,
        name: modelConfig.model_name,
        type: modelConfig.model_type,
        provider: modelConfig.provider
      },
      cost_estimate: {
        estimated_credits: estimatedCost,
        breakdown: costBredown,
        unit_type: modelConfig.unit_type
      },
      user_credits: {
        available: userCredits.left_credits,
        can_afford: canAfford,
        shortfall: shortfall,
        is_pro: userCredits.is_pro || false
      },
      recommendations: generateRecommendations(modelConfig, estimatedCost, userCredits)
    });
  } catch (error) {
    console.error("Cost estimation failed:", error);
    return respErr("Cost estimation failed");
  }
}

/**
 * 估算token数量
 */
function estimateTokens(text: string): number {
  if (!text) return 0;
  
  // 简单估算：中文按字符数，英文按单词数 * 1.3
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(w => w.length > 0).length;
  return Math.ceil(chineseChars + englishWords * 1.3);
}

/**
 * 生成使用建议
 */
function generateRecommendations(modelConfig: any, estimatedCost: number, userCredits: any): string[] {
  const recommendations: string[] = [];

  // 积分不足建议
  if (userCredits.left_credits < estimatedCost) {
    recommendations.push("积分不足，建议充值或选择成本更低的模型");
  }

  // 模型选择建议
  if (modelConfig.model_type === 'text') {
    if (estimatedCost > 10) {
      recommendations.push("对于简单对话，可以考虑使用 gemini-2.5-flash-lite 以节省积分");
    }
  } else if (modelConfig.model_type === 'image') {
    if (estimatedCost > 60) {
      recommendations.push("对于一般图像生成，可以考虑使用 flux-pro-1.1 以节省积分");
    }
  }

  // 积分管理建议
  if (userCredits.left_credits < 100) {
    recommendations.push("积分余额较低，建议及时充值以避免服务中断");
  }

  // 新用户建议
  if (!userCredits.is_pro && userCredits.left_credits <= 10) {
    recommendations.push("新用户建议先尝试低成本模型熟悉功能");
  }

  return recommendations;
}
