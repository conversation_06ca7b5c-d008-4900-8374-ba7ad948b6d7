/**
 * 模型参数配置API端点
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getModelParameterConfig,
  getAllSupportedModelIds,
  getModelDefaultParameters
} from '@/services/provider/parameter-manager';

/**
 * GET /api/ai/model-parameters
 * 获取模型参数配置
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');
    const action = searchParams.get('action') || 'config';

    // 获取所有支持的模型列表
    if (action === 'list') {
      const supportedModels = getAllSupportedModelIds();
      return NextResponse.json({
        code: 0,
        msg: 'success',
        data: {
          supportedModels,
          count: supportedModels.length
        }
      });
    }

    // 获取特定模型的参数配置
    if (action === 'config' && modelId) {
      const config = getModelParameterConfig(modelId);
      
      if (!config) {
        return NextResponse.json({
          code: -1,
          msg: `Model ${modelId} parameter configuration not found`,
          data: null
        });
      }

      return NextResponse.json({
        code: 0,
        msg: 'success',
        data: {
          config,
          defaults: getModelDefaultParameters(modelId)
        }
      });
    }

    // 获取特定模型的默认参数
    if (action === 'defaults' && modelId) {
      const defaults = getModelDefaultParameters(modelId);
      
      return NextResponse.json({
        code: 0,
        msg: 'success',
        data: {
          modelId,
          defaults
        }
      });
    }

    // 参数错误
    return NextResponse.json({
      code: -1,
      msg: 'Invalid parameters. Use ?action=list or ?action=config&modelId=xxx or ?action=defaults&modelId=xxx',
      data: null
    });

  } catch (error) {
    console.error('Model parameters API error:', error);
    return NextResponse.json({
      code: -1,
      msg: 'Internal server error',
      data: null
    });
  }
}

/**
 * POST /api/ai/model-parameters/validate
 * 验证模型参数
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { modelId, parameters } = body;

    if (!modelId || !parameters) {
      return NextResponse.json({
        code: -1,
        msg: 'Missing modelId or parameters',
        data: null
      });
    }

    const config = getModelParameterConfig(modelId);
    if (!config) {
      return NextResponse.json({
        code: -1,
        msg: `Model ${modelId} configuration not found`,
        data: null
      });
    }

    // 验证每个参数
    const validationResults: Record<string, { valid: boolean; error?: string }> = {};
    let hasErrors = false;

    Object.entries(parameters).forEach(([paramName, value]) => {
      const param = config.parameters.find(p => p.name === paramName);
      if (!param) {
        validationResults[paramName] = {
          valid: false,
          error: 'Parameter not found in configuration'
        };
        hasErrors = true;
        return;
      }

      // 基础验证
      if (param.required && (value === undefined || value === null || value === '')) {
        validationResults[paramName] = {
          valid: false,
          error: `${param.description}是必填项`
        };
        hasErrors = true;
        return;
      }

      // 类型验证
      switch (param.type) {
        case 'number':
          const numValue = typeof value === 'number' ? value : parseFloat(value as string);
          if (isNaN(numValue)) {
            validationResults[paramName] = {
              valid: false,
              error: `${param.description}必须是数字`
            };
            hasErrors = true;
            return;
          }
          if (param.min !== undefined && numValue < param.min) {
            validationResults[paramName] = {
              valid: false,
              error: `${param.description}不能小于${param.min}`
            };
            hasErrors = true;
            return;
          }
          if (param.max !== undefined && numValue > param.max) {
            validationResults[paramName] = {
              valid: false,
              error: `${param.description}不能大于${param.max}`
            };
            hasErrors = true;
            return;
          }
          break;

        case 'select':
          if (param.options && !param.options.some(opt => opt.value === value)) {
            validationResults[paramName] = {
              valid: false,
              error: `${param.description}选项无效`
            };
            hasErrors = true;
            return;
          }
          break;

        case 'boolean':
          if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
            validationResults[paramName] = {
              valid: false,
              error: `${param.description}必须是布尔值`
            };
            hasErrors = true;
            return;
          }
          break;
      }

      validationResults[paramName] = { valid: true };
    });

    return NextResponse.json({
      code: 0,
      msg: hasErrors ? 'Validation failed' : 'Validation passed',
      data: {
        valid: !hasErrors,
        results: validationResults
      }
    });

  } catch (error) {
    console.error('Parameter validation API error:', error);
    return NextResponse.json({
      code: -1,
      msg: 'Internal server error',
      data: null
    });
  }
}
