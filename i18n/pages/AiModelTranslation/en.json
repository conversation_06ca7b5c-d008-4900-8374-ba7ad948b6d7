{"models": {"grsai.gemini-2-5-pro": {"name": "Gemini 2.5 Pro111", "description": "Advanced conversational model, suitable for complex tasks and professional use", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate, affects response length"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "Fast conversational model, quick response and high efficiency", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "Lightweight conversational model, low cost and basic functionality", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}}}, "grsai.gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o lightweight version, balanced performance and cost", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini full-featured version with vision and multimodal capabilities", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness"}, "uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}, "grsai.gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o complete version with all advanced features and multimodal support", "parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}}}}}