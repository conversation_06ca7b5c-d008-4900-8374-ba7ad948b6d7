{"models": {"grsai.gemini-2-5-pro": {"name": "Gemini 2.5 Pro", "description": "高级对话模型，适合复杂任务和专业用途", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量，影响回答长度"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性，0为最保守，1为最创新"}, "top_p": {"description": "核心采样", "tooltip": "控制词汇选择范围，较小值使输出更集中"}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}}}, "grsai.gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "快速对话模型，响应迅速，效率高", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性"}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}}}, "grsai.gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "轻量级对话模型，成本低廉，功能基础", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性"}}}, "grsai.gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o 轻量版本，性能与成本平衡", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性"}, "top_p": {"description": "核心采样", "tooltip": "控制词汇选择范围"}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}}}, "grsai.o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini 全功能版本，支持视觉等多模态能力", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性"}, "uploadedImages": {"description": "图片输入", "tooltip": "上传图片进行视觉分析和理解"}, "vision_detail": {"description": "图像分析细节", "tooltip": "控制图像分析的详细程度", "options": {"auto": {"label": "自动", "description": "自动选择最佳图像处理方式"}, "low": {"label": "低细节", "description": "快速处理，适合简单图像"}, "high": {"label": "高细节", "description": "详细分析，适合复杂图像"}}}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}}}, "grsai.gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o 完整版本，具备所有高级功能和多模态支持", "parameters": {"max_tokens": {"description": "最大输出长度", "tooltip": "生成文本的最大token数量"}, "temperature": {"description": "创造性程度", "tooltip": "控制输出的随机性，0为最保守，1为最创新"}, "uploadedImages": {"description": "图片输入", "tooltip": "上传图片进行视觉分析和理解"}, "vision_detail": {"description": "图像分析细节", "tooltip": "控制图像分析的详细程度", "options": {"auto": {"label": "自动", "description": "自动选择最佳图像处理方式"}, "low": {"label": "低细节", "description": "快速处理，适合简单图像"}, "high": {"label": "高细节", "description": "详细分析，适合复杂图像"}}}, "top_p": {"description": "核心采样", "tooltip": "控制词汇选择范围，较小值使输出更集中"}, "stream": {"description": "流式输出", "tooltip": "启用后将实时显示生成过程"}}}}}