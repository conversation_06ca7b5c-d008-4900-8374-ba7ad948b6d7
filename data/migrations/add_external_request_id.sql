-- 重构ai_model_usage表：移除request_id，添加task_id、external_request_id和provider字段
-- 统一ID管理和多提供商支持

-- 如果表存在request_id字段，先备份数据然后重建表
DO $$
BEGIN
  -- 检查是否存在request_id字段
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'ai_model_usage' AND column_name = 'request_id'
  ) THEN
    -- 创建临时表备份数据
    CREATE TEMP TABLE ai_model_usage_backup AS
    SELECT * FROM ai_model_usage;

    -- 删除原表
    DROP TABLE ai_model_usage CASCADE;

    -- 重新创建表（不包含request_id）
    CREATE TABLE ai_model_usage (
      id SERIAL PRIMARY KEY,
      user_uuid VARCHAR(255) NOT NULL,
      model_id VARCHAR(100) NOT NULL,
      task_id VARCHAR(255) UNIQUE NOT NULL,
      external_request_id VARCHAR(255),
      provider VARCHAR(50),
      input_size INT,
      output_size INT,
      credits_consumed INT NOT NULL,
      status VARCHAR(50) NOT NULL DEFAULT 'pending',
      error_reason VARCHAR(100),
      error_detail TEXT,
      request_params JSONB,
      response_data JSONB,
      started_at TIMESTAMPTZ DEFAULT NOW(),
      completed_at TIMESTAMPTZ,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );

    -- 迁移数据（生成新的task_id）
    INSERT INTO ai_model_usage (
      user_uuid, model_id, task_id, external_request_id, provider,
      input_size, output_size, credits_consumed, status, error_reason, error_detail,
      request_params, response_data, started_at, completed_at, created_at
    )
    SELECT
      user_uuid, model_id,
      gen_random_uuid()::text as task_id,
      CASE
        WHEN response_data->>'data' IS NOT NULL AND response_data->'data'->>'id' IS NOT NULL
        THEN response_data->'data'->>'id'
        WHEN response_data->>'id' IS NOT NULL
        THEN response_data->>'id'
        ELSE NULL
      END as external_request_id,
      'grsai' as provider,
      input_size, output_size, credits_consumed, status, error_reason, error_detail,
      request_params, response_data, started_at, completed_at, created_at
    FROM ai_model_usage_backup;

  ELSE
    -- 如果没有request_id字段，只添加缺失的字段
    ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS task_id VARCHAR(255) UNIQUE;
    ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS external_request_id VARCHAR(255);
    ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS provider VARCHAR(50);

    -- 为现有记录生成task_id
    UPDATE ai_model_usage
    SET
      task_id = COALESCE(task_id, gen_random_uuid()::text),
      provider = COALESCE(provider, 'grsai'),
      external_request_id = COALESCE(
        external_request_id,
        CASE
          WHEN response_data->>'data' IS NOT NULL AND response_data->'data'->>'id' IS NOT NULL
          THEN response_data->'data'->>'id'
          WHEN response_data->>'id' IS NOT NULL
          THEN response_data->>'id'
          ELSE NULL
        END
      )
    WHERE task_id IS NULL OR provider IS NULL OR external_request_id IS NULL;
  END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_user ON ai_model_usage(user_uuid);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_model ON ai_model_usage(model_id);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_status ON ai_model_usage(status);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_task_id ON ai_model_usage(task_id);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_external_request ON ai_model_usage(external_request_id);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_provider ON ai_model_usage(provider);

-- 添加字段注释
COMMENT ON COLUMN ai_model_usage.task_id IS '统一的任务ID，用于前端查询';
COMMENT ON COLUMN ai_model_usage.external_request_id IS '外部提供商的任务ID (GRSAI/Replicate等)';
COMMENT ON COLUMN ai_model_usage.provider IS '提供商：grsai, replicate等';
