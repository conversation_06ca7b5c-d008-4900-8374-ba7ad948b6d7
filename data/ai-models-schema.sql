-- AI 模型管理相关数据库表

-- AI 模型配置表
CREATE TABLE ai_models (
    id SERIAL PRIMARY KEY,
    model_id VARCHAR(100) UNIQUE NOT NULL,
    model_name VARCHAR(200) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- text, image, video, multimodal
    provider VARCHAR(50) NOT NULL, -- grsai, openai, anthropic
    api_endpoint VARCHAR(200) NOT NULL,
    credits_per_unit INT NOT NULL, -- 每单位消耗的积分
    unit_type VARCHAR(50) NOT NULL, -- tokens, images, videos
    is_active BOOLEAN NOT NULL DEFAULT true,
    description TEXT, -- 废弃字段，保留兼容性
    description_i18n JSONB, -- 多语言描述 {"en": "English", "zh": "中文"}
    model_name_i18n JSONB, -- 多语言模型名称 {"en": "English", "zh": "中文"}
    max_input_size INT,
    supported_features JSONB, -- 支持的功能列表
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_ai_models_type ON ai_models(model_type);
CREATE INDEX idx_ai_models_provider ON ai_models(provider);
CREATE INDEX idx_ai_models_active ON ai_models(is_active);

-- AI 模型使用记录表
CREATE TABLE ai_model_usage (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    task_id VARCHAR(255) UNIQUE NOT NULL, -- 统一的任务ID，用于前端查询
    external_request_id VARCHAR(255), -- 外部提供商的任务ID (GRSAI/Replicate等)
    provider VARCHAR(50), -- 提供商：grsai, replicate等
    input_size INT, -- 输入大小（tokens/pixels等）
    output_size INT, -- 输出大小
    credits_consumed INT NOT NULL, -- 实际消耗的积分
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, success, failed, cancelled
    error_reason VARCHAR(100), -- 错误原因：error, output_moderation, input_moderation
    error_detail TEXT, -- 详细错误信息
    request_params JSONB, -- 请求参数
    response_data JSONB, -- 响应数据
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_ai_model_usage_user ON ai_model_usage(user_uuid);
CREATE INDEX idx_ai_model_usage_model ON ai_model_usage(model_id);
CREATE INDEX idx_ai_model_usage_status ON ai_model_usage(status);
CREATE INDEX idx_ai_model_usage_task_id ON ai_model_usage(task_id);
CREATE INDEX idx_ai_model_usage_external_request ON ai_model_usage(external_request_id);
CREATE INDEX idx_ai_model_usage_provider ON ai_model_usage(provider);
CREATE INDEX idx_ai_model_usage_created ON ai_model_usage(created_at);

-- 为多语言字段创建索引
CREATE INDEX idx_ai_models_description_i18n ON ai_models USING GIN (description_i18n);
CREATE INDEX idx_ai_models_model_name_i18n ON ai_models USING GIN (model_name_i18n);

-- 创建获取本地化内容的函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  -- 如果content为空，返回空字符串
  IF content IS NULL THEN
    RETURN '';
  END IF;

  -- 尝试获取指定语言的内容
  IF content ? locale THEN
    RETURN content ->> locale;
  END IF;

  -- 尝试获取fallback语言的内容
  IF content ? fallback THEN
    RETURN content ->> fallback;
  END IF;

  -- 返回第一个可用的值
  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 添加字段注释
COMMENT ON COLUMN ai_models.description IS 'DEPRECATED: Use description_i18n instead';
COMMENT ON COLUMN ai_models.model_name IS 'DEPRECATED: Use model_name_i18n instead';
COMMENT ON COLUMN ai_models.description_i18n IS 'Multilingual description in JSON format: {"en": "English", "zh": "中文"}';
COMMENT ON COLUMN ai_models.model_name_i18n IS 'Multilingual model name in JSON format: {"en": "English", "zh": "中文"}';

-- 扩展现有的 credits 表，添加模型相关字段
ALTER TABLE credits ADD COLUMN IF NOT EXISTS model_id VARCHAR(100);
ALTER TABLE credits ADD COLUMN IF NOT EXISTS usage_id INT;
ALTER TABLE credits ADD COLUMN IF NOT EXISTS request_id VARCHAR(255);

-- 为新字段创建索引
CREATE INDEX IF NOT EXISTS idx_credits_model_id ON credits(model_id);
CREATE INDEX IF NOT EXISTS idx_credits_usage_id ON credits(usage_id);
CREATE INDEX IF NOT EXISTS idx_credits_request_id ON credits(request_id);

-- 添加外键约束
ALTER TABLE ai_model_usage 
ADD CONSTRAINT fk_ai_model_usage_model 
FOREIGN KEY (model_id) REFERENCES ai_models(model_id);

-- 插入 GRSAI 模型数据
INSERT INTO ai_models (model_id, model_name, model_type, provider, api_endpoint, credits_per_unit, unit_type, description, supported_features) VALUES
-- 文本生成模型
('gemini-2.5-pro', 'Gemini 2.5 Pro', 'text', 'grsai', '/v1/chat/completions', 10, 'tokens', '高级对话模型，适合复杂任务', '["chat", "stream"]'),
('gemini-2.5-flash', 'Gemini 2.5 Flash', 'text', 'grsai', '/v1/chat/completions', 5, 'tokens', '快速对话模型，响应迅速', '["chat", "stream"]'),
('gemini-2.5-flash-lite', 'Gemini 2.5 Flash Lite', 'text', 'grsai', '/v1/chat/completions', 3, 'tokens', '轻量级对话模型，成本低廉', '["chat", "stream"]'),
('gpt-4o-mini', 'GPT-4o Mini', 'text', 'grsai', '/v1/chat/completions', 6, 'tokens', 'GPT-4o 轻量版本', '["chat", "stream"]'),

-- 多模态模型
('o4-mini-all', 'GPT-4o Mini All', 'multimodal', 'grsai', '/v1/chat/completions', 8, 'tokens', 'GPT-4o Mini 全功能版本', '["chat", "stream", "vision", "image_upload"]'),
('gpt-4o-all', 'GPT-4o All', 'multimodal', 'grsai', '/v1/chat/completions', 12, 'tokens', 'GPT-4o 全功能版本', '["chat", "stream", "vision", "image_upload"]'),

-- 图像生成模型
('sora-image', 'Sora Image', 'image', 'grsai', '/v1/draw/completions', 50, 'images', 'Sora 图像生成模型', '["text2image", "variants", "reference", "image_upload"]'),
('gpt-4o-image', 'GPT-4o Image', 'image', 'grsai', '/v1/draw/completions', 60, 'images', 'GPT-4o 图像生成模型', '["text2image", "variants", "reference", "image_upload"]'),
('flux-pro-1.1', 'Flux Pro 1.1', 'image', 'grsai', '/v1/draw/flux', 40, 'images', 'Flux 专业版图像生成', '["text2image", "aspectRatio"]'),
('flux-pro-1.1-ultra', 'Flux Pro 1.1 Ultra', 'image', 'grsai', '/v1/draw/flux', 80, 'images', 'Flux 超级版图像生成', '["text2image", "aspectRatio"]'),
('flux-kontext-pro', 'Flux Kontext Pro', 'image', 'grsai', '/v1/draw/flux', 45, 'images', 'Flux 上下文专业版', '["text2image", "aspectRatio", "reference", "image_upload"]'),
('flux-kontext-max', 'Flux Kontext Max', 'image', 'grsai', '/v1/draw/flux', 70, 'images', 'Flux 上下文最大版', '["text2image", "aspectRatio", "reference", "image_upload"]'),

-- 视频生成模型
('veo3-fast', 'Veo3 Fast', 'video', 'grsai', '/v1/video/veo', 200, 'videos', 'Veo3 快速视频生成', '["text2video", "firstFrame", "image_upload"]'),
('veo3-pro', 'Veo3 Pro', 'video', 'grsai', '/v1/video/veo', 400, 'videos', 'Veo3 专业视频生成', '["text2video", "firstFrame", "image_upload"]');

-- 创建视图：用户积分使用统计
CREATE OR REPLACE VIEW user_credits_usage_stats AS
SELECT 
    u.user_uuid,
    u.model_id,
    am.model_name,
    am.model_type,
    COUNT(*) as usage_count,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    MAX(u.created_at) as last_used_at
FROM ai_model_usage u
JOIN ai_models am ON u.model_id = am.model_id
GROUP BY u.user_uuid, u.model_id, am.model_name, am.model_type;

-- 创建视图：模型使用统计
CREATE OR REPLACE VIEW model_usage_stats AS
SELECT 
    am.model_id,
    am.model_name,
    am.model_type,
    am.provider,
    COUNT(u.id) as total_usage_count,
    COUNT(DISTINCT u.user_uuid) as unique_users,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    ROUND(COUNT(CASE WHEN u.status = 'success' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM ai_models am
LEFT JOIN ai_model_usage u ON am.model_id = u.model_id
WHERE am.is_active = true
GROUP BY am.model_id, am.model_name, am.model_type, am.provider
ORDER BY total_usage_count DESC;

-- 创建函数：计算模型使用成本
CREATE OR REPLACE FUNCTION calculate_model_cost(
    p_model_id VARCHAR(100),
    p_input_size INT,
    p_output_size INT DEFAULT NULL
) RETURNS INT AS $$
DECLARE
    model_config RECORD;
    total_cost INT;
BEGIN
    -- 获取模型配置
    SELECT credits_per_unit, unit_type INTO model_config
    FROM ai_models 
    WHERE model_id = p_model_id AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Model % not found or inactive', p_model_id;
    END IF;
    
    -- 根据单位类型计算成本
    CASE model_config.unit_type
        WHEN 'tokens' THEN
            -- 按 1000 tokens 计费
            total_cost := CEIL((COALESCE(p_input_size, 0) + COALESCE(p_output_size, 0)) / 1000.0) * model_config.credits_per_unit;
        WHEN 'images' THEN
            -- 按图片数量计费
            total_cost := model_config.credits_per_unit;
        WHEN 'videos' THEN
            -- 按视频数量计费
            total_cost := model_config.credits_per_unit;
        ELSE
            total_cost := model_config.credits_per_unit;
    END CASE;
    
    RETURN GREATEST(total_cost, 1); -- 最少消耗1积分
END;
$$ LANGUAGE plpgsql;

-- 创建触发器函数：自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 ai_models 表创建触发器
CREATE TRIGGER update_ai_models_updated_at
    BEFORE UPDATE ON ai_models
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
