# 全局模态框系统操作指南

## 添加新模态框

### 1. 更新类型定义 (`stores/modalStore.ts`)

```typescript
// 添加新的模态框类型
export type ModalType = 'login' | 'pricing' | 'feedback' | 'newModal';

// 定义新模态框的 props 类型
interface ModalProps {
  // ... 现有类型
  newModal: {
    title?: string;
    data?: any;
  };
}
```

### 2. 创建模态框组件 (`components/modals/NewModal.tsx`)

```typescript
'use client';

import { useModalStore } from '@/stores/modalStore';
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface NewModalProps {
  title?: string;
  data?: any;
}

export default function NewModal({ title, data }: NewModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);

  return (
    <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent>
        {/* 模态框内容 */}
      </DialogContent>
    </Dialog>
  );
}
```

### 3. 注册到管理器 (`components/modals/ModalManager.tsx`)

```typescript
const modalComponents: Record<ModalType, React.ComponentType<any>> = {
  // ... 现有组件
  newModal: dynamic(() => import('./NewModal')),
};
```

## 触发模态框

### 基础用法

```typescript
import { useModalStore } from '@/stores/modalStore';

const openModal = useModalStore((state) => state.openModal);

// 打开模态框
openModal('newModal', { title: '标题', data: {} });
```

### 使用便捷 Hook

```typescript
import { useModal } from '@/hooks/useModal';

const { openModal, closeModal } = useModal();

// 打开模态框
openModal('newModal', { title: '标题' });

// 关闭模态框
closeModal();
```

### 添加便捷方法到 Hook (`hooks/useModal.ts`)

```typescript
export function useModal() {
  // ... 现有代码
  
  return {
    // ... 现有方法
    
    // 新的便捷方法
    openNewModal: (title?: string, data?: any) => 
      openModal('newModal', { title, data }),
  };
}
```

## 常用模式

### 响应式模态框（桌面/移动端）

```typescript
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerContent } from "@/components/ui/drawer";

export default function ResponsiveModal() {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  
  if (isDesktop) {
    return <Dialog>...</Dialog>;
  }
  
  return <Drawer>...</Drawer>;
}
```

### 带加载状态的模态框

```typescript
const modalComponents = {
  newModal: dynamic(() => import('./NewModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
};
```

## 兼容性迁移

### 替换原有的状态管理

```typescript
// 旧方式
const { showSignModal, setShowSignModal } = useAppContext();

// 新方式
import { useSignModal } from '@/hooks/useModal';
const { showSignModal, setShowSignModal } = useSignModal();
```

### 批量替换组件中的调用

```bash
# 查找使用旧方式的文件
grep -r "setShowSignModal" components/
grep -r "showSignModal" components/

# 替换为新的 hook
```

## 最佳实践

1. **类型安全**: 始终为新模态框定义 props 类型
2. **懒加载**: 使用 `dynamic` 导入优化性能
3. **响应式**: 考虑桌面和移动端的不同体验
4. **关闭处理**: 确保正确处理模态框关闭事件
5. **错误边界**: 为复杂模态框添加错误处理

## 调试技巧

```typescript
// 查看当前激活的模态框
const activeModal = useModalStore((state) => state.activeModal);
console.log('当前模态框:', activeModal);

// 检查模态框是否打开
const isOpen = useModalStore((state) => state.isModalOpen('login'));
console.log('登录模态框是否打开:', isOpen);
```
