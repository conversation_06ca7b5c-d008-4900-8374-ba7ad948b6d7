# 动态配置组件迁移指南

## 📋 概述

本指南帮助你从原有的 `OptionsConfig` 组件迁移到新的 `DynamicOptionsConfig` 组件。

## 🔄 迁移步骤

### 1. 测试新组件

首先访问测试页面验证新组件是否正常工作：
```
http://localhost:3000/test-dynamic-config
```

在测试页面中：
- 切换不同模型类型
- 对比新旧组件的差异
- 验证参数配置是否正确

### 2. 逐步替换组件

#### 方法A：使用开关参数（推荐）
```tsx
// 在使用 InputMain 的地方添加 useDynamicConfig 参数
<InputMain
  modelType="image"
  onResultChange={handleResult}
  onGeneratingChange={handleGenerating}
  useDynamicConfig={true}  // 启用新组件
/>
```

#### 方法B：直接替换
```tsx
// 原来的用法
import { OptionsConfig } from "./components/input/OptionsConfig";

<OptionsConfig
  selectedModel={selectedModel}
  options={options}
  onOptionsChange={setOptions}
/>

// 新的用法
import { DynamicOptionsConfig } from "./components/input/DynamicOptionsConfig";

<DynamicOptionsConfig
  selectedModel={selectedModel}
  options={options}
  onOptionsChange={setOptions}
/>
```

### 3. 验证功能

迁移后需要验证以下功能：

#### 基础功能
- [ ] 模型选择正常
- [ ] 参数显示正确
- [ ] 参数值变化正常
- [ ] 默认值加载正确

#### 高级功能
- [ ] 参数分组显示
- [ ] 条件参数显示/隐藏
- [ ] 参数验证工作
- [ ] 错误提示正确

#### 响应式设计
- [ ] 桌面端显示正常
- [ ] 平板端显示正常
- [ ] 手机端显示正常

## 🐛 常见问题

### Q1: 某个模型的参数没有显示
**A:** 检查该模型是否在配置文件中定义。运行以下代码查看支持的模型：
```tsx
import { getAllSupportedModelIds } from '@/services/provider/parameter-manager';
console.log(getAllSupportedModelIds());
```

### Q2: 参数验证不工作
**A:** 确保模型配置中的参数定义正确，特别是 `type`、`min`、`max` 等字段。

### Q3: 条件参数不显示
**A:** 检查参数配置中的 `condition` 字段是否正确设置。

### Q4: 样式显示异常
**A:** 确保已导入必要的 UI 组件样式，特别是 `Tooltip`、`Switch` 等组件。

## 📊 新旧组件对比

| 特性 | 原 OptionsConfig | 新 DynamicOptionsConfig |
|------|-----------------|------------------------|
| 代码维护 | 610行，难维护 | 150行，易维护 |
| 参数配置 | 硬编码在组件中 | 配置文件驱动 |
| 新增模型 | 需修改组件代码 | 只需添加配置文件 |
| 参数验证 | 基础验证 | 完整验证系统 |
| 参数分组 | 无 | 三级分组系统 |
| 条件显示 | 部分支持 | 完全支持 |
| 响应式 | 部分支持 | 完全响应式 |
| 调试支持 | 无 | 开发环境调试信息 |

## 🔧 配置文件说明

### 添加新模型配置

1. 在对应的配置文件中添加模型定义：
```typescript
// services/provider/grsai/parameters/image-models.ts
{
  modelId: 'new-model-id',
  version: '1.0',
  provider: 'grsai',
  modelType: 'image',
  parameters: [
    // 参数定义
  ],
  parameterGroups: {
    basic: ['param1', 'param2'],
    advanced: ['param3'],
    expert: []
  }
}
```

2. 确保在 `services/provider/{provider}/parameters/index.ts` 中导入新配置。

### 参数类型说明

- `number`: 数字输入框，支持 min/max/step
- `select`: 下拉选择，需要 options 数组
- `boolean`: 开关组件
- `range`: 滑块组件，支持 min/max/step
- `string`: 文本输入框
- `file`: 文件输入（暂时使用文本输入）

## 🚀 迁移完成后

1. **删除旧组件**：确认新组件工作正常后，可以删除 `OptionsConfig.tsx`
2. **更新导入**：移除对旧组件的导入
3. **清理代码**：移除 `useDynamicConfig` 参数和相关条件判断
4. **更新文档**：更新相关文档和注释

## 📞 支持

如果在迁移过程中遇到问题：
1. 查看测试页面的调试信息
2. 检查浏览器控制台的错误信息
3. 参考配置文件示例
4. 查看现有模型的配置实现
