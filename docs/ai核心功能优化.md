# AI核心功能优化方案

## 📊 当前架构分析

### 发现的问题：
1. **Provider切换机制不够灵活** - 目前只能通过数据库provider字段切换，缺少运行时故障转移
2. **参数配置按provider统一** - 确实有问题，同provider的不同模型参数差异很大
3. **OptionsConfig.tsx过于冗长** - 610行代码，硬编码参数配置，维护困难
4. **缺少表单验证和状态管理** - 没有使用现代表单库，组件联动有bug

## 🎯 优化方案建议

### 1. Provider故障转移机制

**推荐方案：数据库 + 环境变量混合模式**

```typescript
// config/provider-fallback.ts
interface ProviderConfig {
  primary: string;
  fallbacks: string[];
  healthCheckUrl?: string;
  timeout: number;
}

const PROVIDER_FALLBACK_CONFIG: Record<string, ProviderConfig> = {
  'flux-dev': {
    primary: 'grsai',
    fallbacks: ['replicate'],
    timeout: 30000
  },
  'gemini-2.5-pro': {
    primary: 'grsai',
    fallbacks: ['openai'],
    timeout: 60000
  }
}
```

**实现方式：**
- 数据库增加 `provider_config` JSONB字段存储多provider配置
- 环境变量控制全局provider优先级：`PROVIDER_PRIORITY=grsai,replicate,openai`
- 自动故障转移：primary失败时自动切换到fallback

### 2. 模型参数配置系统

**推荐方案：本地配置文件 + 数据库基础信息**

#### 数据库Schema扩展
```sql
-- 扩展ai_models表
ALTER TABLE ai_models ADD COLUMN provider_config JSONB;
ALTER TABLE ai_models ADD COLUMN parameter_schema_version VARCHAR(10) DEFAULT '1.0';

-- 示例数据
UPDATE ai_models SET provider_config = '{
  "primary": "grsai",
  "fallbacks": ["replicate"],
  "health_check": true,
  "timeout": 30000
}' WHERE model_id = 'flux-dev';
```

#### 配置文件结构设计
```typescript
// config/parameter-types.ts
export interface ParameterConfig {
  name: string;
  type: 'number' | 'string' | 'select' | 'boolean' | 'range' | 'file';
  required: boolean;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{value: string, label: string, description?: string}>;
  description: string;
  tooltip?: string;
  group: 'basic' | 'advanced' | 'expert';
  dependsOn?: string; // 依赖其他参数
  condition?: {
    field: string;
    value: any;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in';
  };
}

export interface ModelParameterConfig {
  modelId: string;
  version: string;
  provider: string;
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}
```

### 3. 通用化参数组件

**创建动态参数系统：**

```typescript
// components/ai-dashboard/components/input/ParameterInput.tsx
interface ParameterInputProps {
  config: ParameterConfig;
  value: any;
  onChange: (value: any) => void;
  error?: string;
}

// 根据参数类型自动选择合适的输入组件
export function ParameterInput({ config, value, onChange, error }: ParameterInputProps) {
  switch (config.type) {
    case 'select':
      return <SelectInput config={config} value={value} onChange={onChange} />;
    case 'number':
      return <NumberInput config={config} value={value} onChange={onChange} />;
    case 'boolean':
      return <BooleanInput config={config} value={value} onChange={onChange} />;
    default:
      return <TextInput config={config} value={value} onChange={onChange} />;
  }
}
```

### 4. Zod表单验证集成

**动态生成验证Schema：**

```typescript
// schemas/model-parameters.ts
import { z } from 'zod';

export function createModelSchema(modelConfig: ModelParameterConfig) {
  const schemaFields: Record<string, z.ZodTypeAny> = {};
  
  modelConfig.parameters.forEach(param => {
    let field: z.ZodTypeAny;
    
    switch (param.type) {
      case 'number':
        field = z.number().min(param.min || 0).max(param.max || 100);
        break;
      case 'select':
        field = z.enum(param.options as [string, ...string[]]);
        break;
      case 'boolean':
        field = z.boolean();
        break;
      default:
        field = z.string();
    }
    
    if (!param.required) {
      field = field.optional();
    }
    
    schemaFields[param.name] = field;
  });
  
  return z.object(schemaFields);
}
```

## 🚀 实施建议

### 优先级排序：
1. **高优先级**：模型参数配置系统（解决核心问题）
2. **中优先级**：通用参数组件（减少代码冗余）
3. **中优先级**：Provider故障转移（提高可靠性）
4. **低优先级**：Zod表单验证（改善开发体验）
5. **后续计划**：多语言支持（在基础功能稳定后实施）

### 推荐实施步骤：

#### 第一阶段：基础功能（单语言版本）
1. 创建 `config/model-parameters/` 目录和配置文件（单语言）
2. 实现 `ParameterInput` 通用组件
3. 创建 `DynamicOptionsConfig` 替代现有组件
4. 逐步迁移现有模型配置

#### 第二阶段：增强功能
5. 添加Provider故障转移机制
6. 集成Zod验证和react-hook-form

#### 第三阶段：多语言支持
7. 将配置文件改为多语言对象结构
8. 实现本地化工具函数
9. 更新组件支持多语言

### 解决结果组件联动bug：
```typescript
// 在handleGenerate中立即更新状态
const handleGenerate = async () => {
  setLoading(true);
  onGeneratingChange?.(true); // 立即通知父组件开始生成
  
  try {
    const result = await generateAI(request);
    onResultChange?.(result);
  } finally {
    setLoading(false);
    onGeneratingChange?.(false);
  }
};
```

## 📁 推荐文件结构

```
config/
  ├── model-parameters/
  │   ├── grsai-models.json
  │   ├── replicate-models.json
  │   └── index.ts
  ├── provider-fallback.ts
  └── parameter-types.ts

components/ai-dashboard/components/input/
  ├── ParameterInput.tsx (通用参数组件)
  ├── ParameterGroup.tsx (参数组分组)
  ├── DynamicOptionsConfig.tsx (新的动态配置组件)
  └── OptionsConfig.tsx (保留，逐步迁移)

services/
  ├── provider-manager.ts (Provider管理和故障转移)
  └── parameter-validator.ts (参数验证)

schemas/
  └── model-parameters.ts (Zod验证schemas)
```

## 🌍 多语言配置解决方案

### 配置文件多语言结构
```json
// config/model-parameters/replicate/flux-dev.json
{
  "modelId": "flux-dev",
  "version": "1.0",
  "provider": "replicate",
  "parameters": [
    {
      "name": "aspect_ratio",
      "type": "select",
      "required": false,
      "default": "1:1",
      "options": [
        {
          "value": "1:1",
          "label": {
            "en": "Square",
            "zh": "正方形",
            "ja": "正方形",
            "ko": "정사각형"
          },
          "description": {
            "en": "Perfect for social media posts",
            "zh": "适合社交媒体",
            "ja": "ソーシャルメディアに最適",
            "ko": "소셜 미디어에 적합"
          }
        }
      ],
      "description": {
        "en": "Image aspect ratio",
        "zh": "图片宽高比",
        "ja": "画像のアスペクト比",
        "ko": "이미지 종횡비"
      },
      "tooltip": {
        "en": "Choose the aspect ratio for your generated image",
        "zh": "选择生成图片的宽高比",
        "ja": "生成される画像のアスペクト比を選択",
        "ko": "생성할 이미지의 종횡비를 선택하세요"
      },
      "group": "basic"
    }
  ]
}
```

### 多语言工具函数
```typescript
// lib/parameter-i18n.ts
export function getLocalizedParameterConfig(
  config: ParameterConfig,
  locale: string = 'en'
): ParameterConfig {
  return {
    ...config,
    description: getLocalizedText(config.description, locale),
    tooltip: config.tooltip ? getLocalizedText(config.tooltip, locale) : undefined,
    options: config.options?.map(option => ({
      ...option,
      label: getLocalizedText(option.label, locale),
      description: option.description ? getLocalizedText(option.description, locale) : undefined
    }))
  };
}

function getLocalizedText(
  text: string | Record<string, string>,
  locale: string
): string {
  if (typeof text === 'string') return text;
  return text[locale] || text['en'] || Object.values(text)[0] || '';
}
```

## ⚡ Zod Schema优点

### 1. 类型安全 + 运行时验证
```typescript
const schema = z.object({
  guidance: z.number().min(0).max(10),
  aspect_ratio: z.enum(['1:1', '16:9', '9:16'])
});

type FormData = z.infer<typeof schema>; // 自动推导类型
```

### 2. 丰富验证规则 + 自动错误消息
```typescript
const schema = z.object({
  guidance: z.number({
    required_error: "引导强度是必填项",
    invalid_type_error: "引导强度必须是数字"
  }).min(0, "不能小于0").max(10, "不能大于10"),

  uploadedImages: z.array(z.string().url()).max(5, "最多上传5张图片")
});
```

### 3. 数据转换 + 条件验证
```typescript
const schema = z.object({
  guidance: z.coerce.number(), // 字符串自动转数字
  hasImage: z.boolean(),
  prompt_strength: z.number().optional()
}).refine(data => {
  // 条件验证：有图片时必须设置强度
  if (data.hasImage && !data.prompt_strength) return false;
  return true;
}, { message: "上传图片时必须设置提示词强度" });
```

## ✅ 实施进度

### 第一阶段：基础功能（单语言版本）
- [x] **创建模型参数配置文件系统** (2025-01-03)
  - ✅ 创建参数类型定义 (`config/parameter-types.ts`)
  - ✅ 创建配置管理器 (`config/model-parameters/index.ts`)
  - ✅ 创建GRSAI文本模型配置 (`config/model-parameters/grsai/text-models.ts`)
  - ✅ 创建GRSAI图像模型配置 (`config/model-parameters/grsai/image-models.ts`)
  - ✅ 创建GRSAI视频模型配置 (`config/model-parameters/grsai/video-models.ts`)
  - ✅ 创建GRSAI多模态模型配置 (`config/model-parameters/grsai/multimodal-models.ts`)
  - ✅ 创建Replicate图像模型配置 (`config/model-parameters/replicate/image-models.ts`)
  - ✅ 创建参数加载工具 (`lib/model-parameter-loader.ts`)
  - ✅ 创建测试配置文件 (`config/model-parameters/test-config.ts`)
  - ✅ 创建API端点 (`app/api/ai/model-parameters/route.ts`)
- [x] **实现通用参数组件** (2025-01-03)
  - ✅ 创建NumberInput组件 (`components/ai-dashboard/components/input/parameter-inputs/NumberInput.tsx`)
  - ✅ 创建SelectInput组件 (`components/ai-dashboard/components/input/parameter-inputs/SelectInput.tsx`)
  - ✅ 创建BooleanInput组件 (`components/ai-dashboard/components/input/parameter-inputs/BooleanInput.tsx`)
  - ✅ 创建RangeInput组件 (`components/ai-dashboard/components/input/parameter-inputs/RangeInput.tsx`)
  - ✅ 创建TextInput组件 (`components/ai-dashboard/components/input/parameter-inputs/TextInput.tsx`)
  - ✅ 创建ImageUploadInput组件 (`components/ai-dashboard/components/input/parameter-inputs/ImageUploadInput.tsx`)
  - ✅ 创建ParameterInput主组件 (`components/ai-dashboard/components/input/parameter-inputs/ParameterInput.tsx`)
  - ✅ 创建ParameterGroup组件 (`components/ai-dashboard/components/input/parameter-inputs/ParameterGroup.tsx`)
  - ✅ 创建组件索引文件 (`components/ai-dashboard/components/input/parameter-inputs/index.ts`)
- [x] **创建动态配置组件** (2025-01-03)
  - ✅ 创建DynamicOptionsConfig组件 (`components/ai-dashboard/components/input/DynamicOptionsConfig.tsx`)
- [x] **迁移现有模型配置** (2025-01-03)
  - ✅ 修改InputMain组件支持新配置组件 (`components/ai-dashboard/input-main.tsx`)
  - ✅ 添加useDynamicConfig开关参数，支持逐步迁移
  - ✅ 创建测试页面 (`app/test-dynamic-config/page.tsx`)
  - ✅ 创建迁移指南 (`docs/migration-guide.md`)
  - ✅ 保持向后兼容，新旧组件可并存

### 已完成的功能特性
- ✅ **模型参数配置系统**：支持15个AI模型的参数配置（文本4个、图像7个、视频2个、多模态2个）
- ✅ **复杂参数类型**：number、string、select、boolean、range、file
- ✅ **参数分组系统**：basic、advanced、expert三级分组，支持折叠展开
- ✅ **条件依赖逻辑**：参数可基于其他参数值显示/隐藏
- ✅ **参数验证系统**：类型检查、范围检查、必填检查、实时验证
- ✅ **默认值管理**：自动加载和合并默认值
- ✅ **API接口**：完整的REST API用于获取配置和验证参数
- ✅ **通用参数组件**：6种输入组件类型（包括图片上传），支持响应式设计
- ✅ **动态配置组件**：基于配置文件自动渲染参数表单
- ✅ **开发调试支持**：开发环境下显示调试信息
- ✅ **迁移支持**：新旧组件可并存，支持逐步迁移
- ✅ **测试页面**：完整的测试页面验证功能
- ✅ **迁移指南**：详细的迁移步骤和问题解决方案
- ✅ **图片上传功能**：完整恢复图片上传功能，支持拖拽和点击上传
- ✅ **模型图片上传支持**：为8个GRSAI模型添加image_upload功能（4个图像、2个视频、2个多模态）

## 🔄 版本更新记录

- v1.0 (2025-01-03): 初始方案设计
- v1.1 (2025-01-03): 增加多语言配置方案和Zod优点说明
- v1.2 (2025-01-03): 调整实施优先级，先实现基础功能（单语言），后续再加多语言支持
- v1.3 (2025-01-03): 完成模型参数配置文件系统，支持15个AI模型的完整参数配置
- v1.4 (2025-01-03): 完成通用参数组件和动态配置组件，实现基于配置文件的自动表单渲染
- v1.5 (2025-01-03): 完成迁移支持，新旧组件可并存，提供测试页面和迁移指南
- v1.6 (2025-01-03): 恢复图片上传功能，创建ImageUploadInput组件，完整支持拖拽和点击上传
- v1.7 (2025-01-03): 为GRSAI模型添加图片上传支持，更新数据库和setup.sql初始化配置
- v1.8 (2025-01-03): 修复配置文件中缺失的uploadedImages参数，为所有支持图片上传的模型添加参数配置
