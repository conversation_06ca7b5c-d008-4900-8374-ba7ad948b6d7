(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2958],{8202:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(29207),r=t(27487),n=t(33335);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...r})});i.displayName="Textarea"},12222:(e,s,t)=>{Promise.resolve().then(t.bind(t,36187))},16692:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(29207);t(27487);var r=t(33335);function n(e){let{className:s,type:t,...n}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},33335:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var a=t(46586),r=t(71427);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},36187:(e,s,t)=>{"use strict";t.d(s,{default:()=>b});var a=t(29207),r=t(27487),n=t(78186),i=t(70616),l=t(16692),d=t(92402),c=t(8202),o=t(54080),u=t(59548),m=t(83978),x=t(92105),h=t(78939),v=t(18821),g=t(98207),f=t(98656),p=t(39792);function b(){let[e,s]=(0,r.useState)("你好，这是火山引擎语音合成测试。"),[t,b]=(0,r.useState)("BV700_streaming"),[j,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[_,k]=(0,r.useState)("这是一段较长的文本，用于测试火山引擎的异步语音合成功能。异步合成适合处理长文本内容，可以生成高质量的音频文件。"),[S,C]=(0,r.useState)("BV701_streaming"),[A,F]=(0,r.useState)(!1),[z,T]=(0,r.useState)(null),[B,V]=(0,r.useState)(""),[E,R]=(0,r.useState)(!1),[J,D]=(0,r.useState)(""),[L,q]=(0,r.useState)("fast"),[Z,O]=(0,r.useState)("zh"),[U,$]=(0,r.useState)(!1),[I,P]=(0,r.useState)(null),[X,W]=(0,r.useState)(""),[M,Q]=(0,r.useState)(!1),[G,H]=(0,r.useState)(""),[K,Y]=(0,r.useState)(null),[ee,es]=(0,r.useState)(""),[et,ea]=(0,r.useState)("1"),[er,en]=(0,r.useState)(!1),[ei,el]=(0,r.useState)(null),[ed,ec]=(0,r.useState)(""),eo=async()=>{if(!e.trim())return void ec("请输入要合成的文本");y(!0),ec("");try{let s=await fetch("/api/volcengine/tts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,voice_type:t,options:{encoding:"mp3",rate:24e3}})}),a=await s.json();0===a.code?w(a.data):ec(a.message||"语音合成失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{y(!1)}},eu=async()=>{if(!_.trim())return void ec("请输入要合成的文本");F(!0),ec("");try{let e=await fetch("/api/volcengine/tts-async/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:_,voice_type:S,options:{format:"mp3",sample_rate:24e3}})}),s=await e.json();0===s.code?(V(s.data.task_id),T({...s.data,status:"submitted"}),ex(s.data.task_id)):ec(s.message||"异步任务提交失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{F(!1)}},em=async()=>{if(!B)return void ec("请先提交异步任务");F(!0),ec("");try{let e=await fetch("/api/volcengine/tts-async/query?task_id=".concat(B)),s=await e.json();0===s.code?T(s.data):ec(s.message||"查询失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{F(!1)}},ex=async e=>{R(!0);let s=0,t=async()=>{if(s>=60){R(!1),ec("任务超时，请手动查询结果");return}try{let a=await fetch("/api/volcengine/tts-async/query?task_id=".concat(e)),r=await a.json();if(0===r.code&&(T(r.data),"success"===r.data.status||"failed"===r.data.status))return void R(!1);s++,setTimeout(t,5e3)}catch(e){console.error("轮询错误:",e),s++,setTimeout(t,5e3)}};t()},eh=async()=>{if(!J.trim())return void ec("请输入音频文件URL");$(!0),ec("");try{let e=await fetch("/api/volcengine/asr/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audio_url:J,model_type:L,language:Z})}),s=await e.json();0===s.code?(W(s.data.task_id),P({...s.data,status:"submitted"}),eg(s.data.task_id,L)):ec(s.message||"ASR任务提交失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{$(!1)}},ev=async()=>{if(!X)return void ec("请先提交ASR任务");$(!0),ec("");try{let e=await fetch("/api/volcengine/asr/query?task_id=".concat(X,"&model_type=").concat(L)),s=await e.json();0===s.code?P(s.data):ec(s.message||"查询失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{$(!1)}},eg=async(e,s)=>{Q(!0);let t=0,a=async()=>{if(t>=120){Q(!1),ec("任务超时，请手动查询结果");return}try{let r=await fetch("/api/volcengine/asr/query?task_id=".concat(e,"&model_type=").concat(s)),n=await r.json();if(0===n.code&&(P(n.data),"success"===n.data.status||"failed"===n.data.status))return void Q(!1);t++,setTimeout(a,5e3)}catch(e){console.error("轮询错误:",e),t++,setTimeout(a,5e3)}};a()},ef=async()=>{if(!G.trim()||!K)return void ec("请输入Speaker ID并选择音频文件");en(!0),ec("");try{let e=new FormData;e.append("audio",K),e.append("speaker_id",G),e.append("reference_text",ee),e.append("language","0"),e.append("model_type",et);let s=await fetch("/api/volcengine/voice-clone/upload",{method:"POST",body:e}),t=await s.json();0===t.code?el(t.data):ec(t.message||"声音复刻上传失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{en(!1)}},ep=async()=>{if(!G.trim())return void ec("请输入Speaker ID");en(!0),ec("");try{let e=await fetch("/api/volcengine/voice-clone/status?speaker_id=".concat(G)),s=await e.json();0===s.code?el(s.data):ec(s.message||"状态查询失败")}catch(e){ec("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{en(!1)}},eb=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"mp3";try{let t=new Blob([Uint8Array.from(atob(e),e=>e.charCodeAt(0))],{type:"audio/".concat(s)}),a=URL.createObjectURL(t);new Audio(a).play()}catch(e){ec("音频播放失败")}};return(0,a.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"火山引擎语音服务测试"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"测试火山引擎豆包语音服务的各项功能"})]}),ed&&(0,a.jsx)(m.Fc,{className:"mb-6 border-red-200 bg-red-50",children:(0,a.jsx)(m.TN,{className:"text-red-800",children:ed})}),(0,a.jsxs)(u.tU,{defaultValue:"tts",className:"space-y-6",children:[(0,a.jsxs)(u.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(u.Xi,{value:"tts",className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),"短文本合成"]}),(0,a.jsxs)(u.Xi,{value:"async-tts",className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),"长文本合成"]}),(0,a.jsxs)(u.Xi,{value:"asr",className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),"语音识别"]}),(0,a.jsxs)(u.Xi,{value:"voice-clone",className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),"声音复刻"]})]}),(0,a.jsx)(u.av,{value:"tts",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"短文本语音合成"}),(0,a.jsx)(n.BT,{children:"实时语音合成，适合短文本（最大1024字节）"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"tts-text",children:"合成文本"}),(0,a.jsx)(c.T,{id:"tts-text",value:e,onChange:e=>s(e.target.value),placeholder:"输入要合成的文本...",className:"mt-1",maxLength:1024}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[e.length,"/1024 字符"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"tts-voice",children:"音色选择"}),(0,a.jsxs)(o.l6,{value:t,onValueChange:b,children:[(0,a.jsx)(o.bq,{className:"mt-1",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"BV700_streaming",children:"灿灿 (支持情感)"}),(0,a.jsx)(o.eb,{value:"BV701_streaming",children:"擎苍 (有声阅读)"}),(0,a.jsx)(o.eb,{value:"BV001_streaming",children:"通用女声"}),(0,a.jsx)(o.eb,{value:"BV002_streaming",children:"通用男声"}),(0,a.jsx)(o.eb,{value:"BV421_streaming",children:"天才少女 (多语种)"}),(0,a.jsx)(o.eb,{value:"BV503_streaming",children:"Ariana (英语)"})]})]})]}),(0,a.jsx)(i.$,{onClick:eo,disabled:j||!e.trim(),className:"w-full",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"合成中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"开始合成"]})}),N&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"合成成功"}),(0,a.jsxs)(x.E,{variant:"secondary",children:["时长: ",N.duration,"ms"]})]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 mb-3",children:["请求ID: ",N.reqid]}),(0,a.jsxs)(i.$,{onClick:()=>eb(N.audio_data,N.format),size:"sm",variant:"outline",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"播放音频"]})]})]})]})}),(0,a.jsx)(u.av,{value:"async-tts",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"长文本异步合成"}),(0,a.jsx)(n.BT,{children:"异步处理长文本（最大10万字符），支持回调和轮询"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"async-tts-text",children:"合成文本"}),(0,a.jsx)(c.T,{id:"async-tts-text",value:_,onChange:e=>k(e.target.value),placeholder:"输入要合成的长文本...",className:"mt-1 min-h-[120px]",maxLength:1e5}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[_.length,"/100,000 字符"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"async-tts-voice",children:"音色选择"}),(0,a.jsxs)(o.l6,{value:S,onValueChange:C,children:[(0,a.jsx)(o.bq,{className:"mt-1",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"BV701_streaming",children:"擎苍 (推荐长文本)"}),(0,a.jsx)(o.eb,{value:"BV700_streaming",children:"灿灿"}),(0,a.jsx)(o.eb,{value:"BV001_streaming",children:"通用女声"}),(0,a.jsx)(o.eb,{value:"BV002_streaming",children:"通用男声"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:eu,disabled:A||E||!_.trim(),className:"flex-1",children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"提交中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"提交任务"]})}),(0,a.jsx)(i.$,{onClick:em,disabled:A||E||!B,variant:"outline",className:"flex-1",children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"轮询中..."]}):"手动查询"})]}),B&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["任务ID: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:B})]}),E&&(0,a.jsx)(x.E,{variant:"secondary",className:"animate-pulse",children:"自动轮询中..."})]})}),z&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"任务状态"}),(0,a.jsx)(x.E,{variant:"success"===z.status?"default":"secondary",children:z.status})]}),z.audio_url&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["音频文件: ",(0,a.jsx)("a",{href:z.audio_url,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"下载链接"})]}),z.subtitle_url&&(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["字幕文件: ",(0,a.jsx)("a",{href:z.subtitle_url,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"下载链接"})]}),(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["时长: ",z.duration,"ms | 文件大小: ",z.file_size," bytes"]})]})]})]})]})}),(0,a.jsx)(u.av,{value:"asr",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"语音识别"}),(0,a.jsx)(n.BT,{children:"支持多种语言和方言的语音转文字服务"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-url",children:"音频文件URL"}),(0,a.jsx)(l.p,{id:"asr-url",value:J,onChange:e=>D(e.target.value),placeholder:"输入公网可访问的音频文件URL...",className:"mt-1"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-model",children:"识别模型"}),(0,a.jsxs)(o.l6,{value:L,onValueChange:q,children:[(0,a.jsx)(o.bq,{className:"mt-1",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"bigmodel",children:"大模型识别 (高精度)"}),(0,a.jsx)(o.eb,{value:"standard",children:"标准版识别"}),(0,a.jsx)(o.eb,{value:"fast",children:"极速版识别"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-language",children:"语言"}),(0,a.jsxs)(o.l6,{value:Z,onValueChange:O,children:[(0,a.jsx)(o.bq,{className:"mt-1",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"zh",children:"中文普通话"}),(0,a.jsx)(o.eb,{value:"yue",children:"粤语"}),(0,a.jsx)(o.eb,{value:"en",children:"英语"}),(0,a.jsx)(o.eb,{value:"ja",children:"日语"}),(0,a.jsx)(o.eb,{value:"ko",children:"韩语"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:eh,disabled:U||M||!J.trim(),className:"flex-1",children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"提交中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"提交识别"]})}),(0,a.jsx)(i.$,{onClick:ev,disabled:U||M||!X,variant:"outline",className:"flex-1",children:M?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"轮询中..."]}):"手动查询"})]}),X&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["任务ID: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:X})]}),M&&(0,a.jsx)(x.E,{variant:"secondary",className:"animate-pulse",children:"自动轮询中..."})]})}),I&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"识别结果"}),(0,a.jsx)(x.E,{variant:"success"===I.status?"default":"secondary",children:I.status})]}),I.result&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-green-800 mb-1",children:"完整文本:"}),(0,a.jsx)("p",{className:"text-green-700 bg-white p-3 rounded border",children:I.result.text})]}),I.result.utterances&&I.result.utterances.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-green-800 mb-2",children:"分段结果:"}),(0,a.jsx)("div",{className:"space-y-2",children:I.result.utterances.map((e,s)=>(0,a.jsx)("div",{className:"bg-white p-2 rounded border text-sm",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,a.jsx)("span",{className:"text-green-700",children:e.text}),(0,a.jsxs)("span",{className:"text-green-600 text-xs",children:[e.start_time,"s - ",e.end_time,"s"]})]})},s))})]})]})]})]})]})}),(0,a.jsx)(u.av,{value:"voice-clone",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"声音复刻"}),(0,a.jsx)(n.BT,{children:"上传音频文件训练个性化音色"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-speaker-id",children:"Speaker ID"}),(0,a.jsx)(l.p,{id:"clone-speaker-id",value:G,onChange:e=>H(e.target.value),placeholder:"输入唯一的音色标识符...",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-file",children:"音频文件"}),(0,a.jsx)(l.p,{id:"clone-file",type:"file",accept:".wav,.mp3,.ogg,.m4a,.aac,.pcm",onChange:e=>{var s;return Y((null==(s=e.target.files)?void 0:s[0])||null)},className:"mt-1"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"支持格式: wav, mp3, ogg, m4a, aac, pcm (最大10MB)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-text",children:"参考文本 (可选)"}),(0,a.jsx)(c.T,{id:"clone-text",value:ee,onChange:e=>es(e.target.value),placeholder:"输入音频对应的文本内容，用于提高复刻质量...",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-model-type",children:"复刻版本"}),(0,a.jsxs)(o.l6,{value:et,onValueChange:ea,children:[(0,a.jsx)(o.bq,{className:"mt-1",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"0",children:"复刻1.0 (基础版)"}),(0,a.jsx)(o.eb,{value:"1",children:"复刻2.0 (增强版)"}),(0,a.jsx)(o.eb,{value:"2",children:"DiT标准版 (音色复刻)"}),(0,a.jsx)(o.eb,{value:"3",children:"DiT还原版 (音色+风格)"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:ef,disabled:er||!G.trim()||!K,className:"flex-1",children:er?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"上传中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"上传训练"]})}),(0,a.jsx)(i.$,{onClick:ep,disabled:er||!G.trim(),variant:"outline",className:"flex-1",children:"查询状态"})]}),ei&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"复刻状态"}),(0,a.jsx)(x.E,{variant:ei.ready_for_use?"default":"secondary",children:ei.status})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-green-700",children:[(0,a.jsxs)("p",{children:["Speaker ID: ",ei.speaker_id]}),ei.version&&(0,a.jsxs)("p",{children:["版本: ",ei.version]}),ei.create_time&&(0,a.jsxs)("p",{children:["创建时间: ",new Date(ei.create_time).toLocaleString()]}),ei.demo_audio&&(0,a.jsxs)("p",{children:["试听音频: ",(0,a.jsx)("a",{href:ei.demo_audio,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"播放"})]}),ei.ready_for_use&&(0,a.jsx)("p",{className:"text-green-800 font-medium",children:"✅ 可用于语音合成"})]})]})]})]})})]})]})}},54080:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>c,yv:()=>o});var a=t(29207);t(27487);var r=t(16833),n=t(22448),i=t(32554),l=t(81385),d=t(33335);function c(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...s})}function o(e){let{...s}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...s})}function u(e){let{className:s,size:t="default",children:i,...l}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...l,children:[i,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:s,children:t,position:n="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(v,{})]})})}function x(e){let{className:s,children:t,...n}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function v(e){let{className:s,...t}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},59548:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>i});var a=t(29207);t(27487);var r=t(25412),n=t(33335);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted/50 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-xl p-1 backdrop-blur-sm border border-border/30",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-md focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-9 flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-background/50 data-[state=active]:scale-[1.02] [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",s),...t})}},70616:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>l});var a=t(29207);t(27487);var r=t(46181),n=t(10060),i=t(33335);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:n,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:s})),...c})}},78186:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,X9:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var a=t(29207);t(27487);var r=t(33335);function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,r.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}function u(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t})}},83978:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>d,TN:()=>c});var a=t(29207),r=t(27487),n=t(10060),i=t(33335);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:r}),t),...n})});d.displayName="Alert",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...r})});c.displayName="AlertDescription"},92105:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(29207);t(27487);var r=t(46181),n=t(10060),i=t(33335);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:n=!1,...d}=e,c=n?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),s),...d})}},92402:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var a=t(29207);t(27487);var r=t(59538),n=t(33335);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2268,5892,6058,5562,3696,2717,8076,3102,7524,7358],()=>s(12222)),_N_E=e.O()}]);