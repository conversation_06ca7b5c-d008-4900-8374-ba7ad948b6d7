(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8272],{2641:(e,t,n)=>{"use strict";n.d(t,{GB:()=>o,IB:()=>r,L$:()=>i,b:()=>a,q:()=>s,u7:()=>l});let r=["en","zh"],i={en:"English",zh:"中文"},s="en",a="as-needed",o=!1,l={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},8357:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var r=n(29207),i=n(27487),s=n(89361),a=n(87853),o=n(25668);let l={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.RQr,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.LrS,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:a.sOK,GoThumbsup:o.VZG,GoArrowUpRight:o.zny},c=(0,i.memo)(e=>{let{name:t,className:n,onClick:i,...s}=e,a=l[t];return a?(0,r.jsx)(a,{className:n,onClick:i,style:{cursor:i?"pointer":"default"},...s}):null})},10060:(e,t,n)=>{"use strict";n.d(t,{F:()=>a});var r=n(46586);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return s(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let s=i(t)||i(r);return a[e][s]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return s(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...c}[t]):({...o,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},30173:(e,t,n)=>{Promise.resolve().then(n.bind(n,41013))},30763:()=>{},33335:(e,t,n)=>{"use strict";n.d(t,{cn:()=>s});var r=n(46586),i=n(71427);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.QP)((0,r.$)(t))}},41013:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u});var r=n(29207),i=n(47801),s=n(76501),a=n(92105),o=n(78186),l=n(89863),c=n(8357),d=n(27487);function u(){let[e,t]=(0,d.useState)("\n    <h2>Welcome to the Rich Text Editor</h2>\n    <p>This is a powerful rich text editor built with <strong>TipTap</strong>. You can:</p>\n    <ul>\n      <li>Format text with <strong>bold</strong>, <em>italic</em>, and <u>underline</u></li>\n      <li>Create lists and organize content</li>\n      <li>Add links and images</li>\n      <li>Use headings and paragraphs</li>\n    </ul>\n    <blockquote>\n      <p>The editor supports many advanced features for content creation.</p>\n    </blockquote>\n    <p>Try editing this content to see the editor in action!</p>\n  "),[n,u]=(0,d.useState)('# Markdown Editor Demo\n\nWelcome to the **Markdown Editor**! This editor provides a clean interface for writing in Markdown.\n\n## Features\n\n- **Live Preview**: See your formatted content as you type\n- **Syntax Highlighting**: Code blocks are highlighted for better readability\n- **Toolbar**: Quick access to common formatting options\n- **Split View**: Edit and preview side by side\n\n### Code Example\n\n```javascript\nfunction greet(name) {\n  return `Hello, ${name}!`;\n}\n\nconsole.log(greet("World"));\n```\n\n### Lists and Links\n\n1. **Ordered lists** work great\n2. You can also create [links](https://example.com)\n3. And add `inline code`\n\n- Unordered lists too\n- With multiple items\n- Easy to create\n\n> Blockquotes are perfect for highlighting important information or quotes.\n\n### Tables\n\n| Feature | Rich Text Editor | Markdown Editor |\n|---------|------------------|-----------------|\n| WYSIWYG | ✅ | ❌ |\n| Markdown | ❌ | ✅ |\n| Live Preview | ✅ | ✅ |\n| Code Highlighting | ❌ | ✅ |\n\nTry editing this content to explore the Markdown editor features!\n');return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(a.E,{variant:"outline",className:"mb-4",children:"Editor Components"}),(0,r.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Editor Components Demo"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Powerful editing experiences with rich text and Markdown editors for content creation."})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)(l.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,r.jsx)(c.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiEditLine",className:"w-5 h-5"}),"Rich Text Editor (TipTap)"]}),(0,r.jsx)(o.BT,{children:"WYSIWYG editor with formatting toolbar, perfect for creating rich content with visual feedback."})]})})}),(0,r.jsx)("div",{className:"border-y bg-muted/30",children:(0,r.jsx)("div",{className:"container mx-auto py-16",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Rich Text Editor Example"}),(0,r.jsx)(o.BT,{children:"Edit the content below to see the rich text editor in action"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(i.A,{value:e,onChange:t})})]})})})})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiMarkdownLine",className:"w-5 h-5"}),"Markdown Editor"]}),(0,r.jsx)(o.BT,{children:"Markdown editor with live preview, syntax highlighting, and split-view support."})]})})}),(0,r.jsx)("div",{className:"border-y",children:(0,r.jsx)("div",{className:"container mx-auto py-16",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Markdown Editor Example"}),(0,r.jsx)(o.BT,{children:"Write in Markdown and see the live preview on the right"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(s.A,{value:n,onChange:u})})]})})})})]}),(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiCompareLineIcon",className:"w-5 h-5"}),"Editor Features Comparison"]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiEditLine",className:"w-4 h-4"}),"Rich Text Editor (TipTap)"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,r.jsx)("li",{children:"WYSIWYG (What You See Is What You Get)"}),(0,r.jsx)("li",{children:"Visual formatting toolbar"}),(0,r.jsx)("li",{children:"Real-time formatting preview"}),(0,r.jsx)("li",{children:"Support for images, links, and media"}),(0,r.jsx)("li",{children:"Collaborative editing capabilities"}),(0,r.jsx)("li",{children:"Custom extensions and plugins"}),(0,r.jsx)("li",{children:"Accessibility features built-in"}),(0,r.jsx)("li",{children:"Mobile-friendly interface"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiMarkdownLine",className:"w-4 h-4"}),"Markdown Editor"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,r.jsx)("li",{children:"Markdown syntax support"}),(0,r.jsx)("li",{children:"Live preview with split view"}),(0,r.jsx)("li",{children:"Syntax highlighting for code blocks"}),(0,r.jsx)("li",{children:"Table editing support"}),(0,r.jsx)("li",{children:"Math equation rendering"}),(0,r.jsx)("li",{children:"Export to various formats"}),(0,r.jsx)("li",{children:"Keyboard shortcuts for efficiency"}),(0,r.jsx)("li",{children:"Version control friendly"})]})]})]})})]})})}),(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(c.default,{name:"RiCodeLine",className:"w-5 h-5"}),"Usage Examples"]})}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"Rich Text Editor Implementation:"}),(0,r.jsx)("code",{className:"text-sm bg-muted p-4 rounded block whitespace-pre",children:'import Editor from "@/components/blocks/editor";\n\nfunction MyComponent() {\n  const [content, setContent] = useState("");\n  \n  return (\n    <Editor\n      content={content}\n      onChange={setContent}\n      placeholder="Start writing..."\n    />\n  );\n}'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"Markdown Editor Implementation:"}),(0,r.jsx)("code",{className:"text-sm bg-muted p-4 rounded block whitespace-pre",children:'import MDEditor from "@/components/blocks/mdeditor";\n\nfunction MyComponent() {\n  const [markdown, setMarkdown] = useState("");\n  \n  return (\n    <MDEditor\n      value={markdown}\n      onChange={setMarkdown}\n      height={400}\n    />\n  );\n}'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"When to Use Each Editor:"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(o.Zp,{className:"p-4",children:[(0,r.jsx)("h5",{className:"font-medium mb-2",children:"Rich Text Editor"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Blog posts and articles"}),(0,r.jsx)("li",{children:"• Email composition"}),(0,r.jsx)("li",{children:"• Content management systems"}),(0,r.jsx)("li",{children:"• User-generated content"}),(0,r.jsx)("li",{children:"• Non-technical users"})]})]}),(0,r.jsxs)(o.Zp,{className:"p-4",children:[(0,r.jsx)("h5",{className:"font-medium mb-2",children:"Markdown Editor"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Documentation"}),(0,r.jsx)("li",{children:"• README files"}),(0,r.jsx)("li",{children:"• Technical writing"}),(0,r.jsx)("li",{children:"• Code comments"}),(0,r.jsx)("li",{children:"• Developer-focused content"})]})]})]})]})]})]})})})]})}},47801:(e,t,n)=>{"use strict";n.d(t,{A:()=>B});var r=n(29207);n(30763);var i=n(38254),s=n(44932),a=n(45496),o=n(14545),l=n(15695);n(27487);var c=n(84581),d=n(66482),u=n(85173),h=n(47507),m=n(71138),g=n(60458),x=n(44586),p=n(11727),b=n(58108),v=n(25681),f=n(83734),j=n(52603),y=n(99674),w=n(23690),N=n(96623),k=n(97958),A=n(64986),C=n(86648),E=n(44407),R=n(53209),L=n(93697);function O(){let{editor:e}=(0,o.aA)();return e?(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 p-2 border-b mb-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBold().run(),disabled:!e.can().chain().focus().toggleBold().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("bold")?"bg-gray-100":""),title:"Bold",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleItalic().run(),disabled:!e.can().chain().focus().toggleItalic().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("italic")?"bg-gray-100":""),title:"Italic",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleStrike().run(),disabled:!e.can().chain().focus().toggleStrike().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("strike")?"bg-gray-100":""),title:"Strike",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCode().run(),disabled:!e.can().chain().focus().toggleCode().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("code")?"bg-gray-100":""),title:"Code",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().unsetAllMarks().run(),className:"p-2 rounded hover:bg-gray-100",title:"Clear marks",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setParagraph().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("paragraph")?"bg-gray-100":""),title:"Paragraph",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:1})?"bg-gray-100":""),title:"Heading 1",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:2})?"bg-gray-100":""),title:"Heading 2",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:3})?"bg-gray-100":""),title:"Heading 3",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:4}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:4})?"bg-gray-100":""),title:"Heading 4",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:5}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:5})?"bg-gray-100":""),title:"Heading 5",children:(0,r.jsx)(f.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:6}).run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("heading",{level:6})?"bg-gray-100":""),title:"Heading 6",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBulletList().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("bulletList")?"bg-gray-100":""),title:"Bullet list",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleOrderedList().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("orderedList")?"bg-gray-100":""),title:"Ordered list",children:(0,r.jsx)(w.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCodeBlock().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("codeBlock")?"bg-gray-100":""),title:"Code block",children:(0,r.jsx)(N.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBlockquote().run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("blockquote")?"bg-gray-100":""),title:"Blockquote",children:(0,r.jsx)(k.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHorizontalRule().run(),className:"p-2 rounded hover:bg-gray-100",title:"Horizontal rule",children:(0,r.jsx)(A.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHardBreak().run(),className:"p-2 rounded hover:bg-gray-100",title:"Hard break",children:(0,r.jsx)(C.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().undo().run(),disabled:!e.can().chain().focus().undo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Undo",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().redo().run(),disabled:!e.can().chain().focus().redo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Redo",children:(0,r.jsx)(R.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setColor("#958DF1").run(),className:"p-2 rounded hover:bg-gray-100 ".concat(e.isActive("textStyle",{color:"#958DF1"})?"bg-gray-100":""),title:"Purple",children:(0,r.jsx)(L.A,{className:"w-4 h-4"})})]}):null}let M=[i.Q.configure({types:[a.A.name,s.A.name]}),a.A,l.A.configure({bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}})];function B(e){let{value:t,onChange:n}=e;return(0,r.jsx)("div",{className:"border rounded-md bg-background",children:(0,r.jsx)(o.zz,{slotBefore:(0,r.jsx)(O,{}),extensions:M,content:t,onUpdate:e=>{let{editor:t}=e;n(t.getHTML())}})})}},68699:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(27487);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...h}=e;return(0,r.createElement)("svg",{ref:t,...a,width:i,height:i,stroke:n,strokeWidth:l?24*Number(o)/Number(i):o,className:s("lucide",c),...h},[...u.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:l,...c}=n;return(0,r.createElement)(o,{ref:a,iconNode:t,className:s("lucide-".concat(i(e)),l),...c})});return n.displayName="".concat(e),n}},76501:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(29207);n(27487);let i=(0,n(7038).default)(()=>Promise.all([n.e(2977),n.e(4651)]).then(n.bind(n,44651)),{loadableGenerated:{webpack:()=>[null]},ssr:!1});function s(e){let{value:t,onChange:n}=e;return(0,r.jsx)("div",{className:"w-full md:w-[800px]",children:(0,r.jsx)(i,{value:t,onChange:e=>n(e||""),height:600})})}},78186:(e,t,n)=>{"use strict";n.d(t,{BT:()=>l,Wu:()=>d,X9:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>a,wL:()=>u});var r=n(29207);n(27487);var i=n(33335);function s(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",t),...n})}function a(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...n})}function o(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...n})}function l(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...n})}function c(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,i.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...n})}function d(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...n})}function u(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex items-center px-6 [.border-t]:pt-6",t),...n})}},89863:(e,t,n)=>{"use strict";n.d(t,{N_:()=>a,a8:()=>l,rd:()=>c});var r=n(2641),i=n(30325);let s=(0,n(70361).A)({locales:r.IB,defaultLocale:r.q,localePrefix:r.b,pathnames:r.u7,localeDetection:r.GB}),{Link:a,redirect:o,usePathname:l,useRouter:c}=(0,i.A)(s)},92105:(e,t,n)=>{"use strict";n.d(t,{E:()=>l});var r=n(29207);n(27487);var i=n(46181),s=n(10060),a=n(33335);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:n,asChild:s=!1,...l}=e,c=s?i.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(o({variant:n}),t),...l})}},95409:(e,t,n)=>{"use strict";n.d(t,{k5:()=>d});var r=n(27487),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=r.createContext&&r.createContext(i),a=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,i,s;r=e,i=t,s=n[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in r?Object.defineProperty(r,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[i]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function d(e){return t=>r.createElement(u,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,c({key:n},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var n,{attr:i,size:s,title:l}=e,d=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,a),u=s||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,d,{className:n,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),e.children)};return void 0!==s?r.createElement(s.Consumer,null,e=>t(e)):t(i)}}},e=>{var t=t=>e(e.s=t);e.O(0,[3890,4474,4056,7885,8104,3736,2268,6310,9324,1286,3102,7524,7358],()=>t(30173)),_N_E=e.O()}]);