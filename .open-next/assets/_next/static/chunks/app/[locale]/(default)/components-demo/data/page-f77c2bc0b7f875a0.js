(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7130],{2641:(e,s,a)=>{"use strict";a.d(s,{GB:()=>n,IB:()=>i,L$:()=>t,b:()=>l,q:()=>r,u7:()=>d});let i=["en","zh"],t={en:"English",zh:"中文"},r="en",l="as-needed",n=!1,d={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},8357:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var i=a(29207),t=a(27487),r=a(89361),l=a(87853),n=a(25668);let d={RiChatSmile3Line:r.Bgv,RiImageLine:r.fsL,RiVideoLine:r.xi0,RiMicLine:r.R<PERSON>,RiMoneyDollarCircleLine:r.AN5,RiArrowRightUpLine:r.SJ3,RiFlashlightFill:r.bwM,RiEyeLine:r.tLq,RiCpuLine:r.y_v,RiUserSmileLine:r.VNl,RiFlashlightLine:r.uEe,RiStarLine:r.WN7,RiPaletteLine:r.LrS,RiRocketLine:r.QWc,RiVoiceprintLine:r.Dcp,RiExchangeLine:r.Lcj,RiTwitterXFill:r.ase,RiGithubFill:r.sAW,RiDiscordFill:r.r53,RiMailLine:r.R0Y,FaRegHeart:l.sOK,GoThumbsup:n.VZG,GoArrowUpRight:n.zny},c=(0,t.memo)(e=>{let{name:s,className:a,onClick:t,...r}=e,l=d[s];return l?(0,i.jsx)(l,{className:a,onClick:t,style:{cursor:t?"pointer":"default"},...r}):null})},11553:(e,s,a)=>{Promise.resolve().then(a.bind(a,13643))},13643:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var i=a(29207),t=a(92105),r=a(78186);function l(e){let{dataCards:s}=e;return(0,i.jsx)("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4",children:s.map((e,s)=>(0,i.jsxs)(r.Zp,{className:"@container/card",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.BT,{children:e.title}),(0,i.jsx)(r.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:e.value}),(0,i.jsx)(r.X9,{children:e.label&&(0,i.jsx)(t.E,{variant:"outline",children:e.label})})]}),(0,i.jsxs)(r.wL,{className:"flex-col items-start gap-1.5 text-sm",children:[(0,i.jsx)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:e.description}),(0,i.jsx)("div",{className:"text-muted-foreground",children:e.tip})]})]},s))})}var n=a(64542),d=a(89863),c=a(8357);function o(){return(0,i.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,i.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)(t.E,{variant:"outline",className:"mb-4",children:"Data Visualization Components"}),(0,i.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Data Visualization Components Demo"}),(0,i.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Display your data beautifully with cards and interactive charts that provide insights at a glance."})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)(d.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,i.jsx)(c.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,i.jsxs)("section",{className:"mb-16",children:[(0,i.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,i.jsx)(r.Zp,{children:(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(c.default,{name:"RiDashboardLine",className:"w-5 h-5"}),"Data Cards Component"]}),(0,i.jsx)(r.BT,{children:"Display key metrics and KPIs in visually appealing cards with icons, values, and trend indicators."})]})})}),(0,i.jsx)("div",{className:"border-y bg-muted/30",children:(0,i.jsx)("div",{className:"container mx-auto py-16",children:(0,i.jsx)(l,{dataCards:[{title:"Total Revenue",value:"$45,231.89",label:"+20.1%",description:"from last month",tip:"Revenue generated this month",icon:"RiMoneyDollarCircleLine"},{title:"Active Users",value:"2,350",label:"+180.1%",description:"from last month",tip:"Users who logged in this month",icon:"RiUserLine"},{title:"Total Orders",value:"12,234",label:"+19%",description:"from last month",tip:"Orders placed this month",icon:"RiShoppingCartLine"},{title:"Conversion Rate",value:"3.2%",label:"+0.5%",description:"from last month",tip:"Percentage of visitors who made a purchase",icon:"RiLineChartLine"}]})})})]}),(0,i.jsxs)("section",{className:"mb-16",children:[(0,i.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,i.jsx)(r.Zp,{children:(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(c.default,{name:"RiLineChartLine",className:"w-5 h-5"}),"Data Charts Component"]}),(0,i.jsx)(r.BT,{children:"Interactive area charts with time range selection and category filtering for data visualization."})]})})}),(0,i.jsx)("div",{className:"border-y",children:(0,i.jsx)("div",{className:"container mx-auto py-16",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsx)(n.default,{title:"Website Traffic",description:"Showing total visitors for the last 30 days",data:[{date:"2024-01-01",desktop:222,mobile:150},{date:"2024-01-02",desktop:97,mobile:180},{date:"2024-01-03",desktop:167,mobile:120},{date:"2024-01-04",desktop:242,mobile:260},{date:"2024-01-05",desktop:373,mobile:290},{date:"2024-01-06",desktop:301,mobile:340},{date:"2024-01-07",desktop:245,mobile:180},{date:"2024-01-08",desktop:409,mobile:320},{date:"2024-01-09",desktop:59,mobile:110},{date:"2024-01-10",desktop:261,mobile:190},{date:"2024-01-11",desktop:327,mobile:350},{date:"2024-01-12",desktop:292,mobile:210},{date:"2024-01-13",desktop:342,mobile:380},{date:"2024-01-14",desktop:137,mobile:220},{date:"2024-01-15",desktop:120,mobile:170},{date:"2024-01-16",desktop:138,mobile:190},{date:"2024-01-17",desktop:446,mobile:360},{date:"2024-01-18",desktop:364,mobile:410},{date:"2024-01-19",desktop:243,mobile:180},{date:"2024-01-20",desktop:89,mobile:150},{date:"2024-01-21",desktop:137,mobile:200},{date:"2024-01-22",desktop:224,mobile:170},{date:"2024-01-23",desktop:138,mobile:230},{date:"2024-01-24",desktop:387,mobile:290},{date:"2024-01-25",desktop:215,mobile:250},{date:"2024-01-26",desktop:75,mobile:130},{date:"2024-01-27",desktop:383,mobile:420},{date:"2024-01-28",desktop:122,mobile:180},{date:"2024-01-29",desktop:315,mobile:240},{date:"2024-01-30",desktop:454,mobile:380}],fields:[{key:"desktop",label:"Desktop",color:"hsl(var(--chart-1))"},{key:"mobile",label:"Mobile",color:"hsl(var(--chart-2))"}],defaultTimeRange:"30d"})})})})]}),(0,i.jsx)("section",{className:"mb-16",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(c.default,{name:"RiInformationLine",className:"w-5 h-5"}),"Data Component Features"]})}),(0,i.jsxs)(r.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold mb-3",children:"Data Cards Features:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,i.jsx)("li",{children:"Responsive grid layout that adapts to screen size"}),(0,i.jsx)("li",{children:"Support for icons, values, labels, and descriptions"}),(0,i.jsx)("li",{children:"Trend indicators with percentage changes"}),(0,i.jsx)("li",{children:"Tooltips for additional context"}),(0,i.jsx)("li",{children:"Gradient backgrounds and shadow effects"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold mb-3",children:"Data Charts Features:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,i.jsx)("li",{children:"Interactive area charts built with Recharts"}),(0,i.jsx)("li",{children:"Time range selection (30d, 7d, 24h)"}),(0,i.jsx)("li",{children:"Category filtering and toggling"}),(0,i.jsx)("li",{children:"Responsive design for mobile and desktop"}),(0,i.jsx)("li",{children:"Customizable colors and styling"}),(0,i.jsx)("li",{children:"Tooltip with detailed data on hover"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold mb-3",children:"Usage Examples:"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<DataCards dataCards={metricsData} />"}),(0,i.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<DataCharts data={chartData} config={chartConfig} />"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold mb-3",children:"Data Structure:"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Data Cards expect an array of objects with:"}),(0,i.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"{ title, value, label, description, tip, icon }"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Charts expect time-series data with:"}),(0,i.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"{ date, [category1], [category2], ... }"})]})]})]})]})})}),(0,i.jsx)("section",{className:"mb-16",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(c.default,{name:"RiSpeedUpLine",className:"w-5 h-5"}),"Performance Metrics Example"]}),(0,i.jsx)(r.BT,{children:"Another example showing different types of metrics and data visualization."})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"pb-2",children:[(0,i.jsx)(r.BT,{children:"Page Load Time"}),(0,i.jsx)(r.ZB,{className:"text-2xl",children:"1.2s"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(t.E,{variant:"outline",className:"bg-green-50 border-green-200 text-green-800",children:"-15%"})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"pb-2",children:[(0,i.jsx)(r.BT,{children:"Bounce Rate"}),(0,i.jsx)(r.ZB,{className:"text-2xl",children:"23.1%"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(t.E,{variant:"outline",className:"bg-red-50 border-red-200 text-red-800",children:"+2.1%"})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"pb-2",children:[(0,i.jsx)(r.BT,{children:"API Response"}),(0,i.jsx)(r.ZB,{className:"text-2xl",children:"245ms"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(t.E,{variant:"outline",className:"bg-blue-50 border-blue-200 text-blue-800",children:"-8%"})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"pb-2",children:[(0,i.jsx)(r.BT,{children:"Uptime"}),(0,i.jsx)(r.ZB,{className:"text-2xl",children:"99.9%"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(t.E,{variant:"outline",className:"bg-green-50 border-green-200 text-green-800",children:"Stable"})})]})]})})]})})})]})}},33335:(e,s,a)=>{"use strict";a.d(s,{cn:()=>r});var i=a(46586),t=a(71427);function r(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,i.$)(s))}},45143:(e,s,a)=>{"use strict";a.d(s,{a:()=>t});var i=a(27487);function t(){let[e,s]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{s(window.innerWidth<768)};return e.addEventListener("change",a),s(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}},89863:(e,s,a)=>{"use strict";a.d(s,{N_:()=>l,a8:()=>d,rd:()=>c});var i=a(2641),t=a(30325);let r=(0,a(70361).A)({locales:i.IB,defaultLocale:i.q,localePrefix:i.b,pathnames:i.u7,localeDetection:i.GB}),{Link:l,redirect:n,usePathname:d,useRouter:c}=(0,t.A)(r)},92105:(e,s,a)=>{"use strict";a.d(s,{E:()=>d});var i=a(29207);a(27487);var t=a(46181),r=a(10060),l=a(33335);let n=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:a,asChild:r=!1,...d}=e,c=r?t.DX:"span";return(0,i.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),s),...d})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4474,4056,7885,2268,6310,9324,6058,5562,3696,2717,4007,2426,4542,3102,7524,7358],()=>s(11553)),_N_E=e.O()}]);