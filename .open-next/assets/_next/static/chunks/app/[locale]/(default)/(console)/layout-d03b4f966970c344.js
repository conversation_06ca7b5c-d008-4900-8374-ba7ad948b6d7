(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7382],{2641:(e,t,r)=>{"use strict";r.d(t,{GB:()=>l,IB:()=>n,L$:()=>i,b:()=>o,q:()=>a,u7:()=>c});let n=["en","zh"],i={en:"English",zh:"中文"},a="en",o="as-needed",l=!1,c={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},6316:(e,t,r)=>{Promise.resolve().then(r.bind(r,16261)),Promise.resolve().then(r.bind(r,71339))},8357:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(29207),i=r(27487),a=r(89361),o=r(87853),l=r(25668);let c={RiChatSmile3Line:a.Bgv,RiImageLine:a.fsL,RiVideoLine:a.xi0,RiMicLine:a.RQr,RiMoneyDollarCircleLine:a.AN5,RiArrowRightUpLine:a.SJ3,RiFlashlightFill:a.bwM,RiEyeLine:a.tLq,RiCpuLine:a.y_v,RiUserSmileLine:a.VNl,RiFlashlightLine:a.uEe,RiStarLine:a.WN7,RiPaletteLine:a.LrS,RiRocketLine:a.QWc,RiVoiceprintLine:a.Dcp,RiExchangeLine:a.Lcj,RiTwitterXFill:a.ase,RiGithubFill:a.sAW,RiDiscordFill:a.r53,RiMailLine:a.R0Y,FaRegHeart:o.sOK,GoThumbsup:l.VZG,GoArrowUpRight:l.zny},u=(0,i.memo)(e=>{let{name:t,className:r,onClick:i,...a}=e,o=c[t];return o?(0,n.jsx)(o,{className:r,onClick:i,style:{cursor:i?"pointer":"default"},...a}):null})},10060:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(46586);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:l}=t,c=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=i(t)||i(n);return o[e][a]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,c,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...u}[t]):({...l,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},16261:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(29207),i=r(8357),a=r(89863),o=r(70616),l=r(33335),c=r(52545);function u(e){let{className:t,items:r,...u}=e,s=(0,c.usePathname)();return console.log(s),(0,n.jsx)("nav",{className:(0,l.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",t),...u,children:r.map((e,t)=>(0,n.jsxs)(a.N_,{href:e.url,className:(0,l.cn)((0,o.r)({variant:"ghost"}),e.is_active||s.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,n.jsx)(i.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},30325:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(52545),i=r(27487),a=r.t(i,2),o=r(76310),l=a["use".trim()],c=r(56402),u=r(71339),s=r(41559),f=r(29207),d=r(97581);function p(e){let{Link:t,config:r,getPathname:a,...p}=function(e,t){var r,a,o;let d={...r=t||{},localePrefix:"object"==typeof(o=r.localePrefix)?o:{mode:o||"always"},localeCookie:!!((a=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof a&&a},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},p=d.pathnames,h=(0,i.forwardRef)(function({href:t,locale:r,...n},i){let a,o;"object"==typeof t?(a=t.pathname,o=t.params):a=t;let s=(0,c._x)(t),h=e(),v=(0,c.yL)(h)?l(h):h,g=s?m({locale:r||v,href:null==p?a:{pathname:a,params:o},forcePrefix:null!=r||void 0}):a;return(0,f.jsx)(u.default,{ref:i,href:"object"==typeof t?{...t,pathname:g}:g,locale:r,localeCookie:d.localeCookie,...n})});function m(e){let t,{forcePrefix:r,href:n,locale:i}=e;return null==p?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,s.Zn)(n.query))):t=n:t=(0,s.FP)({locale:i,...(0,s.TK)(n),pathnames:d.pathnames}),(0,s.x3)(t,i,d,r)}function v(e){return function(t,...r){return e(m(t),...r)}}return{config:d,Link:h,redirect:v(n.redirect),permanentRedirect:v(n.permanentRedirect),getPathname:m}}(o.Ym,e);return{...p,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),r=(0,o.Ym)();return(0,i.useMemo)(()=>{if(!t)return t;let n=t,i=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(i,t))n=(0,c.MY)(t,i);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(n=(0,c.MY)(t,e))}return n},[e.localePrefix,r,t])}(r),t=(0,o.Ym)();return(0,i.useMemo)(()=>e&&r.pathnames?(0,s.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,o.Ym)(),l=(0,n.usePathname)();return(0,i.useMemo)(()=>{function n(e){return function(n,i){let{locale:o,...c}=i||{},u=[a({href:n,locale:o||t})];Object.keys(c).length>0&&u.push(c),(0,d.A)(r.localeCookie,l,t,o),e(...u)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,l,e])},getPathname:a}}},33335:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(46586),i=r(71427);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}},41559:(e,t,r)=>{"use strict";r.d(t,{DT:()=>c,FP:()=>o,TK:()=>i,Zn:()=>a,aM:()=>l,x3:()=>u});var n=r(56402);function i(e){return"string"==typeof e?{pathname:e}:e}function a(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))Array.isArray(n)?n.forEach(e=>{t.append(r,String(e))}):t.set(r,String(n));return"?"+t.toString()}function o({pathname:e,locale:t,params:r,pathnames:i,query:o}){function l(e){let l,c=i[e];return c?(l=(0,n.Wl)(c,t,e),r&&Object.entries(r).forEach(([e,t])=>{let r,n;Array.isArray(t)?(r=`(\\[)?\\[...${e}\\](\\])?`,n=t.map(e=>String(e)).join("/")):(r=`\\[${e}\\]`,n=String(t)),l=l.replace(RegExp(r,"g"),n)}),l=(l=l.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):l=e,l=(0,n.po)(l),o&&(l+=a(o)),l}if("string"==typeof e)return l(e);{let{pathname:t,...r}=e;return{...r,pathname:l(t)}}}function l(e,t,r){let i=(0,n.FD)(Object.keys(r)),a=decodeURI(t);for(let t of i){let i=r[t];if("string"==typeof i){if((0,n.ql)(i,a))return t}else if((0,n.ql)((0,n.Wl)(i,e,t),a))return t}return t}function c(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function u(e,t,r,i){let a,{mode:o}=r.localePrefix;return void 0!==i?a=i:(0,n._x)(e)&&("always"===o?a=!0:"as-needed"===o&&(a=r.domains?!r.domains.some(e=>e.defaultLocale===t):t!==r.defaultLocale)),a?(0,n.PJ)((0,n.XP)(t,r.localePrefix),e):e}},52545:(e,t,r)=>{"use strict";var n=r(94925);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},56402:(e,t,r)=>{"use strict";r.d(t,{FD:()=>g,MY:()=>a,PJ:()=>o,Wl:()=>c,XP:()=>f,_x:()=>i,bL:()=>d,po:()=>u,ql:()=>s,wO:()=>l,yL:()=>b});var n=r(55036);function i(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function a(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function o(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}function l(e,t){return t===e||t.startsWith(`${e}/`)}function c(e,t,r){return"string"==typeof e?e:e[t]||r}function u(e){let t=function(){try{return"true"===n.env._next_intl_trailing_slash}catch{return!1}}(),[r,...i]=e.split("#"),a=i.join("#"),o=r;if("/"!==o){let e=o.endsWith("/");t&&!e?o+="/":!t&&e&&(o=o.slice(0,-1))}return a&&(o+="#"+a),o}function s(e,t){let r=u(e),n=u(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(r).test(n)}function f(e,t){return"never"!==t.mode&&t.prefixes?.[e]||d(e)}function d(e){return"/"+e}function p(e){return e.includes("[[...")}function h(e){return e.includes("[...")}function m(e){return e.includes("[")}function v(e,t){let r=e.split("/"),n=t.split("/"),i=Math.max(r.length,n.length);for(let e=0;e<i;e++){let t=r[e],i=n[e];if(!t&&i)return -1;if(t&&!i)return 1;if(t||i){if(!m(t)&&m(i))return -1;if(m(t)&&!m(i))return 1;if(!h(t)&&h(i))return -1;if(h(t)&&!h(i))return 1;if(!p(t)&&p(i))return -1;if(p(t)&&!p(i))return 1}}return 0}function g(e){return e.sort(v)}function b(e){return"function"==typeof e.then}},70361:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{A:()=>n})},70616:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var n=r(29207);r(27487);var i=r(46181),a=r(10060),o=r(33335);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:a,asChild:c=!1,...u}=e,s=c?i.DX:"button";return(0,n.jsx)(s,{"data-slot":"button",className:(0,o.cn)(l({variant:r,size:a,className:t})),...u})}},71339:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(9324),i=r(52545),a=r(27487),o=r(76310),l=r(97581),c=r(29207),u=(0,a.forwardRef)(function(e,t){let{href:r,locale:a,localeCookie:u,onClick:s,prefetch:f,...d}=e,p=(0,o.Ym)(),h=null!=a&&a!==p,m=(0,i.usePathname)();return h&&(f=!1),(0,c.jsx)(n,{ref:t,href:r,hrefLang:h?a:void 0,onClick:function(e){(0,l.A)(u,m,p,a),s&&s(e)},prefetch:f,...d})})},89863:(e,t,r)=>{"use strict";r.d(t,{N_:()=>o,a8:()=>c,rd:()=>u});var n=r(2641),i=r(30325);let a=(0,r(70361).A)({locales:n.IB,defaultLocale:n.q,localePrefix:n.b,pathnames:n.u7,localeDetection:n.GB}),{Link:o,redirect:l,usePathname:c,useRouter:u}=(0,i.A)(a)},95409:(e,t,r)=>{"use strict";r.d(t,{k5:()=>s});var n=r(27487),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(i),o=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){return t=>n.createElement(f,l({attr:u({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,u({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:i,size:a,title:c}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),f=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,s,{className:r,style:u(u({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>t(e)):t(i)}},97581:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(41559);function i(e,t,r,i){if(!e||i===r||null==i||!t)return;let a=(0,n.DT)(t),{name:o,...l}=e;l.path||(l.path=""!==a?a:"/");let c=`${o}=${i};`;for(let[e,t]of Object.entries(l))c+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(c+="="+t),c+=";";document.cookie=c}}},e=>{var t=t=>e(e.s=t);e.O(0,[4474,4056,7885,2268,6310,9324,3102,7524,7358],()=>t(6316)),_N_E=e.O()}]);