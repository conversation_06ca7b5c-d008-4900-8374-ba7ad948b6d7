(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3423],{8202:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(29207),s=a(27487),n=a(33335);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...s})});i.displayName="Textarea"},16692:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(29207);a(27487);var s=a(33335);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},33335:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(46586),s=a(71427);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},56587:(e,t,a)=>{"use strict";a.d(t,{ModelTranslationManager:()=>N});var r=a(29207),s=a(27487),n=a(78186),i=a(70616),d=a(16692),l=a(92402),o=a(8202),c=a(92105),u=a(59548),m=a(83978),g=a(60411),v=a(8958),f=a(44124),x=a(26769),p=a(77587),h=a(69940);function b(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["en","zh"];return e&&"object"==typeof e?t.filter(t=>!e[t]||""===e[t].trim()):t}let j=[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"zh",name:"中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"ko",name:"한국어",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"fr",name:"Fran\xe7ais",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"de",name:"Deutsch",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"es",name:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"it",name:"Italiano",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"pt",name:"Portugu\xeas",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"ru",name:"Русский",flag:"\uD83C\uDDF7\uD83C\uDDFA"}];function N(e){let{className:t}=e,[a,N]=(0,s.useState)([]),[y,w]=(0,s.useState)([]),[k,_]=(0,s.useState)(null),[T,R]=(0,s.useState)({name:{},description:{}}),[A,F]=(0,s.useState)(!1),[S,Z]=(0,s.useState)(!1),z=async()=>{F(!0);try{let e=await fetch("/api/ai/models?locale=en"),t=await e.json();0===t.code&&(N(t.data.models),B(t.data.models))}catch(e){h.oR.error("Failed to fetch models")}finally{F(!1)}},B=e=>{w(e.map(e=>{let t=e.model_name_i18n||{},a=e.description_i18n||{},r=j.map(e=>e.code),s=b(t,r),n=b(a,r),i=2*r.length,d=Math.round((i-(s.length+n.length))/i*100);return{modelId:e.model_id,modelName:e.model_name,missingTranslations:{name:s,description:n},completeness:d}}))},C=e=>{_(e),R({name:{...e.model_name_i18n||{}},description:{...e.description_i18n||{}}})},E=(e,t,a)=>{R(r=>({...r,[e]:{...r[e],[t]:a}}))},I=async()=>{if(k){Z(!0);try{let e=await fetch("/api/admin/models/translations",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({modelId:k.model_id,translations:T})}),t=await e.json();0===t.code?(h.oR.success("Translations saved successfully"),z()):h.oR.error(t.msg||"Failed to save translations")}catch(e){h.oR.error("Failed to save translations")}finally{Z(!1)}}};return(0,s.useEffect)(()=>{z()},[]),(0,r.jsxs)("div",{className:"space-y-6 ".concat(t),children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Models"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:a.length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Fully Translated"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:y.filter(e=>100===e.completeness).length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Needs Translation"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:y.filter(e=>e.completeness<100).length})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"w-5 h-5"}),"Models Translation Status"]}),(0,r.jsx)(n.BT,{children:"Click on a model to edit its translations"})]}),(0,r.jsx)(n.Wu,{className:"space-y-2",children:A?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)(v.A,{className:"w-6 h-6 animate-spin"})}):y.map(e=>(0,r.jsx)("div",{className:"p-3 rounded-lg border cursor-pointer transition-colors ".concat((null==k?void 0:k.model_id)===e.modelId?"border-primary bg-primary/5":"border-border hover:bg-muted/50"),onClick:()=>{let t=a.find(t=>t.model_id===e.modelId);t&&C(t)},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.modelName}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:e.modelId})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(c.E,{variant:100===e.completeness?"default":"secondary",children:[e.completeness,"%"]}),100===e.completeness?(0,r.jsx)(f.A,{className:"w-4 h-4 text-green-600"}):(0,r.jsx)(x.A,{className:"w-4 h-4 text-orange-600"})]})]})},e.modelId))})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Translation Editor"}),(0,r.jsx)(n.BT,{children:k?"Editing: ".concat(k.model_name):"Select a model to edit translations"})]}),(0,r.jsx)(n.Wu,{children:k?(0,r.jsxs)(u.tU,{defaultValue:"name",className:"space-y-4",children:[(0,r.jsxs)(u.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(u.Xi,{value:"name",children:"Model Name"}),(0,r.jsx)(u.Xi,{value:"description",children:"Description"})]}),(0,r.jsx)(u.av,{value:"name",className:"space-y-4",children:j.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(l.J,{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.flag}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)(d.p,{value:T.name[e.code]||"",onChange:t=>E("name",e.code,t.target.value),placeholder:"Model name in ".concat(e.name)})]},e.code))}),(0,r.jsx)(u.av,{value:"description",className:"space-y-4",children:j.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(l.J,{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.flag}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)(o.T,{value:T.description[e.code]||"",onChange:t=>E("description",e.code,t.target.value),placeholder:"Description in ".concat(e.name),rows:3})]},e.code))}),(0,r.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,r.jsx)(i.$,{onClick:I,disabled:S,children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Save Translations"]})}),(0,r.jsxs)(i.$,{variant:"outline",onClick:z,children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}):(0,r.jsxs)(m.Fc,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(m.TN,{children:"Select a model from the list to start editing translations."})]})})]})]})]})}},59548:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>l,av:()=>o,j7:()=>d,tU:()=>i});var r=a(29207);a(27487);var s=a(25412),n=a(33335);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted/50 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-xl p-1 backdrop-blur-sm border border-border/30",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-md focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-9 flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-background/50 data-[state=active]:scale-[1.02] [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",t),...a})}},70616:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>d});var r=a(29207);a(27487);var s=a(46181),n=a(10060),i=a(33335);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...o})}},78186:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,X9:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>u});var r=a(29207);a(27487);var s=a(33335);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},83978:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>l,TN:()=>o});var r=a(29207),s=a(27487),n=a(10060),i=a(33335);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef((e,t)=>{let{className:a,variant:s,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(d({variant:s}),a),...n})});l.displayName="Alert",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",a),...s})});o.displayName="AlertDescription"},92105:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(29207);a(27487);var s=a(46181),n=a(10060),i=a(33335);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:n=!1,...l}=e,o=n?s.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),t),...l})}},92402:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(29207);a(27487);var s=a(59538),n=a(33335);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},98715:(e,t,a)=>{Promise.resolve().then(a.bind(a,56587))}},e=>{var t=t=>e(e.s=t);e.O(0,[2268,5892,7559,3696,2493,3102,7524,7358],()=>t(98715)),_N_E=e.O()}]);