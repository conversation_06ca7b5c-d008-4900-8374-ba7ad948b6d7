(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2247],{18764:(e,l,s)=>{Promise.resolve().then(s.bind(s,1281)),Promise.resolve().then(s.bind(s,11521)),Promise.resolve().then(s.bind(s,32006)),Promise.resolve().then(s.bind(s,48917)),Promise.resolve().then(s.bind(s,54344)),Promise.resolve().then(s.bind(s,8357)),Promise.resolve().then(s.bind(s,64487)),Promise.resolve().then(s.bind(s,25412)),Promise.resolve().then(s.bind(s,71339)),Promise.resolve().then(s.t.bind(s,9324,23)),Promise.resolve().then(s.t.bind(s,19597,23))},48917:(e,l,s)=>{"use strict";s.d(l,{default:()=>o});var t=s(29207),r=s(78987),a=s(66708),i=s(42774),n=s(27487),d=s(92105),c=s(70616);function o(e){var l;let{section:s}=e;if(s.disabled)return null;let[o,m]=(0,n.useState)(),[x,u]=(0,n.useState)(!1),[b,f]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{if(!o)return;let e=()=>{u(o.canScrollPrev()),f(o.canScrollNext())};return e(),o.on("select",e),()=>{o.off("select",e)}},[o]),(0,t.jsxs)("section",{id:s.name,className:"py-16",children:[(0,t.jsx)("div",{className:"container",children:(0,t.jsxs)("div",{className:"mb-8 flex items-end justify-between md:mb-14 lg:mb-16",children:[(0,t.jsx)("h2",{className:"mb-2 text-pretty text-3xl font-bold lg:text-4xl",children:s.title}),(0,t.jsxs)("div",{className:"shrink-0 gap-2 md:flex",children:[(0,t.jsx)(c.$,{size:"icon",variant:"ghost",onClick:()=>{null==o||o.scrollPrev()},disabled:!x,className:"disabled:pointer-events-auto",children:(0,t.jsx)(r.A,{className:"size-5"})}),(0,t.jsx)(c.$,{size:"icon",variant:"ghost",onClick:()=>{null==o||o.scrollNext()},disabled:!b,className:"disabled:pointer-events-auto",children:(0,t.jsx)(a.A,{className:"size-5"})})]})]})}),(0,t.jsx)("div",{className:"w-full",children:(0,t.jsx)(i.FN,{setApi:m,opts:{breakpoints:{"(max-width: 768px)":{dragFree:!0}}},children:(0,t.jsx)(i.Wk,{className:"container ml-[calc(theme(container.padding)-20px)] mr-[calc(theme(container.padding))] 2xl:ml-[calc(50vw-700px+theme(container.padding)-20px)] 2xl:mr-[calc(50vw-700px+theme(container.padding))]",children:null==(l=s.items)?void 0:l.map((e,l)=>{var s,r;return(0,t.jsx)(i.A7,{className:"max-w-[320px] pl-[20px] lg:max-w-[360px]",children:(0,t.jsxs)("a",{href:e.url,target:e.target,className:"group flex flex-col justify-between rounded-xl border border-border bg-card p-6",children:[(0,t.jsx)("div",{children:(0,t.jsx)("div",{className:"flex aspect-3/2 overflow-clip rounded-xl",children:(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("div",{className:"relative h-full w-full origin-bottom transition duration-300 group-hover:scale-105",children:(0,t.jsx)("img",{src:null==(s=e.image)?void 0:s.src,alt:(null==(r=e.image)?void 0:r.alt)||e.title,className:"h-full w-full object-cover object-center"})})})})}),e.label&&(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(d.E,{children:e.label})}),(0,t.jsx)("div",{className:"mb-2 line-clamp-3 break-words pt-4 text-lg font-medium md:mb-3 md:pt-4 md:text-xl lg:pt-4 lg:text-2xl",children:e.title}),(0,t.jsx)("div",{className:"mb-2 line-clamp-2 text-sm text-muted-foreground md:mb-2 md:text-base lg:mb-2",children:e.description})]})},l)})})})})]})}},54344:(e,l,s)=>{"use strict";s.d(l,{default:()=>m});var t=s(29207),r=s(64487),a=s(42774),i=s(23160),n=s(78186),d=s(8357),c=s(71322),o=s(27487);function m(e){var l;let{section:s}=e;if(s.disabled)return null;let m=(0,o.useRef)((0,i.A)({startDelay:500,speed:.7}));return(0,t.jsxs)("section",{id:s.name,className:"py-16",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[s.label&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm font-semibold text-primary",children:[s.icon&&(0,t.jsx)(d.default,{name:s.icon,className:"h-6 w-auto border-primary"}),s.label]}),(0,t.jsx)("h2",{className:"text-center text-3xl font-semibold lg:text-4xl",children:s.title}),(0,t.jsx)("p",{className:"text-center text-muted-foreground lg:text-lg",children:s.description})]}),(0,t.jsx)("div",{className:"",children:(0,t.jsx)("div",{className:"mt-16 space-y-4",children:(0,t.jsx)(a.FN,{opts:{loop:!0},plugins:[m.current],onMouseLeave:()=>m.current.play(),className:"relative before:absolute before:bottom-0 before:left-0 before:top-0 before:z-10 before:w-36 before:bg-linear-to-r before:from-background before:to-transparent after:absolute after:bottom-0 after:right-0 after:top-0 after:z-10 after:w-36 after:bg-linear-to-l after:from-background after:to-transparent",children:(0,t.jsx)(a.Wk,{children:null==(l=s.items)?void 0:l.map((e,l)=>{var s,i;return(0,t.jsx)(a.A7,{className:"basis-auto",children:(0,t.jsxs)(n.Zp,{className:"max-w-96 select-none p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("div",{className:"mb-4 flex gap-4",children:[(0,t.jsx)(r.Avatar,{className:"size-14 rounded-full ring-1 ring-input",children:(0,t.jsx)(r.AvatarImage,{src:null==(s=e.image)?void 0:s.src,alt:(null==(i=e.image)?void 0:i.alt)||e.title})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.label})]})]}),(0,t.jsx)("div",{className:"flex gap-1",children:Array.from({length:5}).map((e,l)=>(0,t.jsx)(c.A,{className:"size-5 fill-amber-500 text-amber-500"},l))})]}),(0,t.jsx)("q",{className:"leading-7 text-muted-foreground",children:e.description})]})},l)})})})})})]})}}},e=>{var l=l=>e(e.s=l);e.O(0,[4474,4056,7885,2268,6310,9324,5892,3696,9940,4534,4452,9617,8518,3911,3102,7524,7358],()=>l(18764)),_N_E=e.O()}]);