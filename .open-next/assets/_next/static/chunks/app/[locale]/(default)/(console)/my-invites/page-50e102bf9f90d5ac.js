(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3951],{8357:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var i=a(29207),r=a(27487),s=a(89361),n=a(87853),o=a(25668);let l={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.RQr,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.Lr<PERSON>,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:n.sOK,GoThumbsup:o.VZG,GoArrowUpRight:o.zny},d=(0,r.memo)(e=>{let{name:t,className:a,onClick:r,...s}=e,n=l[t];return n?(0,i.jsx)(n,{className:a,onClick:r,style:{cursor:r?"pointer":"default"},...s}):null})},16692:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var i=a(29207);a(27487);var r=a(33335);function s(e){let{className:t,type:a,...s}=e;return(0,i.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},20016:(e,t,a)=>{Promise.resolve().then(a.bind(a,39124)),Promise.resolve().then(a.bind(a,56313)),Promise.resolve().then(a.bind(a,69268)),Promise.resolve().then(a.bind(a,71339)),Promise.resolve().then(a.t.bind(a,9324,23))},33335:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var i=a(46586),r=a(71427);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,i.$)(t))}},39124:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var i=a(29207),r=a(18312),s=a(69940);function n(e){let{text:t,children:a}=e;return(0,i.jsx)(r.CopyToClipboard,{text:t,onCopy:()=>s.oR.success("Copied"),children:(0,i.jsx)("div",{className:"cursor-pointer",children:a})})}},56313:(e,t,a)=>{"use strict";a.d(t,{default:()=>x});var i=a(29207),r=a(78186),s=a(27487),n=a(70616),o=a(18312),l=a(8357),d=a(93304),c=a(16692),u=a(19635);function f(e){let{open:t,setOpen:a,username:r,initInviteCode:o,updateInviteCode:l,loading:f}=e,m=(0,u.c3)(),[p,x]=(0,s.useState)(o);return(0,i.jsx)(d.lG,{open:t,onOpenChange:a,children:(0,i.jsxs)(d.Cf,{className:"sm:max-w-[425px]",children:[(0,i.jsxs)(d.c7,{children:[(0,i.jsx)(d.L3,{children:m("my_invites.update_invite_code")}),(0,i.jsx)(d.rr,{children:m("my_invites.update_invite_code_tip")})]}),(0,i.jsx)("div",{className:"grid gap-4 py-4",children:(0,i.jsx)("div",{className:"grid grid-cols-1 items-center gap-4",children:(0,i.jsx)(c.p,{placeholder:"".concat(r),value:p,onChange:e=>x(e.target.value),className:"w-full"})})}),(0,i.jsx)(d.Es,{children:(0,i.jsx)(n.$,{onClick:()=>l(p),disabled:f,children:m("my_invites.update_invite_button")})})]})})}var m=a(69940),p=a(93450);function x(e){let{summary:t}=e,a=(0,u.c3)(),[d,c]=(0,s.useState)(!1),{user:x,setUser:v}=(0,p.U)(),[h,g]=(0,s.useState)(!1),b=async function(e){try{if(!(e=e.trim()))return void m.oR.error("invite code is required");g(!0);let t={invite_code:e},a=await fetch("/api/update-invite-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok)throw Error("update invite code faild with status "+a.status);let{code:i,message:r,data:s}=await a.json();if(0!==i)return void m.oR.error(r);v(s),m.oR.success("set invite code success"),c(!1)}catch(e){console.log("update invite code failed",e),m.oR.error("set invite code failed")}finally{g(!1)}};return(0,s.useEffect)(()=>{g(!1)},[]),(0,i.jsxs)("div",{className:"flex flex-wrap gap-6",children:[(0,i.jsxs)(r.Zp,{className:"flex-1 p-6",children:[(0,i.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:a("my_invites.invite_code")}),x&&x.uuid&&(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)(f,{open:d,setOpen:c,username:x.nickname,initInviteCode:x.invite_code,updateInviteCode:b,loading:h}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-3xl font-bold",children:x.invite_code||"NOT SET"}),(0,i.jsx)(l.default,{name:"RiEditLine",className:"text-primary text-xl cursor-pointer",onClick:()=>c(!0)})]}),x.invite_code&&(0,i.jsx)(o.CopyToClipboard,{text:"".concat("http://localhost:3000","/i/").concat(null==x?void 0:x.invite_code),onCopy:()=>m.oR.success("copied"),children:(0,i.jsx)(n.$,{size:"sm",children:a("my_invites.copy_invite_link")})})]}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.invite_tip")})]}),(0,i.jsxs)(r.Zp,{className:"flex-1 p-6",children:[(0,i.jsx)("div",{className:"flex justify-between items-end mb-8",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:a("my_invites.invite_balance")}),(0,i.jsxs)("p",{className:"text-4xl font-bold",children:["$",t.total_reward/100]})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-2xl font-bold",children:t.total_invited}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_invite_count")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-2xl font-bold",children:t.total_paid}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_paid_count")})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-2xl font-bold",children:["$",t.total_reward/100]}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_award_amount")})]})]})]})]})}},67699:(e,t,a)=>{"use strict";a.d(t,{lg:()=>i});let i=()=>Date.parse(new Date().toUTCString())/1e3},69268:(e,t,a)=>{"use strict";a.d(t,{Table:()=>s,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>n,TableRow:()=>l});var i=a(29207);a(27487);var r=a(33335);function s(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function n(e){let{className:t,...a}=e;return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function d(e){let{className:t,...a}=e;return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},70616:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var i=a(29207);a(27487);var r=a(46181),s=a(10060),n=a(33335);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:s,className:t})),...d})}},78186:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>n,wL:()=>u});var i=a(29207);a(27487);var r=a(33335);function s(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",t),...a})}function n(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-action",className:(0,r.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a})}function c(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function u(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},80119:(e,t,a)=>{"use strict";a.d(t,{wD:()=>i});let i={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}},80912:(e,t,a)=>{"use strict";a.d(t,{PW:()=>s,np:()=>n,pr:()=>r});var i=a(67699);let r=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let r=Number(a[0]),s=(0,i.lg)();if(-1!==r&&r<s)return n(e),null;let o=a[0]+":";return t.replace(o,"")},s=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},n=e=>{localStorage.removeItem(e)}},93304:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>p,c7:()=>f,lG:()=>l,rr:()=>x});var i=a(29207),r=a(27487),s=a(36559),n=a(7522),o=a(33335);let l=s.bL;s.l9;let d=s.ZL;s.bm;let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,i.jsx)(s.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});c.displayName=s.hJ.displayName;let u=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,i.jsxs)(d,{children:[(0,i.jsx)(c,{}),(0,i.jsxs)(s.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[r,(0,i.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,i.jsx)(n.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=s.UC.displayName;let f=e=>{let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};f.displayName="DialogHeader";let m=e=>{let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};m.displayName="DialogFooter";let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,i.jsx)(s.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});p.displayName=s.hE.displayName;let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,i.jsx)(s.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...r})});x.displayName=s.VY.displayName},93450:(e,t,a)=>{"use strict";a.d(t,{AppContextProvider:()=>f,U:()=>u});var i=a(29207),r=a(27487),s=a(80912),n=a(80119),o=a(25897),l=a.n(o),d=a(64586);let c=(0,r.createContext)({}),u=()=>(0,r.useContext)(c),f=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,d.wV)(),a=async function(){l()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),s(e.credential)})},s=async function(e){console.log("signIn ok",await (0,d.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,r.useEffect)(()=>{if("unauthenticated"===t){a();let e=setInterval(()=>{a()},3e3);return()=>{clearInterval(e)}}},[t]),i.Fragment}();let{data:a}=(0,d.wV)(),[o,u]=(0,r.useState)(()=>"light"),[f,m]=(0,r.useState)(!1),[p,x]=(0,r.useState)(null),[v,h]=(0,r.useState)(!1),g=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:a,data:i}=await e.json();if(0!==t)throw Error(a);x(i),b(i)}catch(e){console.log("fetch user info failed")}},b=async e=>{try{if(e.invited_by)return void console.log("user already been invited",e.invited_by);let t=(0,s.pr)(n.wD.InviteCode);if(!t)return;let a=moment(e.created_at).unix(),i=moment().unix(),r=Number(i-a);if(r<=0||r>7200)return void console.log("user created more than 2 hours");console.log("update invite",t,e.uuid);let o={invite_code:t,user_uuid:e.uuid},l=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!l.ok)throw Error("update invite failed with status: "+l.status);let{code:d,message:c,data:u}=await l.json();if(0!==d)throw Error(c);x(u),(0,s.np)(n.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,r.useEffect)(()=>{a&&a.user&&g()},[a]),(0,i.jsx)(c.Provider,{value:{theme:o,setTheme:u,showSignModal:f,setShowSignModal:m,user:p,setUser:x,showFeedback:v,setShowFeedback:h},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4474,4056,7885,2268,6310,9324,5892,6058,7559,430,2791,3102,7524,7358],()=>t(20016)),_N_E=e.O()}]);