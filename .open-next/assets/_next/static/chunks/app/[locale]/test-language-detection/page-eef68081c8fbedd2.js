(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7557],{7270:(e,s,r)=>{Promise.resolve().then(r.bind(r,42944))},42944:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var n=r(29207),i=r(27487),t=r(52545),a=r(70616),d=r(78186),l=r(92105),c=r(82862),o=r(28722),x=r(66483);function u(){let e=(0,t.useParams)().locale,[s,r]=(0,i.useState)(null),[u,h]=(0,i.useState)(null),j=()=>{let s=(0,c.k3)(e),n=(0,o.DQ)();r(s),h(n)};return i.useEffect(()=>{j()},[e]),(0,n.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"语言检测功能测试"}),(0,n.jsxs)("p",{className:"text-muted-foreground",children:["当前语言: ",(0,n.jsx)(l.E,{variant:"outline",children:e})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)(d.Zp,{children:[(0,n.jsxs)(d.aR,{children:[(0,n.jsx)(d.ZB,{children:"浏览器语言信息"}),(0,n.jsx)(d.BT,{children:"检测到的浏览器语言设置"})]}),(0,n.jsxs)(d.Wu,{className:"space-y-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"navigator.language:"})," ",(0,c.xd)()||"未检测到"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"支持的浏览器语言:"})," ",(0,c.BZ)()||"无"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"建议切换到:"})," ",(0,c.aj)(e)||"无建议"]})]})]}),(0,n.jsxs)(d.Zp,{children:[(0,n.jsxs)(d.aR,{children:[(0,n.jsx)(d.ZB,{children:"用户偏好信息"}),(0,n.jsx)(d.BT,{children:"保存的用户语言偏好"})]}),(0,n.jsx)(d.Wu,{children:(null==u?void 0:u.preference)?(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"选择的语言:"})," ",u.preference.selectedLanguage]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"检测到的语言:"})," ",u.preference.detectedLanguage]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"是否拒绝:"})," ",u.preference.declined?"是":"否"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"时间戳:"})," ",new Date(1e3*u.preference.timestamp).toLocaleString()]})]}):(0,n.jsx)("p",{className:"text-muted-foreground",children:"暂无保存的偏好"})})]})]}),s&&(0,n.jsxs)(d.Zp,{children:[(0,n.jsxs)(d.aR,{children:[(0,n.jsx)(d.ZB,{children:"详细调试信息"}),(0,n.jsx)(d.BT,{children:"完整的语言检测信息"})]}),(0,n.jsx)(d.Wu,{children:(0,n.jsx)("pre",{className:"bg-muted p-4 rounded-lg text-sm overflow-auto",children:JSON.stringify(s,null,2)})})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,n.jsx)(a.$,{onClick:j,variant:"outline",children:"刷新调试信息"}),(0,n.jsx)(a.$,{onClick:()=>{(0,o.A0)(),(0,o.or)(),j(),alert("所有语言偏好已清除！刷新页面以重新测试。")},variant:"destructive",children:"清除所有偏好"}),(0,n.jsx)(a.$,{onClick:()=>window.location.reload(),variant:"secondary",children:"刷新页面"})]}),(0,n.jsxs)(d.Zp,{children:[(0,n.jsx)(d.aR,{children:(0,n.jsx)(d.ZB,{children:"测试说明"})}),(0,n.jsxs)(d.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"如何测试语言检测功能："}),(0,n.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm",children:[(0,n.jsx)("li",{children:"确保浏览器语言设置与当前页面语言不同"}),(0,n.jsx)("li",{children:"清除所有偏好设置"}),(0,n.jsx)("li",{children:"刷新页面，应该会看到语言切换弹框"}),(0,n.jsx)("li",{children:'选择"切换"或"保持"来测试不同的用户选择'}),(0,n.jsx)("li",{children:"再次刷新页面，验证不会重复显示弹框"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"浏览器语言设置方法："}),(0,n.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Chrome:"})," 设置 → 高级 → 语言"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Firefox:"})," 首选项 → 常规 → 语言"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Safari:"})," 偏好设置 → 高级 → 语言"]})]})]})]})]}),(0,n.jsx)(x.G,{})]})}},78186:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>o,X9:()=>c,ZB:()=>d,Zp:()=>t,aR:()=>a,wL:()=>x});var n=r(29207);r(27487);var i=r(33335);function t(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",s),...r})}function a(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",s),...r})}function l(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",s),...r})}function c(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-action",className:(0,i.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",s),...r})}function o(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",s),...r})}function x(e){let{className:s,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex items-center px-6 [.border-t]:pt-6",s),...r})}},92105:(e,s,r)=>{"use strict";r.d(s,{E:()=>l});var n=r(29207);r(27487);var i=r(46181),t=r(10060),a=r(33335);let d=(0,t.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:r,asChild:t=!1,...l}=e,c=t?i.DX:"span";return(0,n.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(d({variant:r}),s),...l})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9813,2268,6310,5892,6058,4660,6483,3102,7524,7358],()=>s(7270)),_N_E=e.O()}]);