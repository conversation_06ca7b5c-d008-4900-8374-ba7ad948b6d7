(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7080],{5421:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>o});var a=t(27487),s=t(99470),i=t(46181),n=t(29207),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),s=a.forwardRef((e,a)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?t:r,{...i,ref:a})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function l(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},10060:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var a=t(46586);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=a.$,n=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:o}=r,l=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let i=s(r)||s(a);return n[e][i]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return i(e,l,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},16692:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(29207);t(27487);var s=t(33335);function i(e){let{className:r,type:t,...i}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},19951:(e,r,t)=>{Promise.resolve().then(t.bind(t,88959))},33335:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(46586),s=t(71427);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},59538:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var a=t(27487),s=t(5421),i=t(29207),n=a.forwardRef((e,r)=>(0,i.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var o=n},68699:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var a=t(27487);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ")};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:d="",children:c,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:r,...n,width:s,height:s,stroke:t,strokeWidth:l?24*Number(o)/Number(s):o,className:i("lucide",d),...m},[...u.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),l=(e,r)=>{let t=(0,a.forwardRef)((t,n)=>{let{className:l,...d}=t;return(0,a.createElement)(o,{ref:n,iconNode:r,className:i("lucide-".concat(s(e)),l),...d})});return t.displayName="".concat(e),t}},70616:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>o});var a=t(29207);t(27487);var s=t(46181),i=t(10060),n=t(33335);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:i,asChild:l=!1,...d}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:i,className:r})),...d})}},78186:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>u});var a=t(29207);t(27487);var s=t(33335);function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...t})}function u(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},88959:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(29207),s=t(27487),i=t(70616),n=t(16692),o=t(92402),l=t(78186),d=t(98656),c=t(69940);function u(){var e,r,t;let[u,m]=(0,s.useState)("a korean woman pale skin, only wearing lace underware, front side camera"),[v,p]=(0,s.useState)(""),[f,h]=(0,s.useState)(!1),[g,x]=(0,s.useState)(null),b=async()=>{if(!u.trim())return void c.oR.error("请输入提示词");h(!0),x(null);try{let e=await fetch("/api/test/replicate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:u})}),r=await e.json();0===r.code?(x(r.data),c.oR.success("测试成功！")):c.oR.error(r.msg||"测试失败")}catch(e){console.error("Test failed:",e),c.oR.error("网络错误")}finally{h(!1)}},y=async()=>{if(!u.trim())return void c.oR.error("请输入提示词");if(!v.trim())return void c.oR.error("请输入 API Key");h(!0),x(null);try{let e=await fetch("/api/ai/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(v)},body:JSON.stringify({model:"black-forest-labs/flux-krea-dev",type:"image",prompt:u,options:{output_quality:90,output_format:"webp",disable_safety_checker:!0}})}),r=await e.json();0===r.code?(x(r.data),c.oR.success("统一API测试成功！")):c.oR.error(r.msg||"统一API测试失败")}catch(e){console.error("Unified API test failed:",e),c.oR.error("网络错误")}finally{h(!1)}},w=async()=>{if(!u.trim())return void c.oR.error("请输入提示词");h(!0),x(null);try{let e=await fetch("/api/test/replicate-unified",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:u,options:{output_quality:90,output_format:"webp"}})}),r=await e.json();0===r.code?(x(r.data),c.oR.success("统一API测试成功（无认证）！")):c.oR.error(r.msg||"统一API测试失败（无认证）")}catch(e){console.error("Unified API test (no auth) failed:",e),c.oR.error("网络错误")}finally{h(!1)}};return(0,a.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Replicate Flux Krea Dev 模型测试"})}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"prompt",children:"提示词"}),(0,a.jsx)(n.p,{id:"prompt",value:u,onChange:e=>m(e.target.value),placeholder:"输入图像生成提示词..."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"apiKey",children:"API Key (用于统一API测试)"}),(0,a.jsx)(n.p,{id:"apiKey",type:"password",value:v,onChange:e=>p(e.target.value),placeholder:"输入你的 API Key..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(i.$,{onClick:b,disabled:f,children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试 Replicate Provider"}),(0,a.jsx)(i.$,{onClick:y,disabled:f,variant:"outline",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试统一 AI API"}),(0,a.jsx)(i.$,{onClick:w,disabled:f,variant:"secondary",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试统一 API (无认证)"})]}),g&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"测试结果"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("pre",{className:"bg-muted p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(g,null,2)}),((null==(e=g.response)?void 0:e.urls)||g.urls)&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:"生成的图片："}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:((null==(r=g.response)?void 0:r.urls)||g.urls||[]).map((e,r)=>(0,a.jsx)("img",{src:e,alt:"Generated image ".concat(r+1),className:"w-full h-auto rounded-md border"},r))})]}),(null==(t=g.result)?void 0:t.images)&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:"生成的图片："}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:g.result.images.map((e,r)=>(0,a.jsx)("img",{src:e.url,alt:"Generated image ".concat(r+1),className:"w-full h-auto rounded-md border"},r))})]})]})]})]})]})})}},92402:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var a=t(29207);t(27487);var s=t(59538),i=t(33335);function n(e){let{className:r,...t}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},98656:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(68699).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[2268,7559,3102,7524,7358],()=>r(19951)),_N_E=e.O()}]);