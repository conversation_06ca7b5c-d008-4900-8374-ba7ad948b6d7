(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8450],{4258:()=>{},8527:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>D});var a=r(29207),n=r(69795);function i(){let e="G-DFL8TGPG03";return e?(0,a.jsx)(n.GoogleAnalytics,{gaId:e}):null}function o(){return null}function s(){return null}r(88588);var l=r(26580);function d(){let e="snteb2f8tj";return e?(0,a.jsx)(l.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:'\n    (function(c, l, a, r, i, t, y) {\n      c[a] = c[a] || function() {\n        (c[a].q = c[a].q || []).push(arguments)\n      };\n      t = l.createElement(r);\n      t.async = 1;\n      t.src = "https://www.clarity.ms/tag/" + i;\n      y = l.getElementsByTagName(r)[0];\n      y.parentNode.insertBefore(t, y);\n    })(window, document, "clarity", "script", "'.concat(e,'");\n  ')}):null}function c(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o,{}),(0,a.jsx)(i,{}),(0,a.jsx)(s,{}),(0,a.jsx)(d,{})]})}var u=r(80119),m=r(27487),h=r(93304),f=r(19366),v=r(33335);function g(e){let{...t}=e;return(0,a.jsx)(f._s.Root,{"data-slot":"drawer",...t})}function x(e){let{...t}=e;return(0,a.jsx)(f._s.Portal,{"data-slot":"drawer-portal",...t})}function w(e){let{...t}=e;return(0,a.jsx)(f._s.Close,{"data-slot":"drawer-close",...t})}function p(e){let{className:t,...r}=e;return(0,a.jsx)(f._s.Overlay,{"data-slot":"drawer-overlay",className:(0,v.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function j(e){let{className:t,children:r,...n}=e;return(0,a.jsxs)(x,{"data-slot":"drawer-portal",children:[(0,a.jsx)(p,{}),(0,a.jsxs)(f._s.Content,{"data-slot":"drawer-content",className:(0,v.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",t),...n,children:[(0,a.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),r]})]})}function b(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"drawer-header",className:(0,v.cn)("flex flex-col gap-1.5 p-4",t),...r})}function _(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"drawer-footer",className:(0,v.cn)("mt-auto flex flex-col gap-2 p-4",t),...r})}function y(e){let{className:t,...r}=e;return(0,a.jsx)(f._s.Title,{"data-slot":"drawer-title",className:(0,v.cn)("text-foreground font-semibold",t),...r})}function N(e){let{className:t,...r}=e;return(0,a.jsx)(f._s.Description,{"data-slot":"drawer-description",className:(0,v.cn)("text-muted-foreground text-sm",t),...r})}var k=r(99646),E=r(98656),C=r(70616),P=r(64586),S=r(93450),L=r(19635);function O(){let e=(0,L.c3)(),{showSignModal:t,setShowSignModal:r}=(0,S.U)(),[n,i]=m.useState(!1);return!function(e){let[t,r]=(0,m.useState)(!1);return(0,m.useEffect)(()=>{let t=window.matchMedia(e);r(t.matches);let a=e=>{r(e.matches)};return t.addEventListener("change",a),()=>{t.removeEventListener("change",a)}},[e]),t}("(min-width: 768px)")?(0,a.jsx)(g,{open:t,onOpenChange:r,children:(0,a.jsxs)(j,{children:[(0,a.jsxs)(b,{className:"text-left",children:[(0,a.jsx)(y,{children:e("sign_modal.sign_in_title")}),(0,a.jsx)(N,{children:e("sign_modal.sign_in_description")})]}),(0,a.jsx)(T,{className:"px-4"}),(0,a.jsx)(_,{className:"pt-4",children:(0,a.jsx)(w,{asChild:!0,children:(0,a.jsx)(C.$,{variant:"outline",children:e("sign_modal.cancel_title")})})})]})}):(0,a.jsx)(h.lG,{open:t,onOpenChange:r,children:(0,a.jsxs)(h.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(h.c7,{children:[(0,a.jsx)(h.L3,{children:e("sign_modal.sign_in_title")}),(0,a.jsx)(h.rr,{children:e("sign_modal.sign_in_description")})]}),(0,a.jsx)(T,{})]})})}function T(e){let{className:t}=e,r=(0,L.c3)(),[n,i]=m.useState(!1),[o,s]=m.useState(!1),l=async()=>{i(!0);try{await (0,P.Jv)("google")}catch(e){console.error("Google登录失败:",e),i(!1)}};return(0,a.jsxs)("div",{className:(0,v.cn)("grid items-start gap-4",t),children:[(0,a.jsx)(C.$,{variant:"outline",className:"w-full flex items-center gap-2",onClick:l,disabled:n||o,children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"w-4 h-4 animate-spin"}),r("sign_modal.google_signing_in")]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.UKz,{className:"w-4 h-4"}),r("sign_modal.google_sign_in")]})}),!1]})}var I=r(69940);let F=e=>{let{...t}=e,{theme:r}=(0,S.U)();return(0,a.jsx)(I.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})};var G=r(80912);function D(e){let{children:t}=e,{theme:r,setTheme:n}=(0,S.U)();return(0,m.useEffect)(()=>{let e=(0,G.pr)(u.wD.Theme);if(e){if(["dark","light"].includes(e))return void n(e)}else{let e="light";if(e&&["dark","light"].includes(e))return void n(e)}let t=window.matchMedia("(prefers-color-scheme: dark)");n(t.matches?"dark":"light");let r=()=>{n(t.matches?"dark":"light")};return t.addEventListener("change",r),()=>{t.removeEventListener("change",r)}},[]),(0,m.useEffect)(()=>{{let e=document.body;e.classList.remove("dark","light"),r&&e.classList.add(r)}},[r]),(0,a.jsxs)(a.Fragment,{children:[t,(0,a.jsx)(F,{position:"top-center",richColors:!0}),(0,a.jsx)(O,{}),(0,a.jsx)(c,{})]})}},70759:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4258,23)),Promise.resolve().then(r.bind(r,99779)),Promise.resolve().then(r.bind(r,66483)),Promise.resolve().then(r.bind(r,93450)),Promise.resolve().then(r.bind(r,71339)),Promise.resolve().then(r.bind(r,99385)),Promise.resolve().then(r.t.bind(r,49529,23)),Promise.resolve().then(r.bind(r,8527))},93450:(e,t,r)=>{"use strict";r.d(t,{AppContextProvider:()=>m,U:()=>u});var a=r(29207),n=r(27487),i=r(80912),o=r(80119),s=r(25897),l=r.n(s),d=r(64586);let c=(0,n.createContext)({}),u=()=>(0,n.useContext)(c),m=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,d.wV)(),r=async function(){l()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),i(e.credential)})},i=async function(e){console.log("signIn ok",await (0,d.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,n.useEffect)(()=>{if("unauthenticated"===t){r();let e=setInterval(()=>{r()},3e3);return()=>{clearInterval(e)}}},[t]),a.Fragment}();let{data:r}=(0,d.wV)(),[s,u]=(0,n.useState)(()=>"light"),[m,h]=(0,n.useState)(!1),[f,v]=(0,n.useState)(null),[g,x]=(0,n.useState)(!1),w=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:r,data:a}=await e.json();if(0!==t)throw Error(r);v(a),p(a)}catch(e){console.log("fetch user info failed")}},p=async e=>{try{if(e.invited_by)return void console.log("user already been invited",e.invited_by);let t=(0,i.pr)(o.wD.InviteCode);if(!t)return;let r=moment(e.created_at).unix(),a=moment().unix(),n=Number(a-r);if(n<=0||n>7200)return void console.log("user created more than 2 hours");console.log("update invite",t,e.uuid);let s={invite_code:t,user_uuid:e.uuid},l=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!l.ok)throw Error("update invite failed with status: "+l.status);let{code:d,message:c,data:u}=await l.json();if(0!==d)throw Error(c);v(u),(0,i.np)(o.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,n.useEffect)(()=>{r&&r.user&&w()},[r]),(0,a.jsx)(c.Provider,{value:{theme:s,setTheme:u,showSignModal:m,setShowSignModal:h,user:f,setUser:v,showFeedback:g,setShowFeedback:x},children:t})}},99779:(e,t,r)=>{"use strict";r.d(t,{NextAuthSessionProvider:()=>i});var a=r(29207),n=r(64586);function i(e){let{children:t}=e;return(0,a.jsx)(n.CP,{children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8635,2792,9813,6674,2268,6310,9324,5892,6058,7559,430,9277,6483,3102,7524,7358],()=>t(70759)),_N_E=e.O()}]);