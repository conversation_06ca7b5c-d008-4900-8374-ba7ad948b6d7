(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9143],{2641:(e,t,a)=>{"use strict";a.d(t,{GB:()=>l,IB:()=>r,L$:()=>n,b:()=>i,q:()=>s,u7:()=>o});let r=["en","zh"],n={en:"English",zh:"中文"},s="en",i="as-needed",l=!1,o={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},8357:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(29207),n=a(27487),s=a(89361),i=a(87853),l=a(25668);let o={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.R<PERSON>r,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.LrS,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:i.sOK,GoThumbsup:l.VZG,GoArrowUpRight:l.zny},c=(0,n.memo)(e=>{let{name:t,className:a,onClick:n,...s}=e,i=o[t];return i?(0,r.jsx)(i,{className:a,onClick:n,style:{cursor:n?"pointer":"default"},...s}):null})},41861:(e,t,a)=>{Promise.resolve().then(a.bind(a,58287)),Promise.resolve().then(a.bind(a,82668))},55957:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>o,_2:()=>d,lp:()=>u,mB:()=>m,rI:()=>i,ty:()=>l});var r=a(29207);a(27487);var n=a(23809),s=a(33335);function i(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...i}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function c(e){let{...t}=e;return(0,r.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t})}function d(e){let{className:t,inset:a,variant:i="default",...l}=e;return(0,r.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":i,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function u(e){let{className:t,inset:a,...i}=e;return(0,r.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},58287:(e,t,a)=>{"use strict";a.d(t,{default:()=>_});var r=a(29207),n=a(27487),s=a(82668),i=a(89863),l=a(8357),o=a(52545);function c(e){var t;let{nav:a}=e,n=(0,o.usePathname)();return(0,r.jsx)(s.Cn,{children:(0,r.jsx)(s.rQ,{className:"flex flex-col gap-2 mt-4",children:(0,r.jsx)(s.wZ,{children:null==a||null==(t=a.items)?void 0:t.map(e=>(0,r.jsx)(s.FX,{children:(0,r.jsx)(s.Uj,{tooltip:e.title,className:"".concat(e.is_active||n.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""),children:e.url?(0,r.jsxs)(i.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]}):(0,r.jsxs)(r.Fragment,{children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})})}var d=a(99940),u=a(64487),m=a(92011),x=a(59289),h=a(55957),f=a(70616),g=a(64586),v=a(93450),p=a(19635);function j(e){var t;let{account:a}=e,o=(0,p.c3)(),{user:c,setShowSignModal:d}=(0,v.U)(),{isMobile:j,open:N}=(0,s.cL)();return(0,r.jsx)(r.Fragment,{children:c?(0,r.jsxs)(s.wZ,{className:"gap-4",children:[!N&&(0,r.jsx)(s.FX,{children:(0,r.jsx)(s.Uj,{className:"cursor-pointer",asChild:!0,children:(0,r.jsx)(s.SidebarTrigger,{})})}),(0,r.jsx)(s.FX,{children:(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsxs)(s.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(u.AvatarImage,{src:null==c?void 0:c.avatar_url,alt:null==c?void 0:c.nickname}),(0,r.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:c.nickname}),(0,r.jsx)("span",{className:"truncate text-xs",children:c.email})]}),(0,r.jsx)(m.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(h.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg bg-background",side:j?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(h.lp,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(u.AvatarImage,{src:null==c?void 0:c.avatar_url,alt:null==c?void 0:c.nickname}),(0,r.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:null==c?void 0:c.nickname}),(0,r.jsx)("span",{className:"truncate text-xs",children:null==c?void 0:c.email})]})]})}),(0,r.jsx)(h.mB,{}),(0,r.jsxs)(h.I,{children:[null==a||null==(t=a.items)?void 0:t.map((e,t)=>(0,r.jsxs)(n.Fragment,{children:[(0,r.jsx)(h._2,{className:"cursor-pointer",children:(0,r.jsxs)(i.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),e.title]})}),(0,r.jsx)(h.mB,{})]},t)),(0,r.jsxs)(h._2,{className:"cursor-pointer",onClick:()=>(0,g.CI)(),children:[(0,r.jsx)(x.A,{}),o("user.sign_out")]})]})]})]})})]}):(0,r.jsx)(r.Fragment,{children:N?(0,r.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-4",children:(0,r.jsx)(f.$,{className:"w-full",onClick:()=>d(!0),children:o("user.sign_in")})}):(0,r.jsx)(s.wZ,{children:(0,r.jsx)(s.FX,{children:(0,r.jsx)(s.Uj,{className:"cursor-pointer",asChild:!0,children:(0,r.jsx)(s.SidebarTrigger,{})})})})})})}function N(e){var t;let{social:a}=e,{open:n}=(0,s.cL)();return(0,r.jsx)(r.Fragment,{children:n?(0,r.jsx)("div",{className:"w-full flex items-center justify-center mx-auto gap-x-4 px-4 py-4 border-t border-gray-200",children:null==a||null==(t=a.items)?void 0:t.map((e,t)=>(0,r.jsx)("div",{className:"cursor-pointer hover:text-primary",children:(0,r.jsx)(i.N_,{href:e.url,target:e.target||"_self",className:"cursor-pointer",children:e.icon&&(0,r.jsx)(l.default,{name:e.icon,className:"text-xl"})})},t))}):null})}var b=a(86802);function w(e){var t;let{library:a}=e,{isMobile:n}=(0,s.cL)(),o=(0,i.a8)();return(0,r.jsxs)(s.Cn,{className:"group-data-[collapsible=icon]:hidden",children:[(0,r.jsx)(s.jj,{children:a.title}),(0,r.jsxs)(s.wZ,{children:[null==(t=a.items)?void 0:t.map((e,t)=>(0,r.jsxs)(s.FX,{children:[(0,r.jsx)(s.Uj,{tooltip:e.title,className:"".concat(e.is_active||o.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""),children:(0,r.jsxs)(i.N_,{href:e.url||"",target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})}),(0,r.jsx)(h.rI,{})]},t)),a.more&&(0,r.jsx)(s.FX,{children:(0,r.jsxs)(s.Uj,{className:"text-sidebar-foreground/70",children:[(0,r.jsx)(b.A,{className:"text-sidebar-foreground/70"}),(0,r.jsx)("span",{children:a.more.title})]})})]})]})}function y(e){var t;let{nav:a,...n}=e;return(0,r.jsx)(s.Cn,{...n,children:(0,r.jsx)(s.rQ,{children:(0,r.jsx)(s.wZ,{children:null==(t=a.items)?void 0:t.map((e,t)=>(0,r.jsx)(s.FX,{children:(0,r.jsx)(s.Uj,{asChild:!0,children:(0,r.jsxs)(i.N_,{href:e.url,target:e.target,children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})})},t))})})})}function _(e){var t,a,n,l,o,u;let{sidebar:m,...x}=e;return(0,r.jsxs)(s.Bx,{collapsible:"offcanvas",...x,children:[(0,r.jsx)(s.Gh,{children:(0,r.jsx)(s.wZ,{children:(0,r.jsx)(s.FX,{children:(0,r.jsx)(s.Uj,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:(0,r.jsxs)(i.N_,{href:null==(t=m.brand)?void 0:t.url,className:"flex items-center gap-2",children:[(null==(a=m.brand)?void 0:a.logo)&&(0,r.jsx)(d.default,{src:null==(l=m.brand)||null==(n=l.logo)?void 0:n.src,alt:null==(o=m.brand)?void 0:o.title,width:28,height:28,className:"rounded-full"}),(0,r.jsx)("span",{className:"text-base font-semibold",children:null==(u=m.brand)?void 0:u.title})]})})})})}),(0,r.jsxs)(s.Yv,{children:[m.nav&&(0,r.jsx)(c,{nav:m.nav}),m.library&&(0,r.jsx)(w,{library:m.library}),m.bottomNav&&(0,r.jsx)(y,{nav:m.bottomNav,className:"mt-auto"})]}),(0,r.jsxs)(s.CG,{children:[(0,r.jsx)(j,{account:m.account}),(null==m?void 0:m.social)&&(0,r.jsx)(N,{social:m.social})]})]})}},64487:(e,t,a)=>{"use strict";a.d(t,{Avatar:()=>i,AvatarImage:()=>l,q:()=>o});var r=a(29207);a(27487);var n=a(18230),s=a(33335);function i(e){let{className:t,...a}=e;return(0,r.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full object-cover",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},67699:(e,t,a)=>{"use strict";a.d(t,{lg:()=>r});let r=()=>Date.parse(new Date().toUTCString())/1e3},80119:(e,t,a)=>{"use strict";a.d(t,{wD:()=>r});let r={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}},80912:(e,t,a)=>{"use strict";a.d(t,{PW:()=>s,np:()=>i,pr:()=>n});var r=a(67699);let n=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let n=Number(a[0]),s=(0,r.lg)();if(-1!==n&&n<s)return i(e),null;let l=a[0]+":";return t.replace(l,"")},s=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},i=e=>{localStorage.removeItem(e)}},89863:(e,t,a)=>{"use strict";a.d(t,{N_:()=>i,a8:()=>o,rd:()=>c});var r=a(2641),n=a(30325);let s=(0,a(70361).A)({locales:r.IB,defaultLocale:r.q,localePrefix:r.b,pathnames:r.u7,localeDetection:r.GB}),{Link:i,redirect:l,usePathname:o,useRouter:c}=(0,n.A)(s)},93450:(e,t,a)=>{"use strict";a.d(t,{AppContextProvider:()=>m,U:()=>u});var r=a(29207),n=a(27487),s=a(80912),i=a(80119),l=a(25897),o=a.n(l),c=a(64586);let d=(0,n.createContext)({}),u=()=>(0,n.useContext)(d),m=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,c.wV)(),a=async function(){o()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),s(e.credential)})},s=async function(e){console.log("signIn ok",await (0,c.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,n.useEffect)(()=>{if("unauthenticated"===t){a();let e=setInterval(()=>{a()},3e3);return()=>{clearInterval(e)}}},[t]),r.Fragment}();let{data:a}=(0,c.wV)(),[l,u]=(0,n.useState)(()=>"light"),[m,x]=(0,n.useState)(!1),[h,f]=(0,n.useState)(null),[g,v]=(0,n.useState)(!1),p=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:a,data:r}=await e.json();if(0!==t)throw Error(a);f(r),j(r)}catch(e){console.log("fetch user info failed")}},j=async e=>{try{if(e.invited_by)return void console.log("user already been invited",e.invited_by);let t=(0,s.pr)(i.wD.InviteCode);if(!t)return;let a=moment(e.created_at).unix(),r=moment().unix(),n=Number(r-a);if(n<=0||n>7200)return void console.log("user created more than 2 hours");console.log("update invite",t,e.uuid);let l={invite_code:t,user_uuid:e.uuid},o=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!o.ok)throw Error("update invite failed with status: "+o.status);let{code:c,message:d,data:u}=await o.json();if(0!==c)throw Error(d);f(u),(0,s.np)(i.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,n.useEffect)(()=>{a&&a.user&&p()},[a]),(0,r.jsx)(d.Provider,{value:{theme:l,setTheme:u,showSignModal:m,setShowSignModal:x,user:h,setUser:f,showFeedback:g,setShowFeedback:v},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4474,4056,7885,2268,6310,9324,5892,6058,5562,3696,430,9147,9940,3809,7001,2668,3102,7524,7358],()=>t(41861)),_N_E=e.O()}]);