
import {<PERSON><PERSON><PERSON>} from "node:buffer";
globalThis.Buffer = Buffer;

import {AsyncLocalStorage} from "node:async_hooks";
globalThis.AsyncLocalStorage = AsyncLocalStorage;


const defaultDefineProperty = Object.defineProperty;
Object.defineProperty = function(o, p, a) {
  if(p=== '__import_unsupported' && Boolean(globalThis.__import_unsupported)) {
    return;
  }
  return defaultDefineProperty(o, p, a);
};

  
  
  globalThis.openNextDebug = false;globalThis.openNextVersion = "3.7.2";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/error.js
function isOpenNextError(e) {
  try {
    return "__openNextInternal" in e;
  } catch {
    return false;
  }
}
var init_error = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/error.js"() {
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/adapters/logger.js
function debug(...args) {
  if (globalThis.openNextDebug) {
    console.log(...args);
  }
}
function warn(...args) {
  console.warn(...args);
}
function error(...args) {
  if (args.some((arg) => isDownplayedErrorLog(arg))) {
    return debug(...args);
  }
  if (args.some((arg) => isOpenNextError(arg))) {
    const error2 = args.find((arg) => isOpenNextError(arg));
    if (error2.logLevel < getOpenNextErrorLogLevel()) {
      return;
    }
    if (error2.logLevel === 0) {
      return console.log(...args.map((arg) => isOpenNextError(arg) ? `${arg.name}: ${arg.message}` : arg));
    }
    if (error2.logLevel === 1) {
      return warn(...args.map((arg) => isOpenNextError(arg) ? `${arg.name}: ${arg.message}` : arg));
    }
    return console.error(...args);
  }
  console.error(...args);
}
function getOpenNextErrorLogLevel() {
  const strLevel = process.env.OPEN_NEXT_ERROR_LOG_LEVEL ?? "1";
  switch (strLevel.toLowerCase()) {
    case "debug":
    case "0":
      return 0;
    case "error":
    case "2":
      return 2;
    default:
      return 1;
  }
}
var DOWNPLAYED_ERROR_LOGS, isDownplayedErrorLog;
var init_logger = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/adapters/logger.js"() {
    init_error();
    DOWNPLAYED_ERROR_LOGS = [
      {
        clientName: "S3Client",
        commandName: "GetObjectCommand",
        errorName: "NoSuchKey"
      }
    ];
    isDownplayedErrorLog = (errorLog) => DOWNPLAYED_ERROR_LOGS.some((downplayedInput) => downplayedInput.clientName === errorLog?.clientName && downplayedInput.commandName === errorLog?.commandName && (downplayedInput.errorName === errorLog?.error?.name || downplayedInput.errorName === errorLog?.error?.Code));
  }
});

// node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.parse = parse3;
    exports.serialize = serialize;
    var cookieNameRegExp = /^[\u0021-\u003A\u003C\u003E-\u007E]+$/;
    var cookieValueRegExp = /^[\u0021-\u003A\u003C-\u007E]*$/;
    var domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;
    var pathValueRegExp = /^[\u0020-\u003A\u003D-\u007E]*$/;
    var __toString = Object.prototype.toString;
    var NullObject = /* @__PURE__ */ (() => {
      const C = function() {
      };
      C.prototype = /* @__PURE__ */ Object.create(null);
      return C;
    })();
    function parse3(str, options) {
      const obj = new NullObject();
      const len = str.length;
      if (len < 2)
        return obj;
      const dec = options?.decode || decode;
      let index = 0;
      do {
        const eqIdx = str.indexOf("=", index);
        if (eqIdx === -1)
          break;
        const colonIdx = str.indexOf(";", index);
        const endIdx = colonIdx === -1 ? len : colonIdx;
        if (eqIdx > endIdx) {
          index = str.lastIndexOf(";", eqIdx - 1) + 1;
          continue;
        }
        const keyStartIdx = startIndex(str, index, eqIdx);
        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);
        const key = str.slice(keyStartIdx, keyEndIdx);
        if (obj[key] === void 0) {
          let valStartIdx = startIndex(str, eqIdx + 1, endIdx);
          let valEndIdx = endIndex(str, endIdx, valStartIdx);
          const value = dec(str.slice(valStartIdx, valEndIdx));
          obj[key] = value;
        }
        index = endIdx + 1;
      } while (index < len);
      return obj;
    }
    function startIndex(str, index, max) {
      do {
        const code = str.charCodeAt(index);
        if (code !== 32 && code !== 9)
          return index;
      } while (++index < max);
      return max;
    }
    function endIndex(str, index, min) {
      while (index > min) {
        const code = str.charCodeAt(--index);
        if (code !== 32 && code !== 9)
          return index + 1;
      }
      return min;
    }
    function serialize(name, val, options) {
      const enc = options?.encode || encodeURIComponent;
      if (!cookieNameRegExp.test(name)) {
        throw new TypeError(`argument name is invalid: ${name}`);
      }
      const value = enc(val);
      if (!cookieValueRegExp.test(value)) {
        throw new TypeError(`argument val is invalid: ${val}`);
      }
      let str = name + "=" + value;
      if (!options)
        return str;
      if (options.maxAge !== void 0) {
        if (!Number.isInteger(options.maxAge)) {
          throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);
        }
        str += "; Max-Age=" + options.maxAge;
      }
      if (options.domain) {
        if (!domainValueRegExp.test(options.domain)) {
          throw new TypeError(`option domain is invalid: ${options.domain}`);
        }
        str += "; Domain=" + options.domain;
      }
      if (options.path) {
        if (!pathValueRegExp.test(options.path)) {
          throw new TypeError(`option path is invalid: ${options.path}`);
        }
        str += "; Path=" + options.path;
      }
      if (options.expires) {
        if (!isDate(options.expires) || !Number.isFinite(options.expires.valueOf())) {
          throw new TypeError(`option expires is invalid: ${options.expires}`);
        }
        str += "; Expires=" + options.expires.toUTCString();
      }
      if (options.httpOnly) {
        str += "; HttpOnly";
      }
      if (options.secure) {
        str += "; Secure";
      }
      if (options.partitioned) {
        str += "; Partitioned";
      }
      if (options.priority) {
        const priority = typeof options.priority === "string" ? options.priority.toLowerCase() : void 0;
        switch (priority) {
          case "low":
            str += "; Priority=Low";
            break;
          case "medium":
            str += "; Priority=Medium";
            break;
          case "high":
            str += "; Priority=High";
            break;
          default:
            throw new TypeError(`option priority is invalid: ${options.priority}`);
        }
      }
      if (options.sameSite) {
        const sameSite = typeof options.sameSite === "string" ? options.sameSite.toLowerCase() : options.sameSite;
        switch (sameSite) {
          case true:
          case "strict":
            str += "; SameSite=Strict";
            break;
          case "lax":
            str += "; SameSite=Lax";
            break;
          case "none":
            str += "; SameSite=None";
            break;
          default:
            throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);
        }
      }
      return str;
    }
    function decode(str) {
      if (str.indexOf("%") === -1)
        return str;
      try {
        return decodeURIComponent(str);
      } catch (e) {
        return str;
      }
    }
    function isDate(val) {
      return __toString.call(val) === "[object Date]";
    }
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/http/util.js
function parseSetCookieHeader(cookies) {
  if (!cookies) {
    return [];
  }
  if (typeof cookies === "string") {
    return cookies.split(/(?<!Expires=\w+),/i).map((c) => c.trim());
  }
  return cookies;
}
function getQueryFromIterator(it) {
  const query = {};
  for (const [key, value] of it) {
    if (key in query) {
      if (Array.isArray(query[key])) {
        query[key].push(value);
      } else {
        query[key] = [query[key], value];
      }
    } else {
      query[key] = value;
    }
  }
  return query;
}
var init_util = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/http/util.js"() {
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/converters/utils.js
function getQueryFromSearchParams(searchParams) {
  return getQueryFromIterator(searchParams.entries());
}
var init_utils = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/converters/utils.js"() {
    init_util();
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/converters/edge.js
var edge_exports = {};
__export(edge_exports, {
  default: () => edge_default
});
import { Buffer as Buffer2 } from "node:buffer";
var import_cookie, NULL_BODY_STATUSES, converter, edge_default;
var init_edge = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/converters/edge.js"() {
    import_cookie = __toESM(require_dist(), 1);
    init_util();
    init_utils();
    NULL_BODY_STATUSES = /* @__PURE__ */ new Set([101, 103, 204, 205, 304]);
    converter = {
      convertFrom: async (event) => {
        const url = new URL(event.url);
        const searchParams = url.searchParams;
        const query = getQueryFromSearchParams(searchParams);
        const body = await event.arrayBuffer();
        const headers = {};
        event.headers.forEach((value, key) => {
          headers[key] = value;
        });
        const rawPath = url.pathname;
        const method = event.method;
        const shouldHaveBody = method !== "GET" && method !== "HEAD";
        const cookieHeader = event.headers.get("cookie");
        const cookies = cookieHeader ? import_cookie.default.parse(cookieHeader) : {};
        return {
          type: "core",
          method,
          rawPath,
          url: event.url,
          body: shouldHaveBody ? Buffer2.from(body) : void 0,
          headers,
          remoteAddress: event.headers.get("x-forwarded-for") ?? "::1",
          query,
          cookies
        };
      },
      convertTo: async (result) => {
        if ("internalEvent" in result) {
          const request = new Request(result.internalEvent.url, {
            body: result.internalEvent.body,
            method: result.internalEvent.method,
            headers: {
              ...result.internalEvent.headers,
              "x-forwarded-host": result.internalEvent.headers.host
            }
          });
          if (globalThis.__dangerous_ON_edge_converter_returns_request === true) {
            return request;
          }
          const cfCache = (result.isISR || result.internalEvent.rawPath.startsWith("/_next/image")) && process.env.DISABLE_CACHE !== "true" ? { cacheEverything: true } : {};
          return fetch(request, {
            // This is a hack to make sure that the response is cached by Cloudflare
            // See https://developers.cloudflare.com/workers/examples/cache-using-fetch/#caching-html-resources
            // @ts-expect-error - This is a Cloudflare specific option
            cf: cfCache
          });
        }
        const headers = new Headers();
        for (const [key, value] of Object.entries(result.headers)) {
          if (key === "set-cookie" && typeof value === "string") {
            const cookies = parseSetCookieHeader(value);
            for (const cookie of cookies) {
              headers.append(key, cookie);
            }
            continue;
          }
          if (Array.isArray(value)) {
            for (const v of value) {
              headers.append(key, v);
            }
          } else {
            headers.set(key, value);
          }
        }
        const body = NULL_BODY_STATUSES.has(result.statusCode) ? null : result.body;
        return new Response(body, {
          status: result.statusCode,
          headers
        });
      },
      name: "edge"
    };
    edge_default = converter;
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/wrappers/cloudflare-edge.js
var cloudflare_edge_exports = {};
__export(cloudflare_edge_exports, {
  default: () => cloudflare_edge_default
});
var cfPropNameMapping, handler, cloudflare_edge_default;
var init_cloudflare_edge = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/wrappers/cloudflare-edge.js"() {
    cfPropNameMapping = {
      // The city name is percent-encoded.
      // See https://github.com/vercel/vercel/blob/4cb6143/packages/functions/src/headers.ts#L94C19-L94C37
      city: [encodeURIComponent, "x-open-next-city"],
      country: "x-open-next-country",
      regionCode: "x-open-next-region",
      latitude: "x-open-next-latitude",
      longitude: "x-open-next-longitude"
    };
    handler = async (handler3, converter2) => async (request, env, ctx) => {
      globalThis.process = process;
      for (const [key, value] of Object.entries(env)) {
        if (typeof value === "string") {
          process.env[key] = value;
        }
      }
      const internalEvent = await converter2.convertFrom(request);
      const cfProperties = request.cf;
      for (const [propName, mapping] of Object.entries(cfPropNameMapping)) {
        const propValue = cfProperties?.[propName];
        if (propValue != null) {
          const [encode, headerName] = Array.isArray(mapping) ? mapping : [null, mapping];
          internalEvent.headers[headerName] = encode ? encode(propValue) : propValue;
        }
      }
      const response = await handler3(internalEvent, {
        waitUntil: ctx.waitUntil.bind(ctx)
      });
      const result = await converter2.convertTo(response);
      return result;
    };
    cloudflare_edge_default = {
      wrapper: handler,
      name: "cloudflare-edge",
      supportStreaming: true,
      edgeRuntime: true
    };
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/originResolver/pattern-env.js
var pattern_env_exports = {};
__export(pattern_env_exports, {
  default: () => pattern_env_default
});
var envLoader, pattern_env_default;
var init_pattern_env = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/originResolver/pattern-env.js"() {
    init_logger();
    envLoader = {
      name: "env",
      resolve: async (_path) => {
        try {
          const origin = JSON.parse(process.env.OPEN_NEXT_ORIGIN ?? "{}");
          for (const [key, value] of Object.entries(globalThis.openNextConfig.functions ?? {}).filter(([key2]) => key2 !== "default")) {
            if (value.patterns.some((pattern) => {
              return new RegExp(
                // transform glob pattern to regex
                `/${pattern.replace(/\*\*/g, "(.*)").replace(/\*/g, "([^/]*)").replace(/\//g, "\\/").replace(/\?/g, ".")}`
              ).test(_path);
            })) {
              debug("Using origin", key, value.patterns);
              return origin[key];
            }
          }
          if (_path.startsWith("/_next/image") && origin.imageOptimizer) {
            debug("Using origin", "imageOptimizer", _path);
            return origin.imageOptimizer;
          }
          if (origin.default) {
            debug("Using default origin", origin.default, _path);
            return origin.default;
          }
          return false;
        } catch (e) {
          error("Error while resolving origin", e);
          return false;
        }
      }
    };
    pattern_env_default = envLoader;
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/assetResolver/dummy.js
var dummy_exports = {};
__export(dummy_exports, {
  default: () => dummy_default
});
var resolver, dummy_default;
var init_dummy = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/assetResolver/dummy.js"() {
    resolver = {
      name: "dummy"
    };
    dummy_default = resolver;
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/stream.js
import { Readable } from "node:stream";
function toReadableStream(value, isBase64) {
  return Readable.toWeb(Readable.from(Buffer.from(value, isBase64 ? "base64" : "utf8")));
}
function emptyReadableStream() {
  if (process.env.OPEN_NEXT_FORCE_NON_EMPTY_RESPONSE === "true") {
    return Readable.toWeb(Readable.from([Buffer.from("SOMETHING")]));
  }
  return Readable.toWeb(Readable.from([]));
}
var init_stream = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/stream.js"() {
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/proxyExternalRequest/fetch.js
var fetch_exports = {};
__export(fetch_exports, {
  default: () => fetch_default
});
var fetchProxy, fetch_default;
var init_fetch = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/overrides/proxyExternalRequest/fetch.js"() {
    init_stream();
    fetchProxy = {
      name: "fetch-proxy",
      // @ts-ignore
      proxy: async (internalEvent) => {
        const { url, headers: eventHeaders, method, body } = internalEvent;
        const headers = Object.fromEntries(Object.entries(eventHeaders).filter(([key]) => key.toLowerCase() !== "cf-connecting-ip"));
        const response = await fetch(url, {
          method,
          headers,
          body
        });
        const responseHeaders = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });
        return {
          type: "core",
          headers: responseHeaders,
          statusCode: response.status,
          isBase64Encoded: true,
          body: response.body ?? emptyReadableStream()
        };
      }
    };
    fetch_default = fetchProxy;
  }
});

// .next/server/edge-runtime-webpack.js
var require_edge_runtime_webpack = __commonJS({
  ".next/server/edge-runtime-webpack.js"() {
    "use strict";
    (() => {
      "use strict";
      var e = {}, r = {};
      function t(o) {
        var n = r[o];
        if (void 0 !== n) return n.exports;
        var a = r[o] = { exports: {} }, f = true;
        try {
          e[o](a, a.exports, t), f = false;
        } finally {
          f && delete r[o];
        }
        return a.exports;
      }
      t.m = e, t.amdO = {}, (() => {
        var e2 = [];
        t.O = (r2, o, n, a) => {
          if (o) {
            a = a || 0;
            for (var f = e2.length; f > 0 && e2[f - 1][2] > a; f--) e2[f] = e2[f - 1];
            e2[f] = [o, n, a];
            return;
          }
          for (var i = 1 / 0, f = 0; f < e2.length; f++) {
            for (var [o, n, a] = e2[f], l = true, u = 0; u < o.length; u++) (false & a || i >= a) && Object.keys(t.O).every((e3) => t.O[e3](o[u])) ? o.splice(u--, 1) : (l = false, a < i && (i = a));
            if (l) {
              e2.splice(f--, 1);
              var c = n();
              void 0 !== c && (r2 = c);
            }
          }
          return r2;
        };
      })(), t.n = (e2) => {
        var r2 = e2 && e2.__esModule ? () => e2.default : () => e2;
        return t.d(r2, { a: r2 }), r2;
      }, (() => {
        var e2, r2 = Object.getPrototypeOf ? (e3) => Object.getPrototypeOf(e3) : (e3) => e3.__proto__;
        t.t = function(o, n) {
          if (1 & n && (o = this(o)), 8 & n || "object" == typeof o && o && (4 & n && o.__esModule || 16 & n && "function" == typeof o.then)) return o;
          var a = /* @__PURE__ */ Object.create(null);
          t.r(a);
          var f = {};
          e2 = e2 || [null, r2({}), r2([]), r2(r2)];
          for (var i = 2 & n && o; "object" == typeof i && !~e2.indexOf(i); i = r2(i)) Object.getOwnPropertyNames(i).forEach((e3) => f[e3] = () => o[e3]);
          return f.default = () => o, t.d(a, f), a;
        };
      })(), t.d = (e2, r2) => {
        for (var o in r2) t.o(r2, o) && !t.o(e2, o) && Object.defineProperty(e2, o, { enumerable: true, get: r2[o] });
      }, t.e = () => Promise.resolve(), t.g = function() {
        if ("object" == typeof globalThis) return globalThis;
        try {
          return this || Function("return this")();
        } catch (e2) {
          if ("object" == typeof window) return window;
        }
      }(), t.o = (e2, r2) => Object.prototype.hasOwnProperty.call(e2, r2), t.r = (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      }, (() => {
        var e2 = { 149: 0 };
        t.O.j = (r3) => 0 === e2[r3];
        var r2 = (r3, o2) => {
          var n, a, [f, i, l] = o2, u = 0;
          if (f.some((r4) => 0 !== e2[r4])) {
            for (n in i) t.o(i, n) && (t.m[n] = i[n]);
            if (l) var c = l(t);
          }
          for (r3 && r3(o2); u < f.length; u++) a = f[u], t.o(e2, a) && e2[a] && e2[a][0](), e2[a] = 0;
          return t.O(c);
        }, o = self.webpackChunk_N_E = self.webpackChunk_N_E || [];
        o.forEach(r2.bind(null, 0)), o.push = r2.bind(null, o.push.bind(o));
      })();
    })();
  }
});

// node-built-in-modules:node:buffer
var node_buffer_exports = {};
import * as node_buffer_star from "node:buffer";
var init_node_buffer = __esm({
  "node-built-in-modules:node:buffer"() {
    __reExport(node_buffer_exports, node_buffer_star);
  }
});

// node-built-in-modules:node:async_hooks
var node_async_hooks_exports = {};
import * as node_async_hooks_star from "node:async_hooks";
var init_node_async_hooks = __esm({
  "node-built-in-modules:node:async_hooks"() {
    __reExport(node_async_hooks_exports, node_async_hooks_star);
  }
});

// .next/server/middleware.js
var require_middleware = __commonJS({
  ".next/server/middleware.js"() {
    "use strict";
    (self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[751], { 5: (e, t, r) => {
      "use strict";
      r.d(t, { p: () => a });
      var n = r(441), i = r(149);
      function a(e2) {
        return (0, i.nJ)(e2) || (0, n.RM)(e2);
      }
    }, 49: (e, t, r) => {
      "use strict";
      var n = r(457);
      function i() {
      }
      var a = { d: { f: i, r: function() {
        throw Error("Invalid form element. requestFormReset must be passed a form that was rendered by React.");
      }, D: i, C: i, L: i, m: i, X: i, S: i, M: i }, p: 0, findDOMNode: null };
      if (!n.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE) throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
      function o(e2, t2) {
        return "font" === e2 ? "" : "string" == typeof t2 ? "use-credentials" === t2 ? t2 : "" : void 0;
      }
      t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = a, t.preconnect = function(e2, t2) {
        "string" == typeof e2 && (t2 = t2 ? "string" == typeof (t2 = t2.crossOrigin) ? "use-credentials" === t2 ? t2 : "" : void 0 : null, a.d.C(e2, t2));
      }, t.prefetchDNS = function(e2) {
        "string" == typeof e2 && a.d.D(e2);
      }, t.preinit = function(e2, t2) {
        if ("string" == typeof e2 && t2 && "string" == typeof t2.as) {
          var r2 = t2.as, n2 = o(r2, t2.crossOrigin), i2 = "string" == typeof t2.integrity ? t2.integrity : void 0, s = "string" == typeof t2.fetchPriority ? t2.fetchPriority : void 0;
          "style" === r2 ? a.d.S(e2, "string" == typeof t2.precedence ? t2.precedence : void 0, { crossOrigin: n2, integrity: i2, fetchPriority: s }) : "script" === r2 && a.d.X(e2, { crossOrigin: n2, integrity: i2, fetchPriority: s, nonce: "string" == typeof t2.nonce ? t2.nonce : void 0 });
        }
      }, t.preinitModule = function(e2, t2) {
        if ("string" == typeof e2) if ("object" == typeof t2 && null !== t2) {
          if (null == t2.as || "script" === t2.as) {
            var r2 = o(t2.as, t2.crossOrigin);
            a.d.M(e2, { crossOrigin: r2, integrity: "string" == typeof t2.integrity ? t2.integrity : void 0, nonce: "string" == typeof t2.nonce ? t2.nonce : void 0 });
          }
        } else null == t2 && a.d.M(e2);
      }, t.preload = function(e2, t2) {
        if ("string" == typeof e2 && "object" == typeof t2 && null !== t2 && "string" == typeof t2.as) {
          var r2 = t2.as, n2 = o(r2, t2.crossOrigin);
          a.d.L(e2, r2, { crossOrigin: n2, integrity: "string" == typeof t2.integrity ? t2.integrity : void 0, nonce: "string" == typeof t2.nonce ? t2.nonce : void 0, type: "string" == typeof t2.type ? t2.type : void 0, fetchPriority: "string" == typeof t2.fetchPriority ? t2.fetchPriority : void 0, referrerPolicy: "string" == typeof t2.referrerPolicy ? t2.referrerPolicy : void 0, imageSrcSet: "string" == typeof t2.imageSrcSet ? t2.imageSrcSet : void 0, imageSizes: "string" == typeof t2.imageSizes ? t2.imageSizes : void 0, media: "string" == typeof t2.media ? t2.media : void 0 });
        }
      }, t.preloadModule = function(e2, t2) {
        if ("string" == typeof e2) if (t2) {
          var r2 = o(t2.as, t2.crossOrigin);
          a.d.m(e2, { as: "string" == typeof t2.as && "script" !== t2.as ? t2.as : void 0, crossOrigin: r2, integrity: "string" == typeof t2.integrity ? t2.integrity : void 0 });
        } else a.d.m(e2);
      }, t.version = "19.2.0-canary-3fbfb9ba-20250409";
    }, 51: (e) => {
      "use strict";
      e.exports = JSON.parse('{"workspace":{"title":"AI Workspace","subtitle":"Unleash Unlimited Creativity","fullscreen":"Full Screen","start_create":"Start to create","choose_model":"Choose a model and start your creation"},"generator":{"start":"Start","generating":"Generating...","model_selector":"Select Model","prompt_input":"Enter your prompt","prompt_placeholder":"Describe what you want to create...","options_config":"Configuration Options"},"cost":{"estimated":"Estimated cost","credits":"credits","consumed":"Consumed {amount} credits","not_enough":"Not enough credits, need {shortfall} credits","can_afford":"You have sufficient credits"},"status":{"success":"Generation completed","failed":"Failed to generate","pending":"Processing...","running":"Generating...","progress":"Progress: {percent}%"},"actions":{"view":"View","download":"Download","retry":"Retry","cancel":"Cancel","close":"Close"},"models":{"loading":"Loading...","error":"Failed to load","no_models":"No models available","select_model":"Choose a model","model_selector":"AI Model","model_info":"Model Information"},"results":{"text_result":"Generated Text","image_result":"Generated Image {index}","video_result":"Generated Video","no_result":"No results yet","result_ready":"Generation Result","text_description":"The result of text generation will be displayed here, supporting copy and export","image_description":"The result of image generation will be displayed here, supporting preview and download","video_description":"The result of video generation will be displayed here, supporting play and download"},"errors":{"generation_failed":"Generation failed: {detail}","network_error":"Network error, please try again","invalid_input":"Invalid input, please check your prompt","model_unavailable":"Selected model is currently unavailable","insufficient_credits":"Insufficient credits, please recharge"},"tabs":{"text":"TEXT LLM","image":"IMAGE","video":"VIDEO","audio":"AUDIO"},"toolbar":{"minimize":"Minimize","maximize":"Maximize","exit_fullscreen":"Exit Fullscreen","settings":"Settings"},"credits":{"current_balance":"Current Balance","insufficient":"Insufficient Credits","recharge":"Recharge","usage_info":"Usage Information"},"options":{"image_upload":"Upload Image","reference_image":"Reference Image","first_frame":"First Frame Image","uploading":"Uploading image...","drag_drop":"Click to select or drag image here","drop_to_upload":"Release to upload","file_detected":"Image file detected, release to upload","supported_formats":"Supports JPG, PNG, GIF, WebP formats, max 10MB","max_tokens":"Max Output Length","temperature":"Creativity (0-1)","variants":"Number of Images","image_size":"Image Size","square":"Square","landscape":"Landscape","portrait":"Portrait","1_image":"1 Image","2_images":"2 Images","generate_1":"Generate 1 image","generate_2":"Generate 2 images","square_ratio":"Square (1:1)","landscape_ratio":"Landscape (16:9)","portrait_ratio":"Portrait (9:16)"}}');
    }, 129: (e, t, r) => {
      "use strict";
      let n;
      r.r(t), r.d(t, { default: () => rU });
      var i, a = {};
      async function o() {
        return "_ENTRIES" in globalThis && _ENTRIES.middleware_instrumentation && await _ENTRIES.middleware_instrumentation;
      }
      r.r(a), r.d(a, { config: () => rj, default: () => rM });
      let s = null;
      async function u() {
        if ("phase-production-build" === process.env.NEXT_PHASE) return;
        s || (s = o());
        let e10 = await s;
        if (null == e10 ? void 0 : e10.register) try {
          await e10.register();
        } catch (e11) {
          throw e11.message = `An error occurred while loading instrumentation hook: ${e11.message}`, e11;
        }
      }
      async function l(...e10) {
        let t10 = await o();
        try {
          var r2;
          await (null == t10 || null == (r2 = t10.onRequestError) ? void 0 : r2.call(t10, ...e10));
        } catch (e11) {
          console.error("Error in instrumentation.onRequestError:", e11);
        }
      }
      let c = null;
      function d() {
        return c || (c = u()), c;
      }
      function f(e10) {
        return `The edge runtime does not support Node.js '${e10}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`;
      }
      process !== r.g.process && (process.env = r.g.process.env, r.g.process = process), Object.defineProperty(globalThis, "__import_unsupported", { value: function(e10) {
        let t10 = new Proxy(function() {
        }, { get(t11, r2) {
          if ("then" === r2) return {};
          throw Object.defineProperty(Error(f(e10)), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }, construct() {
          throw Object.defineProperty(Error(f(e10)), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }, apply(r2, n2, i2) {
          if ("function" == typeof i2[0]) return i2[0](t10);
          throw Object.defineProperty(Error(f(e10)), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        } });
        return new Proxy({}, { get: () => t10 });
      }, enumerable: false, configurable: false }), d();
      class p extends Error {
        constructor({ page: e10 }) {
          super(`The middleware "${e10}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `);
        }
      }
      class h extends Error {
        constructor() {
          super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `);
        }
      }
      class _ extends Error {
        constructor() {
          super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `);
        }
      }
      let y = "_N_T_", g = { shared: "shared", reactServerComponents: "rsc", serverSideRendering: "ssr", actionBrowser: "action-browser", apiNode: "api-node", apiEdge: "api-edge", middleware: "middleware", instrument: "instrument", edgeAsset: "edge-asset", appPagesBrowser: "app-pages-browser", pagesDirBrowser: "pages-dir-browser", pagesDirEdge: "pages-dir-edge", pagesDirNode: "pages-dir-node" };
      function m(e10) {
        var t10, r2, n2, i2, a2, o2 = [], s2 = 0;
        function u2() {
          for (; s2 < e10.length && /\s/.test(e10.charAt(s2)); ) s2 += 1;
          return s2 < e10.length;
        }
        for (; s2 < e10.length; ) {
          for (t10 = s2, a2 = false; u2(); ) if ("," === (r2 = e10.charAt(s2))) {
            for (n2 = s2, s2 += 1, u2(), i2 = s2; s2 < e10.length && "=" !== (r2 = e10.charAt(s2)) && ";" !== r2 && "," !== r2; ) s2 += 1;
            s2 < e10.length && "=" === e10.charAt(s2) ? (a2 = true, s2 = i2, o2.push(e10.substring(t10, n2)), t10 = s2) : s2 = n2 + 1;
          } else s2 += 1;
          (!a2 || s2 >= e10.length) && o2.push(e10.substring(t10, e10.length));
        }
        return o2;
      }
      function v(e10) {
        let t10 = {}, r2 = [];
        if (e10) for (let [n2, i2] of e10.entries()) "set-cookie" === n2.toLowerCase() ? (r2.push(...m(i2)), t10[n2] = 1 === r2.length ? r2[0] : r2) : t10[n2] = i2;
        return t10;
      }
      function b(e10) {
        try {
          return String(new URL(String(e10)));
        } catch (t10) {
          throw Object.defineProperty(Error(`URL is malformed "${String(e10)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`, { cause: t10 }), "__NEXT_ERROR_CODE", { value: "E61", enumerable: false, configurable: true });
        }
      }
      ({ ...g, GROUP: { builtinReact: [g.reactServerComponents, g.actionBrowser], serverOnly: [g.reactServerComponents, g.actionBrowser, g.instrument, g.middleware], neutralTarget: [g.apiNode, g.apiEdge], clientOnly: [g.serverSideRendering, g.appPagesBrowser], bundled: [g.reactServerComponents, g.actionBrowser, g.serverSideRendering, g.appPagesBrowser, g.shared, g.instrument, g.middleware], appPages: [g.reactServerComponents, g.serverSideRendering, g.appPagesBrowser, g.actionBrowser] } });
      let w = Symbol("response"), S = Symbol("passThrough"), E = Symbol("waitUntil");
      class C {
        constructor(e10, t10) {
          this[S] = false, this[E] = t10 ? { kind: "external", function: t10 } : { kind: "internal", promises: [] };
        }
        respondWith(e10) {
          this[w] || (this[w] = Promise.resolve(e10));
        }
        passThroughOnException() {
          this[S] = true;
        }
        waitUntil(e10) {
          if ("external" === this[E].kind) return (0, this[E].function)(e10);
          this[E].promises.push(e10);
        }
      }
      class R extends C {
        constructor(e10) {
          var t10;
          super(e10.request, null == (t10 = e10.context) ? void 0 : t10.waitUntil), this.sourcePage = e10.page;
        }
        get request() {
          throw Object.defineProperty(new p({ page: this.sourcePage }), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }
        respondWith() {
          throw Object.defineProperty(new p({ page: this.sourcePage }), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }
      }
      function x(e10) {
        return e10.replace(/\/$/, "") || "/";
      }
      function T(e10) {
        let t10 = e10.indexOf("#"), r2 = e10.indexOf("?"), n2 = r2 > -1 && (t10 < 0 || r2 < t10);
        return n2 || t10 > -1 ? { pathname: e10.substring(0, n2 ? r2 : t10), query: n2 ? e10.substring(r2, t10 > -1 ? t10 : void 0) : "", hash: t10 > -1 ? e10.slice(t10) : "" } : { pathname: e10, query: "", hash: "" };
      }
      function k(e10, t10) {
        if (!e10.startsWith("/") || !t10) return e10;
        let { pathname: r2, query: n2, hash: i2 } = T(e10);
        return "" + t10 + r2 + n2 + i2;
      }
      function O(e10, t10) {
        if (!e10.startsWith("/") || !t10) return e10;
        let { pathname: r2, query: n2, hash: i2 } = T(e10);
        return "" + r2 + t10 + n2 + i2;
      }
      function P(e10, t10) {
        if ("string" != typeof e10) return false;
        let { pathname: r2 } = T(e10);
        return r2 === t10 || r2.startsWith(t10 + "/");
      }
      let A = /* @__PURE__ */ new WeakMap();
      function N(e10, t10) {
        let r2;
        if (!t10) return { pathname: e10 };
        let n2 = A.get(t10);
        n2 || (n2 = t10.map((e11) => e11.toLowerCase()), A.set(t10, n2));
        let i2 = e10.split("/", 2);
        if (!i2[1]) return { pathname: e10 };
        let a2 = i2[1].toLowerCase(), o2 = n2.indexOf(a2);
        return o2 < 0 ? { pathname: e10 } : (r2 = t10[o2], { pathname: e10 = e10.slice(r2.length + 1) || "/", detectedLocale: r2 });
      }
      let L = /(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;
      function I(e10, t10) {
        return new URL(String(e10).replace(L, "localhost"), t10 && String(t10).replace(L, "localhost"));
      }
      let M = Symbol("NextURLInternal");
      class j {
        constructor(e10, t10, r2) {
          let n2, i2;
          "object" == typeof t10 && "pathname" in t10 || "string" == typeof t10 ? (n2 = t10, i2 = r2 || {}) : i2 = r2 || t10 || {}, this[M] = { url: I(e10, n2 ?? i2.base), options: i2, basePath: "" }, this.analyze();
        }
        analyze() {
          var e10, t10, r2, n2, i2;
          let a2 = function(e11, t11) {
            var r3, n3;
            let { basePath: i3, i18n: a3, trailingSlash: o3 } = null != (r3 = t11.nextConfig) ? r3 : {}, s3 = { pathname: e11, trailingSlash: "/" !== e11 ? e11.endsWith("/") : o3 };
            i3 && P(s3.pathname, i3) && (s3.pathname = function(e12, t12) {
              if (!P(e12, t12)) return e12;
              let r4 = e12.slice(t12.length);
              return r4.startsWith("/") ? r4 : "/" + r4;
            }(s3.pathname, i3), s3.basePath = i3);
            let u2 = s3.pathname;
            if (s3.pathname.startsWith("/_next/data/") && s3.pathname.endsWith(".json")) {
              let e12 = s3.pathname.replace(/^\/_next\/data\//, "").replace(/\.json$/, "").split("/");
              s3.buildId = e12[0], u2 = "index" !== e12[1] ? "/" + e12.slice(1).join("/") : "/", true === t11.parseData && (s3.pathname = u2);
            }
            if (a3) {
              let e12 = t11.i18nProvider ? t11.i18nProvider.analyze(s3.pathname) : N(s3.pathname, a3.locales);
              s3.locale = e12.detectedLocale, s3.pathname = null != (n3 = e12.pathname) ? n3 : s3.pathname, !e12.detectedLocale && s3.buildId && (e12 = t11.i18nProvider ? t11.i18nProvider.analyze(u2) : N(u2, a3.locales)).detectedLocale && (s3.locale = e12.detectedLocale);
            }
            return s3;
          }(this[M].url.pathname, { nextConfig: this[M].options.nextConfig, parseData: true, i18nProvider: this[M].options.i18nProvider }), o2 = function(e11, t11) {
            let r3;
            if ((null == t11 ? void 0 : t11.host) && !Array.isArray(t11.host)) r3 = t11.host.toString().split(":", 1)[0];
            else {
              if (!e11.hostname) return;
              r3 = e11.hostname;
            }
            return r3.toLowerCase();
          }(this[M].url, this[M].options.headers);
          this[M].domainLocale = this[M].options.i18nProvider ? this[M].options.i18nProvider.detectDomainLocale(o2) : function(e11, t11, r3) {
            if (e11) for (let a3 of (r3 && (r3 = r3.toLowerCase()), e11)) {
              var n3, i3;
              if (t11 === (null == (n3 = a3.domain) ? void 0 : n3.split(":", 1)[0].toLowerCase()) || r3 === a3.defaultLocale.toLowerCase() || (null == (i3 = a3.locales) ? void 0 : i3.some((e12) => e12.toLowerCase() === r3))) return a3;
            }
          }(null == (t10 = this[M].options.nextConfig) || null == (e10 = t10.i18n) ? void 0 : e10.domains, o2);
          let s2 = (null == (r2 = this[M].domainLocale) ? void 0 : r2.defaultLocale) || (null == (i2 = this[M].options.nextConfig) || null == (n2 = i2.i18n) ? void 0 : n2.defaultLocale);
          this[M].url.pathname = a2.pathname, this[M].defaultLocale = s2, this[M].basePath = a2.basePath ?? "", this[M].buildId = a2.buildId, this[M].locale = a2.locale ?? s2, this[M].trailingSlash = a2.trailingSlash;
        }
        formatPathname() {
          var e10;
          let t10;
          return t10 = function(e11, t11, r2, n2) {
            if (!t11 || t11 === r2) return e11;
            let i2 = e11.toLowerCase();
            return !n2 && (P(i2, "/api") || P(i2, "/" + t11.toLowerCase())) ? e11 : k(e11, "/" + t11);
          }((e10 = { basePath: this[M].basePath, buildId: this[M].buildId, defaultLocale: this[M].options.forceLocale ? void 0 : this[M].defaultLocale, locale: this[M].locale, pathname: this[M].url.pathname, trailingSlash: this[M].trailingSlash }).pathname, e10.locale, e10.buildId ? void 0 : e10.defaultLocale, e10.ignorePrefix), (e10.buildId || !e10.trailingSlash) && (t10 = x(t10)), e10.buildId && (t10 = O(k(t10, "/_next/data/" + e10.buildId), "/" === e10.pathname ? "index.json" : ".json")), t10 = k(t10, e10.basePath), !e10.buildId && e10.trailingSlash ? t10.endsWith("/") ? t10 : O(t10, "/") : x(t10);
        }
        formatSearch() {
          return this[M].url.search;
        }
        get buildId() {
          return this[M].buildId;
        }
        set buildId(e10) {
          this[M].buildId = e10;
        }
        get locale() {
          return this[M].locale ?? "";
        }
        set locale(e10) {
          var t10, r2;
          if (!this[M].locale || !(null == (r2 = this[M].options.nextConfig) || null == (t10 = r2.i18n) ? void 0 : t10.locales.includes(e10))) throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e10}"`), "__NEXT_ERROR_CODE", { value: "E597", enumerable: false, configurable: true });
          this[M].locale = e10;
        }
        get defaultLocale() {
          return this[M].defaultLocale;
        }
        get domainLocale() {
          return this[M].domainLocale;
        }
        get searchParams() {
          return this[M].url.searchParams;
        }
        get host() {
          return this[M].url.host;
        }
        set host(e10) {
          this[M].url.host = e10;
        }
        get hostname() {
          return this[M].url.hostname;
        }
        set hostname(e10) {
          this[M].url.hostname = e10;
        }
        get port() {
          return this[M].url.port;
        }
        set port(e10) {
          this[M].url.port = e10;
        }
        get protocol() {
          return this[M].url.protocol;
        }
        set protocol(e10) {
          this[M].url.protocol = e10;
        }
        get href() {
          let e10 = this.formatPathname(), t10 = this.formatSearch();
          return `${this.protocol}//${this.host}${e10}${t10}${this.hash}`;
        }
        set href(e10) {
          this[M].url = I(e10), this.analyze();
        }
        get origin() {
          return this[M].url.origin;
        }
        get pathname() {
          return this[M].url.pathname;
        }
        set pathname(e10) {
          this[M].url.pathname = e10;
        }
        get hash() {
          return this[M].url.hash;
        }
        set hash(e10) {
          this[M].url.hash = e10;
        }
        get search() {
          return this[M].url.search;
        }
        set search(e10) {
          this[M].url.search = e10;
        }
        get password() {
          return this[M].url.password;
        }
        set password(e10) {
          this[M].url.password = e10;
        }
        get username() {
          return this[M].url.username;
        }
        set username(e10) {
          this[M].url.username = e10;
        }
        get basePath() {
          return this[M].basePath;
        }
        set basePath(e10) {
          this[M].basePath = e10.startsWith("/") ? e10 : `/${e10}`;
        }
        toString() {
          return this.href;
        }
        toJSON() {
          return this.href;
        }
        [Symbol.for("edge-runtime.inspect.custom")]() {
          return { href: this.href, origin: this.origin, protocol: this.protocol, username: this.username, password: this.password, host: this.host, hostname: this.hostname, port: this.port, pathname: this.pathname, search: this.search, searchParams: this.searchParams, hash: this.hash };
        }
        clone() {
          return new j(String(this), this[M].options);
        }
      }
      var $ = r(446);
      let D = Symbol("internal request");
      class q extends Request {
        constructor(e10, t10 = {}) {
          let r2 = "string" != typeof e10 && "url" in e10 ? e10.url : String(e10);
          b(r2), e10 instanceof Request ? super(e10, t10) : super(r2, t10);
          let n2 = new j(r2, { headers: v(this.headers), nextConfig: t10.nextConfig });
          this[D] = { cookies: new $.RequestCookies(this.headers), nextUrl: n2, url: n2.toString() };
        }
        [Symbol.for("edge-runtime.inspect.custom")]() {
          return { cookies: this.cookies, nextUrl: this.nextUrl, url: this.url, bodyUsed: this.bodyUsed, cache: this.cache, credentials: this.credentials, destination: this.destination, headers: Object.fromEntries(this.headers), integrity: this.integrity, keepalive: this.keepalive, method: this.method, mode: this.mode, redirect: this.redirect, referrer: this.referrer, referrerPolicy: this.referrerPolicy, signal: this.signal };
        }
        get cookies() {
          return this[D].cookies;
        }
        get nextUrl() {
          return this[D].nextUrl;
        }
        get page() {
          throw new h();
        }
        get ua() {
          throw new _();
        }
        get url() {
          return this[D].url;
        }
      }
      class U {
        static get(e10, t10, r2) {
          let n2 = Reflect.get(e10, t10, r2);
          return "function" == typeof n2 ? n2.bind(e10) : n2;
        }
        static set(e10, t10, r2, n2) {
          return Reflect.set(e10, t10, r2, n2);
        }
        static has(e10, t10) {
          return Reflect.has(e10, t10);
        }
        static deleteProperty(e10, t10) {
          return Reflect.deleteProperty(e10, t10);
        }
      }
      let G = Symbol("internal response"), B = /* @__PURE__ */ new Set([301, 302, 303, 307, 308]);
      function H(e10, t10) {
        var r2;
        if (null == e10 || null == (r2 = e10.request) ? void 0 : r2.headers) {
          if (!(e10.request.headers instanceof Headers)) throw Object.defineProperty(Error("request.headers must be an instance of Headers"), "__NEXT_ERROR_CODE", { value: "E119", enumerable: false, configurable: true });
          let r3 = [];
          for (let [n2, i2] of e10.request.headers) t10.set("x-middleware-request-" + n2, i2), r3.push(n2);
          t10.set("x-middleware-override-headers", r3.join(","));
        }
      }
      class z extends Response {
        constructor(e10, t10 = {}) {
          super(e10, t10);
          let r2 = this.headers, n2 = new Proxy(new $.ResponseCookies(r2), { get(e11, n3, i2) {
            switch (n3) {
              case "delete":
              case "set":
                return (...i3) => {
                  let a2 = Reflect.apply(e11[n3], e11, i3), o2 = new Headers(r2);
                  return a2 instanceof $.ResponseCookies && r2.set("x-middleware-set-cookie", a2.getAll().map((e12) => (0, $.stringifyCookie)(e12)).join(",")), H(t10, o2), a2;
                };
              default:
                return U.get(e11, n3, i2);
            }
          } });
          this[G] = { cookies: n2, url: t10.url ? new j(t10.url, { headers: v(r2), nextConfig: t10.nextConfig }) : void 0 };
        }
        [Symbol.for("edge-runtime.inspect.custom")]() {
          return { cookies: this.cookies, url: this.url, body: this.body, bodyUsed: this.bodyUsed, headers: Object.fromEntries(this.headers), ok: this.ok, redirected: this.redirected, status: this.status, statusText: this.statusText, type: this.type };
        }
        get cookies() {
          return this[G].cookies;
        }
        static json(e10, t10) {
          let r2 = Response.json(e10, t10);
          return new z(r2.body, r2);
        }
        static redirect(e10, t10) {
          let r2 = "number" == typeof t10 ? t10 : (null == t10 ? void 0 : t10.status) ?? 307;
          if (!B.has(r2)) throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'), "__NEXT_ERROR_CODE", { value: "E529", enumerable: false, configurable: true });
          let n2 = "object" == typeof t10 ? t10 : {}, i2 = new Headers(null == n2 ? void 0 : n2.headers);
          return i2.set("Location", b(e10)), new z(null, { ...n2, headers: i2, status: r2 });
        }
        static rewrite(e10, t10) {
          let r2 = new Headers(null == t10 ? void 0 : t10.headers);
          return r2.set("x-middleware-rewrite", b(e10)), H(t10, r2), new z(null, { ...t10, headers: r2 });
        }
        static next(e10) {
          let t10 = new Headers(null == e10 ? void 0 : e10.headers);
          return t10.set("x-middleware-next", "1"), H(e10, t10), new z(null, { ...e10, headers: t10 });
        }
      }
      function F(e10, t10) {
        let r2 = "string" == typeof t10 ? new URL(t10) : t10, n2 = new URL(e10, t10), i2 = n2.origin === r2.origin;
        return { url: i2 ? n2.toString().slice(r2.origin.length) : n2.toString(), isRelative: i2 };
      }
      let W = "Next-Router-Prefetch", V = ["RSC", "Next-Router-State-Tree", W, "Next-HMR-Refresh", "Next-Router-Segment-Prefetch"], K = "_rsc";
      class X extends Error {
        constructor() {
          super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers");
        }
        static callable() {
          throw new X();
        }
      }
      class Y extends Headers {
        constructor(e10) {
          super(), this.headers = new Proxy(e10, { get(t10, r2, n2) {
            if ("symbol" == typeof r2) return U.get(t10, r2, n2);
            let i2 = r2.toLowerCase(), a2 = Object.keys(e10).find((e11) => e11.toLowerCase() === i2);
            if (void 0 !== a2) return U.get(t10, a2, n2);
          }, set(t10, r2, n2, i2) {
            if ("symbol" == typeof r2) return U.set(t10, r2, n2, i2);
            let a2 = r2.toLowerCase(), o2 = Object.keys(e10).find((e11) => e11.toLowerCase() === a2);
            return U.set(t10, o2 ?? r2, n2, i2);
          }, has(t10, r2) {
            if ("symbol" == typeof r2) return U.has(t10, r2);
            let n2 = r2.toLowerCase(), i2 = Object.keys(e10).find((e11) => e11.toLowerCase() === n2);
            return void 0 !== i2 && U.has(t10, i2);
          }, deleteProperty(t10, r2) {
            if ("symbol" == typeof r2) return U.deleteProperty(t10, r2);
            let n2 = r2.toLowerCase(), i2 = Object.keys(e10).find((e11) => e11.toLowerCase() === n2);
            return void 0 === i2 || U.deleteProperty(t10, i2);
          } });
        }
        static seal(e10) {
          return new Proxy(e10, { get(e11, t10, r2) {
            switch (t10) {
              case "append":
              case "delete":
              case "set":
                return X.callable;
              default:
                return U.get(e11, t10, r2);
            }
          } });
        }
        merge(e10) {
          return Array.isArray(e10) ? e10.join(", ") : e10;
        }
        static from(e10) {
          return e10 instanceof Headers ? e10 : new Y(e10);
        }
        append(e10, t10) {
          let r2 = this.headers[e10];
          "string" == typeof r2 ? this.headers[e10] = [r2, t10] : Array.isArray(r2) ? r2.push(t10) : this.headers[e10] = t10;
        }
        delete(e10) {
          delete this.headers[e10];
        }
        get(e10) {
          let t10 = this.headers[e10];
          return void 0 !== t10 ? this.merge(t10) : null;
        }
        has(e10) {
          return void 0 !== this.headers[e10];
        }
        set(e10, t10) {
          this.headers[e10] = t10;
        }
        forEach(e10, t10) {
          for (let [r2, n2] of this.entries()) e10.call(t10, n2, r2, this);
        }
        *entries() {
          for (let e10 of Object.keys(this.headers)) {
            let t10 = e10.toLowerCase(), r2 = this.get(t10);
            yield [t10, r2];
          }
        }
        *keys() {
          for (let e10 of Object.keys(this.headers)) {
            let t10 = e10.toLowerCase();
            yield t10;
          }
        }
        *values() {
          for (let e10 of Object.keys(this.headers)) {
            let t10 = this.get(e10);
            yield t10;
          }
        }
        [Symbol.iterator]() {
          return this.entries();
        }
      }
      var J = r(989), Z = r(213);
      class Q extends Error {
        constructor() {
          super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options");
        }
        static callable() {
          throw new Q();
        }
      }
      class ee {
        static seal(e10) {
          return new Proxy(e10, { get(e11, t10, r2) {
            switch (t10) {
              case "clear":
              case "delete":
              case "set":
                return Q.callable;
              default:
                return U.get(e11, t10, r2);
            }
          } });
        }
      }
      let et = Symbol.for("next.mutated.cookies");
      class er {
        static wrap(e10, t10) {
          let r2 = new $.ResponseCookies(new Headers());
          for (let t11 of e10.getAll()) r2.set(t11);
          let n2 = [], i2 = /* @__PURE__ */ new Set(), a2 = () => {
            let e11 = J.J.getStore();
            if (e11 && (e11.pathWasRevalidated = true), n2 = r2.getAll().filter((e12) => i2.has(e12.name)), t10) {
              let e12 = [];
              for (let t11 of n2) {
                let r3 = new $.ResponseCookies(new Headers());
                r3.set(t11), e12.push(r3.toString());
              }
              t10(e12);
            }
          }, o2 = new Proxy(r2, { get(e11, t11, r3) {
            switch (t11) {
              case et:
                return n2;
              case "delete":
                return function(...t12) {
                  i2.add("string" == typeof t12[0] ? t12[0] : t12[0].name);
                  try {
                    return e11.delete(...t12), o2;
                  } finally {
                    a2();
                  }
                };
              case "set":
                return function(...t12) {
                  i2.add("string" == typeof t12[0] ? t12[0] : t12[0].name);
                  try {
                    return e11.set(...t12), o2;
                  } finally {
                    a2();
                  }
                };
              default:
                return U.get(e11, t11, r3);
            }
          } });
          return o2;
        }
      }
      function en(e10) {
        if ("action" !== (0, Z.XN)(e10).phase) throw new Q();
      }
      var ei = function(e10) {
        return e10.handleRequest = "BaseServer.handleRequest", e10.run = "BaseServer.run", e10.pipe = "BaseServer.pipe", e10.getStaticHTML = "BaseServer.getStaticHTML", e10.render = "BaseServer.render", e10.renderToResponseWithComponents = "BaseServer.renderToResponseWithComponents", e10.renderToResponse = "BaseServer.renderToResponse", e10.renderToHTML = "BaseServer.renderToHTML", e10.renderError = "BaseServer.renderError", e10.renderErrorToResponse = "BaseServer.renderErrorToResponse", e10.renderErrorToHTML = "BaseServer.renderErrorToHTML", e10.render404 = "BaseServer.render404", e10;
      }(ei || {}), ea = function(e10) {
        return e10.loadDefaultErrorComponents = "LoadComponents.loadDefaultErrorComponents", e10.loadComponents = "LoadComponents.loadComponents", e10;
      }(ea || {}), eo = function(e10) {
        return e10.getRequestHandler = "NextServer.getRequestHandler", e10.getServer = "NextServer.getServer", e10.getServerRequestHandler = "NextServer.getServerRequestHandler", e10.createServer = "createServer.createServer", e10;
      }(eo || {}), es = function(e10) {
        return e10.compression = "NextNodeServer.compression", e10.getBuildId = "NextNodeServer.getBuildId", e10.createComponentTree = "NextNodeServer.createComponentTree", e10.clientComponentLoading = "NextNodeServer.clientComponentLoading", e10.getLayoutOrPageModule = "NextNodeServer.getLayoutOrPageModule", e10.generateStaticRoutes = "NextNodeServer.generateStaticRoutes", e10.generateFsStaticRoutes = "NextNodeServer.generateFsStaticRoutes", e10.generatePublicRoutes = "NextNodeServer.generatePublicRoutes", e10.generateImageRoutes = "NextNodeServer.generateImageRoutes.route", e10.sendRenderResult = "NextNodeServer.sendRenderResult", e10.proxyRequest = "NextNodeServer.proxyRequest", e10.runApi = "NextNodeServer.runApi", e10.render = "NextNodeServer.render", e10.renderHTML = "NextNodeServer.renderHTML", e10.imageOptimizer = "NextNodeServer.imageOptimizer", e10.getPagePath = "NextNodeServer.getPagePath", e10.getRoutesManifest = "NextNodeServer.getRoutesManifest", e10.findPageComponents = "NextNodeServer.findPageComponents", e10.getFontManifest = "NextNodeServer.getFontManifest", e10.getServerComponentManifest = "NextNodeServer.getServerComponentManifest", e10.getRequestHandler = "NextNodeServer.getRequestHandler", e10.renderToHTML = "NextNodeServer.renderToHTML", e10.renderError = "NextNodeServer.renderError", e10.renderErrorToHTML = "NextNodeServer.renderErrorToHTML", e10.render404 = "NextNodeServer.render404", e10.startResponse = "NextNodeServer.startResponse", e10.route = "route", e10.onProxyReq = "onProxyReq", e10.apiResolver = "apiResolver", e10.internalFetch = "internalFetch", e10;
      }(es || {}), eu = function(e10) {
        return e10.startServer = "startServer.startServer", e10;
      }(eu || {}), el = function(e10) {
        return e10.getServerSideProps = "Render.getServerSideProps", e10.getStaticProps = "Render.getStaticProps", e10.renderToString = "Render.renderToString", e10.renderDocument = "Render.renderDocument", e10.createBodyResult = "Render.createBodyResult", e10;
      }(el || {}), ec = function(e10) {
        return e10.renderToString = "AppRender.renderToString", e10.renderToReadableStream = "AppRender.renderToReadableStream", e10.getBodyResult = "AppRender.getBodyResult", e10.fetch = "AppRender.fetch", e10;
      }(ec || {}), ed = function(e10) {
        return e10.executeRoute = "Router.executeRoute", e10;
      }(ed || {}), ef = function(e10) {
        return e10.runHandler = "Node.runHandler", e10;
      }(ef || {}), ep = function(e10) {
        return e10.runHandler = "AppRouteRouteHandlers.runHandler", e10;
      }(ep || {}), eh = function(e10) {
        return e10.generateMetadata = "ResolveMetadata.generateMetadata", e10.generateViewport = "ResolveMetadata.generateViewport", e10;
      }(eh || {}), e_ = function(e10) {
        return e10.execute = "Middleware.execute", e10;
      }(e_ || {});
      let ey = ["Middleware.execute", "BaseServer.handleRequest", "Render.getServerSideProps", "Render.getStaticProps", "AppRender.fetch", "AppRender.getBodyResult", "Render.renderDocument", "Node.runHandler", "AppRouteRouteHandlers.runHandler", "ResolveMetadata.generateMetadata", "ResolveMetadata.generateViewport", "NextNodeServer.createComponentTree", "NextNodeServer.findPageComponents", "NextNodeServer.getLayoutOrPageModule", "NextNodeServer.startResponse", "NextNodeServer.clientComponentLoading"], eg = ["NextNodeServer.findPageComponents", "NextNodeServer.createComponentTree", "NextNodeServer.clientComponentLoading"];
      function em(e10) {
        return null !== e10 && "object" == typeof e10 && "then" in e10 && "function" == typeof e10.then;
      }
      let { context: ev, propagation: eb, trace: ew, SpanStatusCode: eS, SpanKind: eE, ROOT_CONTEXT: eC } = n = r(338);
      class eR extends Error {
        constructor(e10, t10) {
          super(), this.bubble = e10, this.result = t10;
        }
      }
      let ex = (e10, t10) => {
        (function(e11) {
          return "object" == typeof e11 && null !== e11 && e11 instanceof eR;
        })(t10) && t10.bubble ? e10.setAttribute("next.bubble", true) : (t10 && e10.recordException(t10), e10.setStatus({ code: eS.ERROR, message: null == t10 ? void 0 : t10.message })), e10.end();
      }, eT = /* @__PURE__ */ new Map(), ek = n.createContextKey("next.rootSpanId"), eO = 0, eP = () => eO++, eA = { set(e10, t10, r2) {
        e10.push({ key: t10, value: r2 });
      } };
      class eN {
        getTracerInstance() {
          return ew.getTracer("next.js", "0.0.1");
        }
        getContext() {
          return ev;
        }
        getTracePropagationData() {
          let e10 = ev.active(), t10 = [];
          return eb.inject(e10, t10, eA), t10;
        }
        getActiveScopeSpan() {
          return ew.getSpan(null == ev ? void 0 : ev.active());
        }
        withPropagatedContext(e10, t10, r2) {
          let n2 = ev.active();
          if (ew.getSpanContext(n2)) return t10();
          let i2 = eb.extract(n2, e10, r2);
          return ev.with(i2, t10);
        }
        trace(...e10) {
          var t10;
          let [r2, n2, i2] = e10, { fn: a2, options: o2 } = "function" == typeof n2 ? { fn: n2, options: {} } : { fn: i2, options: { ...n2 } }, s2 = o2.spanName ?? r2;
          if (!ey.includes(r2) && "1" !== process.env.NEXT_OTEL_VERBOSE || o2.hideSpan) return a2();
          let u2 = this.getSpanContext((null == o2 ? void 0 : o2.parentSpan) ?? this.getActiveScopeSpan()), l2 = false;
          u2 ? (null == (t10 = ew.getSpanContext(u2)) ? void 0 : t10.isRemote) && (l2 = true) : (u2 = (null == ev ? void 0 : ev.active()) ?? eC, l2 = true);
          let c2 = eP();
          return o2.attributes = { "next.span_name": s2, "next.span_type": r2, ...o2.attributes }, ev.with(u2.setValue(ek, c2), () => this.getTracerInstance().startActiveSpan(s2, o2, (e11) => {
            let t11 = "performance" in globalThis && "measure" in performance ? globalThis.performance.now() : void 0, n3 = () => {
              eT.delete(c2), t11 && process.env.NEXT_OTEL_PERFORMANCE_PREFIX && eg.includes(r2 || "") && performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r2.split(".").pop() || "").replace(/[A-Z]/g, (e12) => "-" + e12.toLowerCase())}`, { start: t11, end: performance.now() });
            };
            l2 && eT.set(c2, new Map(Object.entries(o2.attributes ?? {})));
            try {
              if (a2.length > 1) return a2(e11, (t13) => ex(e11, t13));
              let t12 = a2(e11);
              if (em(t12)) return t12.then((t13) => (e11.end(), t13)).catch((t13) => {
                throw ex(e11, t13), t13;
              }).finally(n3);
              return e11.end(), n3(), t12;
            } catch (t12) {
              throw ex(e11, t12), n3(), t12;
            }
          }));
        }
        wrap(...e10) {
          let t10 = this, [r2, n2, i2] = 3 === e10.length ? e10 : [e10[0], {}, e10[1]];
          return ey.includes(r2) || "1" === process.env.NEXT_OTEL_VERBOSE ? function() {
            let e11 = n2;
            "function" == typeof e11 && "function" == typeof i2 && (e11 = e11.apply(this, arguments));
            let a2 = arguments.length - 1, o2 = arguments[a2];
            if ("function" != typeof o2) return t10.trace(r2, e11, () => i2.apply(this, arguments));
            {
              let n3 = t10.getContext().bind(ev.active(), o2);
              return t10.trace(r2, e11, (e12, t11) => (arguments[a2] = function(e13) {
                return null == t11 || t11(e13), n3.apply(this, arguments);
              }, i2.apply(this, arguments)));
            }
          } : i2;
        }
        startSpan(...e10) {
          let [t10, r2] = e10, n2 = this.getSpanContext((null == r2 ? void 0 : r2.parentSpan) ?? this.getActiveScopeSpan());
          return this.getTracerInstance().startSpan(t10, r2, n2);
        }
        getSpanContext(e10) {
          return e10 ? ew.setSpan(ev.active(), e10) : void 0;
        }
        getRootSpanAttributes() {
          let e10 = ev.active().getValue(ek);
          return eT.get(e10);
        }
        setRootSpanAttribute(e10, t10) {
          let r2 = ev.active().getValue(ek), n2 = eT.get(r2);
          n2 && n2.set(e10, t10);
        }
      }
      let eL = (() => {
        let e10 = new eN();
        return () => e10;
      })(), eI = "__prerender_bypass";
      Symbol("__next_preview_data"), Symbol(eI);
      class eM {
        constructor(e10, t10, r2, n2) {
          var i2;
          let a2 = e10 && function(e11, t11) {
            let r3 = Y.from(e11.headers);
            return { isOnDemandRevalidate: r3.get("x-prerender-revalidate") === t11.previewModeId, revalidateOnlyGenerated: r3.has("x-prerender-revalidate-if-generated") };
          }(t10, e10).isOnDemandRevalidate, o2 = null == (i2 = r2.get(eI)) ? void 0 : i2.value;
          this._isEnabled = !!(!a2 && o2 && e10 && o2 === e10.previewModeId), this._previewModeId = null == e10 ? void 0 : e10.previewModeId, this._mutableCookies = n2;
        }
        get isEnabled() {
          return this._isEnabled;
        }
        enable() {
          if (!this._previewModeId) throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"), "__NEXT_ERROR_CODE", { value: "E93", enumerable: false, configurable: true });
          this._mutableCookies.set({ name: eI, value: this._previewModeId, httpOnly: true, sameSite: "none", secure: true, path: "/" }), this._isEnabled = true;
        }
        disable() {
          this._mutableCookies.set({ name: eI, value: "", httpOnly: true, sameSite: "none", secure: true, path: "/", expires: /* @__PURE__ */ new Date(0) }), this._isEnabled = false;
        }
      }
      function ej(e10, t10) {
        if ("x-middleware-set-cookie" in e10.headers && "string" == typeof e10.headers["x-middleware-set-cookie"]) {
          let r2 = e10.headers["x-middleware-set-cookie"], n2 = new Headers();
          for (let e11 of m(r2)) n2.append("set-cookie", e11);
          for (let e11 of new $.ResponseCookies(n2).getAll()) t10.set(e11);
        }
      }
      var e$ = r(408), eD = r.n(e$);
      class eq extends Error {
        constructor(e10, t10) {
          super("Invariant: " + (e10.endsWith(".") ? e10 : e10 + ".") + " This is a bug in Next.js.", t10), this.name = "InvariantError";
        }
      }
      class eU {
        constructor(e10, t10) {
          this.cache = /* @__PURE__ */ new Map(), this.sizes = /* @__PURE__ */ new Map(), this.totalSize = 0, this.maxSize = e10, this.calculateSize = t10 || (() => 1);
        }
        set(e10, t10) {
          if (!e10 || !t10) return;
          let r2 = this.calculateSize(t10);
          if (r2 > this.maxSize) return void console.warn("Single item size exceeds maxSize");
          this.cache.has(e10) && (this.totalSize -= this.sizes.get(e10) || 0), this.cache.set(e10, t10), this.sizes.set(e10, r2), this.totalSize += r2, this.touch(e10);
        }
        has(e10) {
          return !!e10 && (this.touch(e10), !!this.cache.get(e10));
        }
        get(e10) {
          if (!e10) return;
          let t10 = this.cache.get(e10);
          if (void 0 !== t10) return this.touch(e10), t10;
        }
        touch(e10) {
          let t10 = this.cache.get(e10);
          void 0 !== t10 && (this.cache.delete(e10), this.cache.set(e10, t10), this.evictIfNecessary());
        }
        evictIfNecessary() {
          for (; this.totalSize > this.maxSize && this.cache.size > 0; ) this.evictLeastRecentlyUsed();
        }
        evictLeastRecentlyUsed() {
          let e10 = this.cache.keys().next().value;
          if (void 0 !== e10) {
            let t10 = this.sizes.get(e10) || 0;
            this.totalSize -= t10, this.cache.delete(e10), this.sizes.delete(e10);
          }
        }
        reset() {
          this.cache.clear(), this.sizes.clear(), this.totalSize = 0;
        }
        keys() {
          return [...this.cache.keys()];
        }
        remove(e10) {
          this.cache.has(e10) && (this.totalSize -= this.sizes.get(e10) || 0, this.cache.delete(e10), this.sizes.delete(e10));
        }
        clear() {
          this.cache.clear(), this.sizes.clear(), this.totalSize = 0;
        }
        get size() {
          return this.cache.size;
        }
        get currentSize() {
          return this.totalSize;
        }
      }
      r(356).Buffer, new eU(********, (e10) => e10.size), process.env.NEXT_PRIVATE_DEBUG_CACHE && console.debug.bind(console, "DefaultCacheHandler:"), process.env.NEXT_PRIVATE_DEBUG_CACHE, Symbol.for("@next/cache-handlers");
      let eG = Symbol.for("@next/cache-handlers-map"), eB = Symbol.for("@next/cache-handlers-set"), eH = globalThis;
      function ez() {
        if (eH[eG]) return eH[eG].entries();
      }
      async function eF(e10, t10) {
        if (!e10) return t10();
        let r2 = eW(e10);
        try {
          return await t10();
        } finally {
          let t11 = function(e11, t12) {
            let r3 = new Set(e11.pendingRevalidatedTags), n2 = new Set(e11.pendingRevalidateWrites);
            return { pendingRevalidatedTags: t12.pendingRevalidatedTags.filter((e12) => !r3.has(e12)), pendingRevalidates: Object.fromEntries(Object.entries(t12.pendingRevalidates).filter(([t13]) => !(t13 in e11.pendingRevalidates))), pendingRevalidateWrites: t12.pendingRevalidateWrites.filter((e12) => !n2.has(e12)) };
          }(r2, eW(e10));
          await eK(e10, t11);
        }
      }
      function eW(e10) {
        return { pendingRevalidatedTags: e10.pendingRevalidatedTags ? [...e10.pendingRevalidatedTags] : [], pendingRevalidates: { ...e10.pendingRevalidates }, pendingRevalidateWrites: e10.pendingRevalidateWrites ? [...e10.pendingRevalidateWrites] : [] };
      }
      async function eV(e10, t10) {
        if (0 === e10.length) return;
        let r2 = [];
        t10 && r2.push(t10.revalidateTag(e10));
        let n2 = function() {
          if (eH[eB]) return eH[eB].values();
        }();
        if (n2) for (let t11 of n2) r2.push(t11.expireTags(...e10));
        await Promise.all(r2);
      }
      async function eK(e10, t10) {
        let r2 = (null == t10 ? void 0 : t10.pendingRevalidatedTags) ?? e10.pendingRevalidatedTags ?? [], n2 = (null == t10 ? void 0 : t10.pendingRevalidates) ?? e10.pendingRevalidates ?? {}, i2 = (null == t10 ? void 0 : t10.pendingRevalidateWrites) ?? e10.pendingRevalidateWrites ?? [];
        return Promise.all([eV(r2, e10.incrementalCache), ...Object.values(n2), ...i2]);
      }
      let eX = Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"), "__NEXT_ERROR_CODE", { value: "E504", enumerable: false, configurable: true });
      class eY {
        disable() {
          throw eX;
        }
        getStore() {
        }
        run() {
          throw eX;
        }
        exit() {
          throw eX;
        }
        enterWith() {
          throw eX;
        }
        static bind(e10) {
          return e10;
        }
      }
      let eJ = "undefined" != typeof globalThis && globalThis.AsyncLocalStorage, eZ = eJ ? new eJ() : new eY();
      class eQ {
        constructor({ waitUntil: e10, onClose: t10, onTaskError: r2 }) {
          this.workUnitStores = /* @__PURE__ */ new Set(), this.waitUntil = e10, this.onClose = t10, this.onTaskError = r2, this.callbackQueue = new (eD())(), this.callbackQueue.pause();
        }
        after(e10) {
          if (em(e10)) this.waitUntil || e0(), this.waitUntil(e10.catch((e11) => this.reportTaskError("promise", e11)));
          else if ("function" == typeof e10) this.addCallback(e10);
          else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"), "__NEXT_ERROR_CODE", { value: "E50", enumerable: false, configurable: true });
        }
        addCallback(e10) {
          var t10;
          this.waitUntil || e0();
          let r2 = Z.FP.getStore();
          r2 && this.workUnitStores.add(r2);
          let n2 = eZ.getStore(), i2 = n2 ? n2.rootTaskSpawnPhase : null == r2 ? void 0 : r2.phase;
          this.runCallbacksOnClosePromise || (this.runCallbacksOnClosePromise = this.runCallbacksOnClose(), this.waitUntil(this.runCallbacksOnClosePromise));
          let a2 = (t10 = async () => {
            try {
              await eZ.run({ rootTaskSpawnPhase: i2 }, () => e10());
            } catch (e11) {
              this.reportTaskError("function", e11);
            }
          }, eJ ? eJ.bind(t10) : eY.bind(t10));
          this.callbackQueue.add(a2);
        }
        async runCallbacksOnClose() {
          return await new Promise((e10) => this.onClose(e10)), this.runCallbacks();
        }
        async runCallbacks() {
          if (0 === this.callbackQueue.size) return;
          for (let e11 of this.workUnitStores) e11.phase = "after";
          let e10 = J.J.getStore();
          if (!e10) throw Object.defineProperty(new eq("Missing workStore in AfterContext.runCallbacks"), "__NEXT_ERROR_CODE", { value: "E547", enumerable: false, configurable: true });
          return eF(e10, () => (this.callbackQueue.start(), this.callbackQueue.onIdle()));
        }
        reportTaskError(e10, t10) {
          if (console.error("promise" === e10 ? "A promise passed to `after()` rejected:" : "An error occurred in a function passed to `after()`:", t10), this.onTaskError) try {
            null == this.onTaskError || this.onTaskError.call(this, t10);
          } catch (e11) {
            console.error(Object.defineProperty(new eq("`onTaskError` threw while handling an error thrown from an `after` task", { cause: e11 }), "__NEXT_ERROR_CODE", { value: "E569", enumerable: false, configurable: true }));
          }
        }
      }
      function e0() {
        throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."), "__NEXT_ERROR_CODE", { value: "E91", enumerable: false, configurable: true });
      }
      function e1(e10) {
        let t10, r2 = { then: (n2, i2) => (t10 || (t10 = e10()), t10.then((e11) => {
          r2.value = e11;
        }).catch(() => {
        }), t10.then(n2, i2)) };
        return r2;
      }
      class e3 {
        onClose(e10) {
          if (this.isClosed) throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"), "__NEXT_ERROR_CODE", { value: "E365", enumerable: false, configurable: true });
          this.target.addEventListener("close", e10), this.listeners++;
        }
        dispatchClose() {
          if (this.isClosed) throw Object.defineProperty(Error("Cannot close a CloseController multiple times"), "__NEXT_ERROR_CODE", { value: "E229", enumerable: false, configurable: true });
          this.listeners > 0 && this.target.dispatchEvent(new Event("close")), this.isClosed = true;
        }
        constructor() {
          this.target = new EventTarget(), this.listeners = 0, this.isClosed = false;
        }
      }
      function e2() {
        return { previewModeId: process.env.__NEXT_PREVIEW_MODE_ID, previewModeSigningKey: process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY || "", previewModeEncryptionKey: process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY || "" };
      }
      let e4 = Symbol.for("@next/request-context"), e5 = (e10) => {
        let t10 = ["/layout"];
        if (e10.startsWith("/")) {
          let r2 = e10.split("/");
          for (let e11 = 1; e11 < r2.length + 1; e11++) {
            let n2 = r2.slice(0, e11).join("/");
            n2 && (n2.endsWith("/page") || n2.endsWith("/route") || (n2 = `${n2}${!n2.endsWith("/") ? "/" : ""}layout`), t10.push(n2));
          }
        }
        return t10;
      };
      async function e6(e10, t10, r2) {
        let n2 = [], i2 = r2 && r2.size > 0;
        for (let t11 of e5(e10)) t11 = `${y}${t11}`, n2.push(t11);
        if (t10.pathname && !i2) {
          let e11 = `${y}${t10.pathname}`;
          n2.push(e11);
        }
        return { tags: n2, expirationsByCacheKind: function(e11) {
          let t11 = /* @__PURE__ */ new Map(), r3 = ez();
          if (r3) for (let [n3, i3] of r3) "getExpiration" in i3 && t11.set(n3, e1(async () => i3.getExpiration(...e11)));
          return t11;
        }(n2) };
      }
      class e9 extends q {
        constructor(e10) {
          super(e10.input, e10.init), this.sourcePage = e10.page;
        }
        get request() {
          throw Object.defineProperty(new p({ page: this.sourcePage }), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }
        respondWith() {
          throw Object.defineProperty(new p({ page: this.sourcePage }), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }
        waitUntil() {
          throw Object.defineProperty(new p({ page: this.sourcePage }), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        }
      }
      let e8 = { keys: (e10) => Array.from(e10.keys()), get: (e10, t10) => e10.get(t10) ?? void 0 }, e7 = (e10, t10) => eL().withPropagatedContext(e10.headers, t10, e8), te = false;
      async function tt(e10) {
        var t10;
        let n2, i2;
        if (!te && (te = true, "true" === process.env.NEXT_PRIVATE_TEST_PROXY)) {
          let { interceptTestApis: e11, wrapRequestHandler: t11 } = r(363);
          e11(), e7 = t11(e7);
        }
        await d();
        let a2 = void 0 !== globalThis.__BUILD_MANIFEST;
        e10.request.url = e10.request.url.replace(/\.rsc($|\?)/, "$1");
        let o2 = new j(e10.request.url, { headers: e10.request.headers, nextConfig: e10.request.nextConfig });
        for (let e11 of [...o2.searchParams.keys()]) {
          let t11 = o2.searchParams.getAll(e11), r2 = function(e12) {
            for (let t12 of ["nxtP", "nxtI"]) if (e12 !== t12 && e12.startsWith(t12)) return e12.substring(t12.length);
            return null;
          }(e11);
          if (r2) {
            for (let e12 of (o2.searchParams.delete(r2), t11)) o2.searchParams.append(r2, e12);
            o2.searchParams.delete(e11);
          }
        }
        let s2 = o2.buildId;
        o2.buildId = "";
        let u2 = function(e11) {
          let t11 = new Headers();
          for (let [r2, n3] of Object.entries(e11)) for (let e12 of Array.isArray(n3) ? n3 : [n3]) void 0 !== e12 && ("number" == typeof e12 && (e12 = e12.toString()), t11.append(r2, e12));
          return t11;
        }(e10.request.headers), l2 = u2.has("x-nextjs-data"), c2 = "1" === u2.get("RSC");
        l2 && "/index" === o2.pathname && (o2.pathname = "/");
        let f2 = /* @__PURE__ */ new Map();
        if (!a2) for (let e11 of V) {
          let t11 = e11.toLowerCase(), r2 = u2.get(t11);
          null !== r2 && (f2.set(t11, r2), u2.delete(t11));
        }
        let p2 = new e9({ page: e10.page, input: function(e11) {
          let t11 = "string" == typeof e11, r2 = t11 ? new URL(e11) : e11;
          return r2.searchParams.delete(K), t11 ? r2.toString() : r2;
        }(o2).toString(), init: { body: e10.request.body, headers: u2, method: e10.request.method, nextConfig: e10.request.nextConfig, signal: e10.request.signal } });
        l2 && Object.defineProperty(p2, "__isData", { enumerable: false, value: true }), !globalThis.__incrementalCache && e10.IncrementalCache && (globalThis.__incrementalCache = new e10.IncrementalCache({ appDir: true, fetchCache: true, minimalMode: true, fetchCacheKeyPrefix: "", dev: false, requestHeaders: e10.request.headers, requestProtocol: "https", getPrerenderManifest: () => ({ version: -1, routes: {}, dynamicRoutes: {}, notFoundRoutes: [], preview: e2() }) }));
        let h2 = e10.request.waitUntil ?? (null == (t10 = function() {
          let e11 = globalThis[e4];
          return null == e11 ? void 0 : e11.get();
        }()) ? void 0 : t10.waitUntil), _2 = new R({ request: p2, page: e10.page, context: h2 ? { waitUntil: h2 } : void 0 });
        if ((n2 = await e7(p2, () => {
          if ("/middleware" === e10.page || "/src/middleware" === e10.page) {
            let t11 = _2.waitUntil.bind(_2), r2 = new e3();
            return eL().trace(e_.execute, { spanName: `middleware ${p2.method} ${p2.nextUrl.pathname}`, attributes: { "http.target": p2.nextUrl.pathname, "http.method": p2.method } }, async () => {
              try {
                var n3, a3, o3, u3, l3, c3;
                let d2 = e2(), f3 = await e6("/", p2.nextUrl, null), h3 = (l3 = p2.nextUrl, c3 = (e11) => {
                  i2 = e11;
                }, function(e11, t12, r3, n4, i3, a4, o4, s3, u4, l4, c4) {
                  function d3(e12) {
                    r3 && r3.setHeader("Set-Cookie", e12);
                  }
                  let f4 = {};
                  return { type: "request", phase: e11, implicitTags: a4, url: { pathname: n4.pathname, search: n4.search ?? "" }, rootParams: i3, get headers() {
                    return f4.headers || (f4.headers = function(e12) {
                      let t13 = Y.from(e12);
                      for (let e13 of V) t13.delete(e13.toLowerCase());
                      return Y.seal(t13);
                    }(t12.headers)), f4.headers;
                  }, get cookies() {
                    if (!f4.cookies) {
                      let e12 = new $.RequestCookies(Y.from(t12.headers));
                      ej(t12, e12), f4.cookies = ee.seal(e12);
                    }
                    return f4.cookies;
                  }, set cookies(value) {
                    f4.cookies = value;
                  }, get mutableCookies() {
                    if (!f4.mutableCookies) {
                      let e12 = function(e13, t13) {
                        let r4 = new $.RequestCookies(Y.from(e13));
                        return er.wrap(r4, t13);
                      }(t12.headers, o4 || (r3 ? d3 : void 0));
                      ej(t12, e12), f4.mutableCookies = e12;
                    }
                    return f4.mutableCookies;
                  }, get userspaceMutableCookies() {
                    return f4.userspaceMutableCookies || (f4.userspaceMutableCookies = function(e12) {
                      let t13 = new Proxy(e12, { get(e13, r4, n5) {
                        switch (r4) {
                          case "delete":
                            return function(...r5) {
                              return en("cookies().delete"), e13.delete(...r5), t13;
                            };
                          case "set":
                            return function(...r5) {
                              return en("cookies().set"), e13.set(...r5), t13;
                            };
                          default:
                            return U.get(e13, r4, n5);
                        }
                      } });
                      return t13;
                    }(this.mutableCookies)), f4.userspaceMutableCookies;
                  }, get draftMode() {
                    return f4.draftMode || (f4.draftMode = new eM(u4, t12, this.cookies, this.mutableCookies)), f4.draftMode;
                  }, renderResumeDataCache: s3 ?? null, isHmrRefresh: l4, serverComponentsHmrCache: c4 || globalThis.__serverComponentsHmrCache };
                }("action", p2, void 0, l3, {}, f3, c3, void 0, d2, false, void 0)), y3 = function({ page: e11, fallbackRouteParams: t12, renderOpts: r3, requestEndedState: n4, isPrefetchRequest: i3, buildId: a4, previouslyRevalidatedTags: o4 }) {
                  var s3;
                  let u4 = { isStaticGeneration: !r3.shouldWaitOnAllReady && !r3.supportsDynamicResponse && !r3.isDraftMode && !r3.isPossibleServerAction, page: e11, fallbackRouteParams: t12, route: (s3 = e11.split("/").reduce((e12, t13, r4, n5) => t13 ? "(" === t13[0] && t13.endsWith(")") || "@" === t13[0] || ("page" === t13 || "route" === t13) && r4 === n5.length - 1 ? e12 : e12 + "/" + t13 : e12, "")).startsWith("/") ? s3 : "/" + s3, incrementalCache: r3.incrementalCache || globalThis.__incrementalCache, cacheLifeProfiles: r3.cacheLifeProfiles, isRevalidate: r3.isRevalidate, isPrerendering: r3.nextExport, fetchCache: r3.fetchCache, isOnDemandRevalidate: r3.isOnDemandRevalidate, isDraftMode: r3.isDraftMode, requestEndedState: n4, isPrefetchRequest: i3, buildId: a4, reactLoadableManifest: (null == r3 ? void 0 : r3.reactLoadableManifest) || {}, assetPrefix: (null == r3 ? void 0 : r3.assetPrefix) || "", afterContext: function(e12) {
                    let { waitUntil: t13, onClose: r4, onAfterTaskError: n5 } = e12;
                    return new eQ({ waitUntil: t13, onClose: r4, onTaskError: n5 });
                  }(r3), dynamicIOEnabled: r3.experimental.dynamicIO, dev: r3.dev ?? false, previouslyRevalidatedTags: o4, refreshTagsByCacheKind: function() {
                    let e12 = /* @__PURE__ */ new Map(), t13 = ez();
                    if (t13) for (let [r4, n5] of t13) "refreshTags" in n5 && e12.set(r4, e1(async () => n5.refreshTags()));
                    return e12;
                  }() };
                  return r3.store = u4, u4;
                }({ page: "/", fallbackRouteParams: null, renderOpts: { cacheLifeProfiles: null == (a3 = e10.request.nextConfig) || null == (n3 = a3.experimental) ? void 0 : n3.cacheLife, experimental: { isRoutePPREnabled: false, dynamicIO: false, authInterrupts: !!(null == (u3 = e10.request.nextConfig) || null == (o3 = u3.experimental) ? void 0 : o3.authInterrupts) }, supportsDynamicResponse: true, waitUntil: t11, onClose: r2.onClose.bind(r2), onAfterTaskError: void 0 }, requestEndedState: { ended: false }, isPrefetchRequest: p2.headers.has(W), buildId: s2 ?? "", previouslyRevalidatedTags: [] });
                return await J.J.run(y3, () => Z.FP.run(h3, e10.handler, p2, _2));
              } finally {
                setTimeout(() => {
                  r2.dispatchClose();
                }, 0);
              }
            });
          }
          return e10.handler(p2, _2);
        })) && !(n2 instanceof Response)) throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"), "__NEXT_ERROR_CODE", { value: "E567", enumerable: false, configurable: true });
        n2 && i2 && n2.headers.set("set-cookie", i2);
        let y2 = null == n2 ? void 0 : n2.headers.get("x-middleware-rewrite");
        if (n2 && y2 && (c2 || !a2)) {
          let t11 = new j(y2, { forceLocale: true, headers: e10.request.headers, nextConfig: e10.request.nextConfig });
          a2 || t11.host !== p2.nextUrl.host || (t11.buildId = s2 || t11.buildId, n2.headers.set("x-middleware-rewrite", String(t11)));
          let { url: r2, isRelative: i3 } = F(t11.toString(), o2.toString());
          !a2 && l2 && n2.headers.set("x-nextjs-rewrite", r2), c2 && i3 && (o2.pathname !== t11.pathname && n2.headers.set("x-nextjs-rewritten-path", t11.pathname), o2.search !== t11.search && n2.headers.set("x-nextjs-rewritten-query", t11.search.slice(1)));
        }
        let g2 = null == n2 ? void 0 : n2.headers.get("Location");
        if (n2 && g2 && !a2) {
          let t11 = new j(g2, { forceLocale: false, headers: e10.request.headers, nextConfig: e10.request.nextConfig });
          n2 = new Response(n2.body, n2), t11.host === o2.host && (t11.buildId = s2 || t11.buildId, n2.headers.set("Location", t11.toString())), l2 && (n2.headers.delete("Location"), n2.headers.set("x-nextjs-redirect", F(t11.toString(), o2.toString()).url));
        }
        let m2 = n2 || z.next(), v2 = m2.headers.get("x-middleware-override-headers"), b2 = [];
        if (v2) {
          for (let [e11, t11] of f2) m2.headers.set(`x-middleware-request-${e11}`, t11), b2.push(e11);
          b2.length > 0 && m2.headers.set("x-middleware-override-headers", v2 + "," + b2.join(","));
        }
        return { response: m2, waitUntil: ("internal" === _2[E].kind ? Promise.all(_2[E].promises).then(() => {
        }) : void 0) ?? Promise.resolve(), fetchMetrics: p2.fetchMetrics };
      }
      r(270), "undefined" == typeof URLPattern || URLPattern;
      var tr = r(223), tn = r(348), ti = r(911);
      function ta(e10) {
        var t10, r2;
        return { ...e10, localePrefix: "object" == typeof (r2 = e10.localePrefix) ? r2 : { mode: r2 || "always" }, localeCookie: !!((t10 = e10.localeCookie) ?? 1) && { name: "NEXT_LOCALE", sameSite: "lax", ..."object" == typeof t10 && t10 }, localeDetection: e10.localeDetection ?? true, alternateLinks: e10.alternateLinks ?? true };
      }
      /* @__PURE__ */ new WeakMap();
      let to = "X-NEXT-INTL-LOCALE";
      function ts(e10) {
        return ("object" == typeof e10 ? null == e10.host && null == e10.hostname : !/^[a-z]+:/i.test(e10)) && !function(e11) {
          let t10 = "object" == typeof e11 ? e11.pathname : e11;
          return null != t10 && !t10.startsWith("/");
        }(e10);
      }
      function tu(e10, t10) {
        let r2 = e10;
        return /^\/(\?.*)?$/.test(t10) && (t10 = t10.slice(1)), r2 += t10;
      }
      function tl(e10, t10, r2) {
        return "string" == typeof e10 ? e10 : e10[t10] || r2;
      }
      function tc(e10) {
        let t10 = function() {
          try {
            return "true" === process.env._next_intl_trailing_slash;
          } catch {
            return false;
          }
        }(), [r2, ...n2] = e10.split("#"), i2 = n2.join("#"), a2 = r2;
        if ("/" !== a2) {
          let e11 = a2.endsWith("/");
          t10 && !e11 ? a2 += "/" : !t10 && e11 && (a2 = a2.slice(0, -1));
        }
        return i2 && (a2 += "#" + i2), a2;
      }
      function td(e10, t10) {
        let r2 = tc(e10), n2 = tc(t10);
        return tp(r2).test(n2);
      }
      function tf(e10, t10) {
        return "never" !== t10.mode && t10.prefixes?.[e10] || "/" + e10;
      }
      function tp(e10) {
        let t10 = e10.replace(/\[\[(\.\.\.[^\]]+)\]\]/g, "?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g, "(.+)").replace(/\[([^\]]+)\]/g, "([^/]+)");
        return RegExp(`^${t10}$`);
      }
      function th(e10) {
        return e10.includes("[[...");
      }
      function t_(e10) {
        return e10.includes("[...");
      }
      function ty(e10) {
        return e10.includes("[");
      }
      function tg(e10, t10) {
        let r2 = e10.split("/"), n2 = t10.split("/"), i2 = Math.max(r2.length, n2.length);
        for (let e11 = 0; e11 < i2; e11++) {
          let t11 = r2[e11], i3 = n2[e11];
          if (!t11 && i3) return -1;
          if (t11 && !i3) return 1;
          if (t11 || i3) {
            if (!ty(t11) && ty(i3)) return -1;
            if (ty(t11) && !ty(i3)) return 1;
            if (!t_(t11) && t_(i3)) return -1;
            if (t_(t11) && !t_(i3)) return 1;
            if (!th(t11) && th(i3)) return -1;
            if (th(t11) && !th(i3)) return 1;
          }
        }
        return 0;
      }
      function tm(e10) {
        return "function" == typeof e10.then;
      }
      function tv(e10, t10, r2, n2) {
        let i2 = "";
        return i2 += function(e11, t11) {
          if (!t11) return e11;
          let r3 = e11 = e11.replace(/\[\[/g, "[").replace(/\]\]/g, "]");
          return Object.entries(t11).forEach(([e12, t12]) => {
            r3 = r3.replace(`[${e12}]`, t12);
          }), r3;
        }(r2, function(e11, t11) {
          let r3 = tc(t11), n3 = tc(e11), i3 = tp(n3).exec(r3);
          if (!i3) return;
          let a2 = {};
          for (let e12 = 1; e12 < i3.length; e12++) {
            let t12 = n3.match(/\[([^\]]+)\]/g)?.[e12 - 1].replace(/[[\]]/g, "");
            t12 && (a2[t12] = i3[e12]);
          }
          return a2;
        }(t10, e10)), i2 = tc(i2);
      }
      function tb(e10, t10, r2) {
        e10.endsWith("/") || (e10 += "/");
        let n2 = tw(t10, r2), i2 = RegExp(`^(${n2.map(([, e11]) => e11.replaceAll("/", "\\/")).join("|")})/(.*)`, "i"), a2 = e10.match(i2), o2 = a2 ? "/" + a2[2] : e10;
        return "/" !== o2 && (o2 = tc(o2)), o2;
      }
      function tw(e10, t10, r2 = true) {
        let n2 = e10.map((e11) => [e11, tf(e11, t10)]);
        return r2 && n2.sort((e11, t11) => t11[1].length - e11[1].length), n2;
      }
      function tS(e10, t10, r2, n2) {
        let i2 = tw(t10, r2);
        for (let [t11, r3] of (n2 && i2.sort(([e11], [t12]) => {
          if (e11 === n2.defaultLocale) return -1;
          if (t12 === n2.defaultLocale) return 1;
          let r4 = n2.locales.includes(e11), i3 = n2.locales.includes(t12);
          return r4 && !i3 ? -1 : !r4 && i3 ? 1 : 0;
        }), i2)) {
          let n3, i3;
          if (e10 === r3 || e10.startsWith(r3 + "/")) n3 = i3 = true;
          else {
            let t12 = e10.toLowerCase(), a2 = r3.toLowerCase();
            (t12 === a2 || t12.startsWith(a2 + "/")) && (n3 = false, i3 = true);
          }
          if (i3) return { locale: t11, prefix: r3, matchedPrefix: e10.slice(0, r3.length), exact: n3 };
        }
      }
      function tE(e10, t10, r2) {
        let n2 = e10;
        return t10 && (n2 = tu(t10, n2)), r2 && (n2 += r2), n2;
      }
      function tC(e10) {
        return e10.get("x-forwarded-host") ?? e10.get("host") ?? void 0;
      }
      function tR(e10, t10) {
        return t10.defaultLocale === e10 || t10.locales.includes(e10);
      }
      function tx(e10, t10, r2) {
        let n2;
        return e10 && tR(t10, e10) && (n2 = e10), n2 || (n2 = r2.find((e11) => e11.defaultLocale === t10)), n2 || (n2 = r2.find((e11) => e11.locales.includes(t10))), n2;
      }
      Object.create;
      function tT(e10, t10, r2) {
        if (r2 || 2 == arguments.length) for (var n2, i2 = 0, a2 = t10.length; i2 < a2; i2++) !n2 && i2 in t10 || (n2 || (n2 = Array.prototype.slice.call(t10, 0, i2)), n2[i2] = t10[i2]);
        return e10.concat(n2 || Array.prototype.slice.call(t10));
      }
      Object.create;
      var tk = ("function" == typeof SuppressedError && SuppressedError, { supplemental: { languageMatching: { "written-new": [{ paradigmLocales: { _locales: "en en_GB es es_419 pt_BR pt_PT" } }, { $enUS: { _value: "AS+CA+GU+MH+MP+PH+PR+UM+US+VI" } }, { $cnsar: { _value: "HK+MO" } }, { $americas: { _value: "019" } }, { $maghreb: { _value: "MA+DZ+TN+LY+MR+EH" } }, { no: { _desired: "nb", _distance: "1" } }, { bs: { _desired: "hr", _distance: "4" } }, { bs: { _desired: "sh", _distance: "4" } }, { hr: { _desired: "sh", _distance: "4" } }, { sr: { _desired: "sh", _distance: "4" } }, { aa: { _desired: "ssy", _distance: "4" } }, { de: { _desired: "gsw", _distance: "4", _oneway: "true" } }, { de: { _desired: "lb", _distance: "4", _oneway: "true" } }, { no: { _desired: "da", _distance: "8" } }, { nb: { _desired: "da", _distance: "8" } }, { ru: { _desired: "ab", _distance: "30", _oneway: "true" } }, { en: { _desired: "ach", _distance: "30", _oneway: "true" } }, { nl: { _desired: "af", _distance: "20", _oneway: "true" } }, { en: { _desired: "ak", _distance: "30", _oneway: "true" } }, { en: { _desired: "am", _distance: "30", _oneway: "true" } }, { es: { _desired: "ay", _distance: "20", _oneway: "true" } }, { ru: { _desired: "az", _distance: "30", _oneway: "true" } }, { ur: { _desired: "bal", _distance: "20", _oneway: "true" } }, { ru: { _desired: "be", _distance: "20", _oneway: "true" } }, { en: { _desired: "bem", _distance: "30", _oneway: "true" } }, { hi: { _desired: "bh", _distance: "30", _oneway: "true" } }, { en: { _desired: "bn", _distance: "30", _oneway: "true" } }, { zh: { _desired: "bo", _distance: "20", _oneway: "true" } }, { fr: { _desired: "br", _distance: "20", _oneway: "true" } }, { es: { _desired: "ca", _distance: "20", _oneway: "true" } }, { fil: { _desired: "ceb", _distance: "30", _oneway: "true" } }, { en: { _desired: "chr", _distance: "20", _oneway: "true" } }, { ar: { _desired: "ckb", _distance: "30", _oneway: "true" } }, { fr: { _desired: "co", _distance: "20", _oneway: "true" } }, { fr: { _desired: "crs", _distance: "20", _oneway: "true" } }, { sk: { _desired: "cs", _distance: "20" } }, { en: { _desired: "cy", _distance: "20", _oneway: "true" } }, { en: { _desired: "ee", _distance: "30", _oneway: "true" } }, { en: { _desired: "eo", _distance: "30", _oneway: "true" } }, { es: { _desired: "eu", _distance: "20", _oneway: "true" } }, { da: { _desired: "fo", _distance: "20", _oneway: "true" } }, { nl: { _desired: "fy", _distance: "20", _oneway: "true" } }, { en: { _desired: "ga", _distance: "20", _oneway: "true" } }, { en: { _desired: "gaa", _distance: "30", _oneway: "true" } }, { en: { _desired: "gd", _distance: "20", _oneway: "true" } }, { es: { _desired: "gl", _distance: "20", _oneway: "true" } }, { es: { _desired: "gn", _distance: "20", _oneway: "true" } }, { hi: { _desired: "gu", _distance: "30", _oneway: "true" } }, { en: { _desired: "ha", _distance: "30", _oneway: "true" } }, { en: { _desired: "haw", _distance: "20", _oneway: "true" } }, { fr: { _desired: "ht", _distance: "20", _oneway: "true" } }, { ru: { _desired: "hy", _distance: "30", _oneway: "true" } }, { en: { _desired: "ia", _distance: "30", _oneway: "true" } }, { en: { _desired: "ig", _distance: "30", _oneway: "true" } }, { en: { _desired: "is", _distance: "20", _oneway: "true" } }, { id: { _desired: "jv", _distance: "20", _oneway: "true" } }, { en: { _desired: "ka", _distance: "30", _oneway: "true" } }, { fr: { _desired: "kg", _distance: "30", _oneway: "true" } }, { ru: { _desired: "kk", _distance: "30", _oneway: "true" } }, { en: { _desired: "km", _distance: "30", _oneway: "true" } }, { en: { _desired: "kn", _distance: "30", _oneway: "true" } }, { en: { _desired: "kri", _distance: "30", _oneway: "true" } }, { tr: { _desired: "ku", _distance: "30", _oneway: "true" } }, { ru: { _desired: "ky", _distance: "30", _oneway: "true" } }, { it: { _desired: "la", _distance: "20", _oneway: "true" } }, { en: { _desired: "lg", _distance: "30", _oneway: "true" } }, { fr: { _desired: "ln", _distance: "30", _oneway: "true" } }, { en: { _desired: "lo", _distance: "30", _oneway: "true" } }, { en: { _desired: "loz", _distance: "30", _oneway: "true" } }, { fr: { _desired: "lua", _distance: "30", _oneway: "true" } }, { hi: { _desired: "mai", _distance: "20", _oneway: "true" } }, { en: { _desired: "mfe", _distance: "30", _oneway: "true" } }, { fr: { _desired: "mg", _distance: "30", _oneway: "true" } }, { en: { _desired: "mi", _distance: "20", _oneway: "true" } }, { en: { _desired: "ml", _distance: "30", _oneway: "true" } }, { ru: { _desired: "mn", _distance: "30", _oneway: "true" } }, { hi: { _desired: "mr", _distance: "30", _oneway: "true" } }, { id: { _desired: "ms", _distance: "30", _oneway: "true" } }, { en: { _desired: "mt", _distance: "30", _oneway: "true" } }, { en: { _desired: "my", _distance: "30", _oneway: "true" } }, { en: { _desired: "ne", _distance: "30", _oneway: "true" } }, { nb: { _desired: "nn", _distance: "20" } }, { no: { _desired: "nn", _distance: "20" } }, { en: { _desired: "nso", _distance: "30", _oneway: "true" } }, { en: { _desired: "ny", _distance: "30", _oneway: "true" } }, { en: { _desired: "nyn", _distance: "30", _oneway: "true" } }, { fr: { _desired: "oc", _distance: "20", _oneway: "true" } }, { en: { _desired: "om", _distance: "30", _oneway: "true" } }, { en: { _desired: "or", _distance: "30", _oneway: "true" } }, { en: { _desired: "pa", _distance: "30", _oneway: "true" } }, { en: { _desired: "pcm", _distance: "20", _oneway: "true" } }, { en: { _desired: "ps", _distance: "30", _oneway: "true" } }, { es: { _desired: "qu", _distance: "30", _oneway: "true" } }, { de: { _desired: "rm", _distance: "20", _oneway: "true" } }, { en: { _desired: "rn", _distance: "30", _oneway: "true" } }, { fr: { _desired: "rw", _distance: "30", _oneway: "true" } }, { hi: { _desired: "sa", _distance: "30", _oneway: "true" } }, { en: { _desired: "sd", _distance: "30", _oneway: "true" } }, { en: { _desired: "si", _distance: "30", _oneway: "true" } }, { en: { _desired: "sn", _distance: "30", _oneway: "true" } }, { en: { _desired: "so", _distance: "30", _oneway: "true" } }, { en: { _desired: "sq", _distance: "30", _oneway: "true" } }, { en: { _desired: "st", _distance: "30", _oneway: "true" } }, { id: { _desired: "su", _distance: "20", _oneway: "true" } }, { en: { _desired: "sw", _distance: "30", _oneway: "true" } }, { en: { _desired: "ta", _distance: "30", _oneway: "true" } }, { en: { _desired: "te", _distance: "30", _oneway: "true" } }, { ru: { _desired: "tg", _distance: "30", _oneway: "true" } }, { en: { _desired: "ti", _distance: "30", _oneway: "true" } }, { ru: { _desired: "tk", _distance: "30", _oneway: "true" } }, { en: { _desired: "tlh", _distance: "30", _oneway: "true" } }, { en: { _desired: "tn", _distance: "30", _oneway: "true" } }, { en: { _desired: "to", _distance: "30", _oneway: "true" } }, { ru: { _desired: "tt", _distance: "30", _oneway: "true" } }, { en: { _desired: "tum", _distance: "30", _oneway: "true" } }, { zh: { _desired: "ug", _distance: "20", _oneway: "true" } }, { ru: { _desired: "uk", _distance: "20", _oneway: "true" } }, { en: { _desired: "ur", _distance: "30", _oneway: "true" } }, { ru: { _desired: "uz", _distance: "30", _oneway: "true" } }, { fr: { _desired: "wo", _distance: "30", _oneway: "true" } }, { en: { _desired: "xh", _distance: "30", _oneway: "true" } }, { en: { _desired: "yi", _distance: "30", _oneway: "true" } }, { en: { _desired: "yo", _distance: "30", _oneway: "true" } }, { zh: { _desired: "za", _distance: "20", _oneway: "true" } }, { en: { _desired: "zu", _distance: "30", _oneway: "true" } }, { ar: { _desired: "aao", _distance: "10", _oneway: "true" } }, { ar: { _desired: "abh", _distance: "10", _oneway: "true" } }, { ar: { _desired: "abv", _distance: "10", _oneway: "true" } }, { ar: { _desired: "acm", _distance: "10", _oneway: "true" } }, { ar: { _desired: "acq", _distance: "10", _oneway: "true" } }, { ar: { _desired: "acw", _distance: "10", _oneway: "true" } }, { ar: { _desired: "acx", _distance: "10", _oneway: "true" } }, { ar: { _desired: "acy", _distance: "10", _oneway: "true" } }, { ar: { _desired: "adf", _distance: "10", _oneway: "true" } }, { ar: { _desired: "aeb", _distance: "10", _oneway: "true" } }, { ar: { _desired: "aec", _distance: "10", _oneway: "true" } }, { ar: { _desired: "afb", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ajp", _distance: "10", _oneway: "true" } }, { ar: { _desired: "apc", _distance: "10", _oneway: "true" } }, { ar: { _desired: "apd", _distance: "10", _oneway: "true" } }, { ar: { _desired: "arq", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ars", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ary", _distance: "10", _oneway: "true" } }, { ar: { _desired: "arz", _distance: "10", _oneway: "true" } }, { ar: { _desired: "auz", _distance: "10", _oneway: "true" } }, { ar: { _desired: "avl", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ayh", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ayl", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ayn", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ayp", _distance: "10", _oneway: "true" } }, { ar: { _desired: "bbz", _distance: "10", _oneway: "true" } }, { ar: { _desired: "pga", _distance: "10", _oneway: "true" } }, { ar: { _desired: "shu", _distance: "10", _oneway: "true" } }, { ar: { _desired: "ssh", _distance: "10", _oneway: "true" } }, { az: { _desired: "azb", _distance: "10", _oneway: "true" } }, { et: { _desired: "vro", _distance: "10", _oneway: "true" } }, { ff: { _desired: "ffm", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fub", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fue", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fuf", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fuh", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fui", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fuq", _distance: "10", _oneway: "true" } }, { ff: { _desired: "fuv", _distance: "10", _oneway: "true" } }, { gn: { _desired: "gnw", _distance: "10", _oneway: "true" } }, { gn: { _desired: "gui", _distance: "10", _oneway: "true" } }, { gn: { _desired: "gun", _distance: "10", _oneway: "true" } }, { gn: { _desired: "nhd", _distance: "10", _oneway: "true" } }, { iu: { _desired: "ikt", _distance: "10", _oneway: "true" } }, { kln: { _desired: "enb", _distance: "10", _oneway: "true" } }, { kln: { _desired: "eyo", _distance: "10", _oneway: "true" } }, { kln: { _desired: "niq", _distance: "10", _oneway: "true" } }, { kln: { _desired: "oki", _distance: "10", _oneway: "true" } }, { kln: { _desired: "pko", _distance: "10", _oneway: "true" } }, { kln: { _desired: "sgc", _distance: "10", _oneway: "true" } }, { kln: { _desired: "tec", _distance: "10", _oneway: "true" } }, { kln: { _desired: "tuy", _distance: "10", _oneway: "true" } }, { kok: { _desired: "gom", _distance: "10", _oneway: "true" } }, { kpe: { _desired: "gkp", _distance: "10", _oneway: "true" } }, { luy: { _desired: "ida", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lkb", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lko", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lks", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lri", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lrm", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lsm", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lto", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lts", _distance: "10", _oneway: "true" } }, { luy: { _desired: "lwg", _distance: "10", _oneway: "true" } }, { luy: { _desired: "nle", _distance: "10", _oneway: "true" } }, { luy: { _desired: "nyd", _distance: "10", _oneway: "true" } }, { luy: { _desired: "rag", _distance: "10", _oneway: "true" } }, { lv: { _desired: "ltg", _distance: "10", _oneway: "true" } }, { mg: { _desired: "bhr", _distance: "10", _oneway: "true" } }, { mg: { _desired: "bjq", _distance: "10", _oneway: "true" } }, { mg: { _desired: "bmm", _distance: "10", _oneway: "true" } }, { mg: { _desired: "bzc", _distance: "10", _oneway: "true" } }, { mg: { _desired: "msh", _distance: "10", _oneway: "true" } }, { mg: { _desired: "skg", _distance: "10", _oneway: "true" } }, { mg: { _desired: "tdx", _distance: "10", _oneway: "true" } }, { mg: { _desired: "tkg", _distance: "10", _oneway: "true" } }, { mg: { _desired: "txy", _distance: "10", _oneway: "true" } }, { mg: { _desired: "xmv", _distance: "10", _oneway: "true" } }, { mg: { _desired: "xmw", _distance: "10", _oneway: "true" } }, { mn: { _desired: "mvf", _distance: "10", _oneway: "true" } }, { ms: { _desired: "bjn", _distance: "10", _oneway: "true" } }, { ms: { _desired: "btj", _distance: "10", _oneway: "true" } }, { ms: { _desired: "bve", _distance: "10", _oneway: "true" } }, { ms: { _desired: "bvu", _distance: "10", _oneway: "true" } }, { ms: { _desired: "coa", _distance: "10", _oneway: "true" } }, { ms: { _desired: "dup", _distance: "10", _oneway: "true" } }, { ms: { _desired: "hji", _distance: "10", _oneway: "true" } }, { ms: { _desired: "id", _distance: "10", _oneway: "true" } }, { ms: { _desired: "jak", _distance: "10", _oneway: "true" } }, { ms: { _desired: "jax", _distance: "10", _oneway: "true" } }, { ms: { _desired: "kvb", _distance: "10", _oneway: "true" } }, { ms: { _desired: "kvr", _distance: "10", _oneway: "true" } }, { ms: { _desired: "kxd", _distance: "10", _oneway: "true" } }, { ms: { _desired: "lce", _distance: "10", _oneway: "true" } }, { ms: { _desired: "lcf", _distance: "10", _oneway: "true" } }, { ms: { _desired: "liw", _distance: "10", _oneway: "true" } }, { ms: { _desired: "max", _distance: "10", _oneway: "true" } }, { ms: { _desired: "meo", _distance: "10", _oneway: "true" } }, { ms: { _desired: "mfa", _distance: "10", _oneway: "true" } }, { ms: { _desired: "mfb", _distance: "10", _oneway: "true" } }, { ms: { _desired: "min", _distance: "10", _oneway: "true" } }, { ms: { _desired: "mqg", _distance: "10", _oneway: "true" } }, { ms: { _desired: "msi", _distance: "10", _oneway: "true" } }, { ms: { _desired: "mui", _distance: "10", _oneway: "true" } }, { ms: { _desired: "orn", _distance: "10", _oneway: "true" } }, { ms: { _desired: "ors", _distance: "10", _oneway: "true" } }, { ms: { _desired: "pel", _distance: "10", _oneway: "true" } }, { ms: { _desired: "pse", _distance: "10", _oneway: "true" } }, { ms: { _desired: "tmw", _distance: "10", _oneway: "true" } }, { ms: { _desired: "urk", _distance: "10", _oneway: "true" } }, { ms: { _desired: "vkk", _distance: "10", _oneway: "true" } }, { ms: { _desired: "vkt", _distance: "10", _oneway: "true" } }, { ms: { _desired: "xmm", _distance: "10", _oneway: "true" } }, { ms: { _desired: "zlm", _distance: "10", _oneway: "true" } }, { ms: { _desired: "zmi", _distance: "10", _oneway: "true" } }, { ne: { _desired: "dty", _distance: "10", _oneway: "true" } }, { om: { _desired: "gax", _distance: "10", _oneway: "true" } }, { om: { _desired: "hae", _distance: "10", _oneway: "true" } }, { om: { _desired: "orc", _distance: "10", _oneway: "true" } }, { or: { _desired: "spv", _distance: "10", _oneway: "true" } }, { ps: { _desired: "pbt", _distance: "10", _oneway: "true" } }, { ps: { _desired: "pst", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qub", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qud", _distance: "10", _oneway: "true" } }, { qu: { _desired: "quf", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qug", _distance: "10", _oneway: "true" } }, { qu: { _desired: "quh", _distance: "10", _oneway: "true" } }, { qu: { _desired: "quk", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qul", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qup", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qur", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qus", _distance: "10", _oneway: "true" } }, { qu: { _desired: "quw", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qux", _distance: "10", _oneway: "true" } }, { qu: { _desired: "quy", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qva", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvc", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qve", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvh", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvi", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvj", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvl", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvm", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvn", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvo", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvp", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvs", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvw", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qvz", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qwa", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qwc", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qwh", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qws", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxa", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxc", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxh", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxl", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxn", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxo", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxp", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxr", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxt", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxu", _distance: "10", _oneway: "true" } }, { qu: { _desired: "qxw", _distance: "10", _oneway: "true" } }, { sc: { _desired: "sdc", _distance: "10", _oneway: "true" } }, { sc: { _desired: "sdn", _distance: "10", _oneway: "true" } }, { sc: { _desired: "sro", _distance: "10", _oneway: "true" } }, { sq: { _desired: "aae", _distance: "10", _oneway: "true" } }, { sq: { _desired: "aat", _distance: "10", _oneway: "true" } }, { sq: { _desired: "aln", _distance: "10", _oneway: "true" } }, { syr: { _desired: "aii", _distance: "10", _oneway: "true" } }, { uz: { _desired: "uzs", _distance: "10", _oneway: "true" } }, { yi: { _desired: "yih", _distance: "10", _oneway: "true" } }, { zh: { _desired: "cdo", _distance: "10", _oneway: "true" } }, { zh: { _desired: "cjy", _distance: "10", _oneway: "true" } }, { zh: { _desired: "cpx", _distance: "10", _oneway: "true" } }, { zh: { _desired: "czh", _distance: "10", _oneway: "true" } }, { zh: { _desired: "czo", _distance: "10", _oneway: "true" } }, { zh: { _desired: "gan", _distance: "10", _oneway: "true" } }, { zh: { _desired: "hak", _distance: "10", _oneway: "true" } }, { zh: { _desired: "hsn", _distance: "10", _oneway: "true" } }, { zh: { _desired: "lzh", _distance: "10", _oneway: "true" } }, { zh: { _desired: "mnp", _distance: "10", _oneway: "true" } }, { zh: { _desired: "nan", _distance: "10", _oneway: "true" } }, { zh: { _desired: "wuu", _distance: "10", _oneway: "true" } }, { zh: { _desired: "yue", _distance: "10", _oneway: "true" } }, { "*": { _desired: "*", _distance: "80" } }, { "en-Latn": { _desired: "am-Ethi", _distance: "10", _oneway: "true" } }, { "ru-Cyrl": { _desired: "az-Latn", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "bn-Beng", _distance: "10", _oneway: "true" } }, { "zh-Hans": { _desired: "bo-Tibt", _distance: "10", _oneway: "true" } }, { "ru-Cyrl": { _desired: "hy-Armn", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ka-Geor", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "km-Khmr", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "kn-Knda", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "lo-Laoo", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ml-Mlym", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "my-Mymr", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ne-Deva", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "or-Orya", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "pa-Guru", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ps-Arab", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "sd-Arab", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "si-Sinh", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ta-Taml", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "te-Telu", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ti-Ethi", _distance: "10", _oneway: "true" } }, { "ru-Cyrl": { _desired: "tk-Latn", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "ur-Arab", _distance: "10", _oneway: "true" } }, { "ru-Cyrl": { _desired: "uz-Latn", _distance: "10", _oneway: "true" } }, { "en-Latn": { _desired: "yi-Hebr", _distance: "10", _oneway: "true" } }, { "sr-Cyrl": { _desired: "sr-Latn", _distance: "5" } }, { "zh-Hans": { _desired: "za-Latn", _distance: "10", _oneway: "true" } }, { "zh-Hans": { _desired: "zh-Hani", _distance: "20", _oneway: "true" } }, { "zh-Hant": { _desired: "zh-Hani", _distance: "20", _oneway: "true" } }, { "ar-Arab": { _desired: "ar-Latn", _distance: "20", _oneway: "true" } }, { "bn-Beng": { _desired: "bn-Latn", _distance: "20", _oneway: "true" } }, { "gu-Gujr": { _desired: "gu-Latn", _distance: "20", _oneway: "true" } }, { "hi-Deva": { _desired: "hi-Latn", _distance: "20", _oneway: "true" } }, { "kn-Knda": { _desired: "kn-Latn", _distance: "20", _oneway: "true" } }, { "ml-Mlym": { _desired: "ml-Latn", _distance: "20", _oneway: "true" } }, { "mr-Deva": { _desired: "mr-Latn", _distance: "20", _oneway: "true" } }, { "ta-Taml": { _desired: "ta-Latn", _distance: "20", _oneway: "true" } }, { "te-Telu": { _desired: "te-Latn", _distance: "20", _oneway: "true" } }, { "zh-Hans": { _desired: "zh-Latn", _distance: "20", _oneway: "true" } }, { "ja-Jpan": { _desired: "ja-Latn", _distance: "5", _oneway: "true" } }, { "ja-Jpan": { _desired: "ja-Hani", _distance: "5", _oneway: "true" } }, { "ja-Jpan": { _desired: "ja-Hira", _distance: "5", _oneway: "true" } }, { "ja-Jpan": { _desired: "ja-Kana", _distance: "5", _oneway: "true" } }, { "ja-Jpan": { _desired: "ja-Hrkt", _distance: "5", _oneway: "true" } }, { "ja-Hrkt": { _desired: "ja-Hira", _distance: "5", _oneway: "true" } }, { "ja-Hrkt": { _desired: "ja-Kana", _distance: "5", _oneway: "true" } }, { "ko-Kore": { _desired: "ko-Hani", _distance: "5", _oneway: "true" } }, { "ko-Kore": { _desired: "ko-Hang", _distance: "5", _oneway: "true" } }, { "ko-Kore": { _desired: "ko-Jamo", _distance: "5", _oneway: "true" } }, { "ko-Hang": { _desired: "ko-Jamo", _distance: "5", _oneway: "true" } }, { "*-*": { _desired: "*-*", _distance: "50" } }, { "ar-*-$maghreb": { _desired: "ar-*-$maghreb", _distance: "4" } }, { "ar-*-$!maghreb": { _desired: "ar-*-$!maghreb", _distance: "4" } }, { "ar-*-*": { _desired: "ar-*-*", _distance: "5" } }, { "en-*-$enUS": { _desired: "en-*-$enUS", _distance: "4" } }, { "en-*-GB": { _desired: "en-*-$!enUS", _distance: "3" } }, { "en-*-$!enUS": { _desired: "en-*-$!enUS", _distance: "4" } }, { "en-*-*": { _desired: "en-*-*", _distance: "5" } }, { "es-*-$americas": { _desired: "es-*-$americas", _distance: "4" } }, { "es-*-$!americas": { _desired: "es-*-$!americas", _distance: "4" } }, { "es-*-*": { _desired: "es-*-*", _distance: "5" } }, { "pt-*-$americas": { _desired: "pt-*-$americas", _distance: "4" } }, { "pt-*-$!americas": { _desired: "pt-*-$!americas", _distance: "4" } }, { "pt-*-*": { _desired: "pt-*-*", _distance: "5" } }, { "zh-Hant-$cnsar": { _desired: "zh-Hant-$cnsar", _distance: "4" } }, { "zh-Hant-$!cnsar": { _desired: "zh-Hant-$!cnsar", _distance: "4" } }, { "zh-Hant-*": { _desired: "zh-Hant-*", _distance: "5" } }, { "*-*-*": { _desired: "*-*-*", _distance: "4" } }] } } }), tO = { "001": ["001", "001-status-grouping", "002", "005", "009", "011", "013", "014", "015", "017", "018", "019", "021", "029", "030", "034", "035", "039", "053", "054", "057", "061", "142", "143", "145", "150", "151", "154", "155", "AC", "AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AQ", "AR", "AS", "AT", "AU", "AW", "AX", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BL", "BM", "BN", "BO", "BQ", "BR", "BS", "BT", "BV", "BW", "BY", "BZ", "CA", "CC", "CD", "CF", "CG", "CH", "CI", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CU", "CV", "CW", "CX", "CY", "CZ", "DE", "DG", "DJ", "DK", "DM", "DO", "DZ", "EA", "EC", "EE", "EG", "EH", "ER", "ES", "ET", "EU", "EZ", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF", "GG", "GH", "GI", "GL", "GM", "GN", "GP", "GQ", "GR", "GS", "GT", "GU", "GW", "GY", "HK", "HM", "HN", "HR", "HT", "HU", "IC", "ID", "IE", "IL", "IM", "IN", "IO", "IQ", "IR", "IS", "IT", "JE", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KP", "KR", "KW", "KY", "KZ", "LA", "LB", "LC", "LI", "LK", "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "ML", "MM", "MN", "MO", "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PN", "PR", "PS", "PT", "PW", "PY", "QA", "QO", "RE", "RO", "RS", "RU", "RW", "SA", "SB", "SC", "SD", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SO", "SR", "SS", "ST", "SV", "SX", "SY", "SZ", "TA", "TC", "TD", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TW", "TZ", "UA", "UG", "UM", "UN", "US", "UY", "UZ", "VA", "VC", "VE", "VG", "VI", "VN", "VU", "WF", "WS", "XK", "YE", "YT", "ZA", "ZM", "ZW"], "002": ["002", "002-status-grouping", "011", "014", "015", "017", "018", "202", "AO", "BF", "BI", "BJ", "BW", "CD", "CF", "CG", "CI", "CM", "CV", "DJ", "DZ", "EA", "EG", "EH", "ER", "ET", "GA", "GH", "GM", "GN", "GQ", "GW", "IC", "IO", "KE", "KM", "LR", "LS", "LY", "MA", "MG", "ML", "MR", "MU", "MW", "MZ", "NA", "NE", "NG", "RE", "RW", "SC", "SD", "SH", "SL", "SN", "SO", "SS", "ST", "SZ", "TD", "TF", "TG", "TN", "TZ", "UG", "YT", "ZA", "ZM", "ZW"], "003": ["003", "013", "021", "029", "AG", "AI", "AW", "BB", "BL", "BM", "BQ", "BS", "BZ", "CA", "CR", "CU", "CW", "DM", "DO", "GD", "GL", "GP", "GT", "HN", "HT", "JM", "KN", "KY", "LC", "MF", "MQ", "MS", "MX", "NI", "PA", "PM", "PR", "SV", "SX", "TC", "TT", "US", "VC", "VG", "VI"], "005": ["005", "AR", "BO", "BR", "BV", "CL", "CO", "EC", "FK", "GF", "GS", "GY", "PE", "PY", "SR", "UY", "VE"], "009": ["009", "053", "054", "057", "061", "AC", "AQ", "AS", "AU", "CC", "CK", "CP", "CX", "DG", "FJ", "FM", "GU", "HM", "KI", "MH", "MP", "NC", "NF", "NR", "NU", "NZ", "PF", "PG", "PN", "PW", "QO", "SB", "TA", "TK", "TO", "TV", "UM", "VU", "WF", "WS"], "011": ["011", "BF", "BJ", "CI", "CV", "GH", "GM", "GN", "GW", "LR", "ML", "MR", "NE", "NG", "SH", "SL", "SN", "TG"], "013": ["013", "BZ", "CR", "GT", "HN", "MX", "NI", "PA", "SV"], "014": ["014", "BI", "DJ", "ER", "ET", "IO", "KE", "KM", "MG", "MU", "MW", "MZ", "RE", "RW", "SC", "SO", "SS", "TF", "TZ", "UG", "YT", "ZM", "ZW"], "015": ["015", "DZ", "EA", "EG", "EH", "IC", "LY", "MA", "SD", "TN"], "017": ["017", "AO", "CD", "CF", "CG", "CM", "GA", "GQ", "ST", "TD"], "018": ["018", "BW", "LS", "NA", "SZ", "ZA"], "019": ["003", "005", "013", "019", "019-status-grouping", "021", "029", "419", "AG", "AI", "AR", "AW", "BB", "BL", "BM", "BO", "BQ", "BR", "BS", "BV", "BZ", "CA", "CL", "CO", "CR", "CU", "CW", "DM", "DO", "EC", "FK", "GD", "GF", "GL", "GP", "GS", "GT", "GY", "HN", "HT", "JM", "KN", "KY", "LC", "MF", "MQ", "MS", "MX", "NI", "PA", "PE", "PM", "PR", "PY", "SR", "SV", "SX", "TC", "TT", "US", "UY", "VC", "VE", "VG", "VI"], "021": ["021", "BM", "CA", "GL", "PM", "US"], "029": ["029", "AG", "AI", "AW", "BB", "BL", "BQ", "BS", "CU", "CW", "DM", "DO", "GD", "GP", "HT", "JM", "KN", "KY", "LC", "MF", "MQ", "MS", "PR", "SX", "TC", "TT", "VC", "VG", "VI"], "030": ["030", "CN", "HK", "JP", "KP", "KR", "MN", "MO", "TW"], "034": ["034", "AF", "BD", "BT", "IN", "IR", "LK", "MV", "NP", "PK"], "035": ["035", "BN", "ID", "KH", "LA", "MM", "MY", "PH", "SG", "TH", "TL", "VN"], "039": ["039", "AD", "AL", "BA", "ES", "GI", "GR", "HR", "IT", "ME", "MK", "MT", "PT", "RS", "SI", "SM", "VA", "XK"], "053": ["053", "AU", "CC", "CX", "HM", "NF", "NZ"], "054": ["054", "FJ", "NC", "PG", "SB", "VU"], "057": ["057", "FM", "GU", "KI", "MH", "MP", "NR", "PW", "UM"], "061": ["061", "AS", "CK", "NU", "PF", "PN", "TK", "TO", "TV", "WF", "WS"], 142: ["030", "034", "035", "142", "143", "145", "AE", "AF", "AM", "AZ", "BD", "BH", "BN", "BT", "CN", "CY", "GE", "HK", "ID", "IL", "IN", "IQ", "IR", "JO", "JP", "KG", "KH", "KP", "KR", "KW", "KZ", "LA", "LB", "LK", "MM", "MN", "MO", "MV", "MY", "NP", "OM", "PH", "PK", "PS", "QA", "SA", "SG", "SY", "TH", "TJ", "TL", "TM", "TR", "TW", "UZ", "VN", "YE"], 143: ["143", "KG", "KZ", "TJ", "TM", "UZ"], 145: ["145", "AE", "AM", "AZ", "BH", "CY", "GE", "IL", "IQ", "JO", "KW", "LB", "OM", "PS", "QA", "SA", "SY", "TR", "YE"], 150: ["039", "150", "151", "154", "155", "AD", "AL", "AT", "AX", "BA", "BE", "BG", "BY", "CH", "CQ", "CZ", "DE", "DK", "EE", "ES", "FI", "FO", "FR", "GB", "GG", "GI", "GR", "HR", "HU", "IE", "IM", "IS", "IT", "JE", "LI", "LT", "LU", "LV", "MC", "MD", "ME", "MK", "MT", "NL", "NO", "PL", "PT", "RO", "RS", "RU", "SE", "SI", "SJ", "SK", "SM", "UA", "VA", "XK"], 151: ["151", "BG", "BY", "CZ", "HU", "MD", "PL", "RO", "RU", "SK", "UA"], 154: ["154", "AX", "CQ", "DK", "EE", "FI", "FO", "GB", "GG", "IE", "IM", "IS", "JE", "LT", "LV", "NO", "SE", "SJ"], 155: ["155", "AT", "BE", "CH", "DE", "FR", "LI", "LU", "MC", "NL"], 202: ["011", "014", "017", "018", "202", "AO", "BF", "BI", "BJ", "BW", "CD", "CF", "CG", "CI", "CM", "CV", "DJ", "ER", "ET", "GA", "GH", "GM", "GN", "GQ", "GW", "IO", "KE", "KM", "LR", "LS", "MG", "ML", "MR", "MU", "MW", "MZ", "NA", "NE", "NG", "RE", "RW", "SC", "SH", "SL", "SN", "SO", "SS", "ST", "SZ", "TD", "TF", "TG", "TZ", "UG", "YT", "ZA", "ZM", "ZW"], 419: ["005", "013", "029", "419", "AG", "AI", "AR", "AW", "BB", "BL", "BO", "BQ", "BR", "BS", "BV", "BZ", "CL", "CO", "CR", "CU", "CW", "DM", "DO", "EC", "FK", "GD", "GF", "GP", "GS", "GT", "GY", "HN", "HT", "JM", "KN", "KY", "LC", "MF", "MQ", "MS", "MX", "NI", "PA", "PE", "PR", "PY", "SR", "SV", "SX", "TC", "TT", "UY", "VC", "VE", "VG", "VI"], EU: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "EU", "FI", "FR", "GR", "HR", "HU", "IE", "IT", "LT", "LU", "LV", "MT", "NL", "PL", "PT", "RO", "SE", "SI", "SK"], EZ: ["AT", "BE", "CY", "DE", "EE", "ES", "EZ", "FI", "FR", "GR", "IE", "IT", "LT", "LU", "LV", "MT", "NL", "PT", "SI", "SK"], QO: ["AC", "AQ", "CP", "DG", "QO", "TA"], UN: ["AD", "AE", "AF", "AG", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BN", "BO", "BR", "BS", "BT", "BW", "BY", "BZ", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ER", "ES", "ET", "FI", "FJ", "FM", "FR", "GA", "GB", "GD", "GE", "GH", "GM", "GN", "GQ", "GR", "GT", "GW", "GY", "HN", "HR", "HT", "HU", "ID", "IE", "IL", "IN", "IQ", "IR", "IS", "IT", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KP", "KR", "KW", "KZ", "LA", "LB", "LC", "LI", "LK", "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MG", "MH", "MK", "ML", "MM", "MN", "MR", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NE", "NG", "NI", "NL", "NO", "NP", "NR", "NZ", "OM", "PA", "PE", "PG", "PH", "PK", "PL", "PT", "PW", "PY", "QA", "RO", "RS", "RU", "RW", "SA", "SB", "SC", "SD", "SE", "SG", "SI", "SK", "SL", "SM", "SN", "SO", "SR", "SS", "ST", "SV", "SY", "SZ", "TD", "TG", "TH", "TJ", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TZ", "UA", "UG", "UN", "US", "UY", "UZ", "VC", "VE", "VN", "VU", "WS", "YE", "ZA", "ZM", "ZW"] }, tP = /-u(?:-[0-9a-z]{2,8})+/gi;
      function tA(e10, t10, r2) {
        if (void 0 === r2 && (r2 = Error), !e10) throw new r2(t10);
      }
      function tN(e10, t10, r2) {
        var n2 = t10.split("-"), i2 = n2[0], a2 = n2[1], o2 = n2[2], s2 = true;
        if (o2 && "$" === o2[0]) {
          var u2 = "!" !== o2[1], l2 = (u2 ? r2[o2.slice(1)] : r2[o2.slice(2)]).map(function(e11) {
            return tO[e11] || [e11];
          }).reduce(function(e11, t11) {
            return tT(tT([], e11, true), t11, true);
          }, []);
          s2 && (s2 = l2.indexOf(e10.region || "") > 1 == u2);
        } else s2 && (s2 = !e10.region || "*" === o2 || o2 === e10.region);
        return s2 && (s2 = !e10.script || "*" === a2 || a2 === e10.script), s2 && (s2 = !e10.language || "*" === i2 || i2 === e10.language), s2;
      }
      function tL(e10) {
        return [e10.language, e10.script, e10.region].filter(Boolean).join("-");
      }
      function tI(e10, t10, r2) {
        for (var n2 = 0, i2 = r2.matches; n2 < i2.length; n2++) {
          var a2 = i2[n2], o2 = tN(e10, a2.desired, r2.matchVariables) && tN(t10, a2.supported, r2.matchVariables);
          if (a2.oneway || o2 || (o2 = tN(e10, a2.supported, r2.matchVariables) && tN(t10, a2.desired, r2.matchVariables)), o2) {
            var s2 = 10 * a2.distance;
            if (r2.paradigmLocales.indexOf(tL(e10)) > -1 != r2.paradigmLocales.indexOf(tL(t10)) > -1) return s2 - 1;
            return s2;
          }
        }
        throw Error("No matching distance found");
      }
      function tM(e10) {
        return Intl.getCanonicalLocales(e10)[0];
      }
      var tj = r(988);
      function t$(e10, t10, r2) {
        let n2, a2 = new tj({ headers: { "accept-language": e10.get("accept-language") || void 0 } }).languages();
        try {
          let e11 = t10.slice().sort((e12, t11) => t11.length - e12.length);
          n2 = function(e12, t11, r3, n3, a3, o2) {
            "lookup" === r3.localeMatcher ? u2 = function(e13, t12, r4) {
              for (var n4 = { locale: "" }, i2 = 0; i2 < t12.length; i2++) {
                var a4 = t12[i2], o3 = a4.replace(tP, ""), s3 = function(e14, t13) {
                  for (var r5 = t13; ; ) {
                    if (e14.indexOf(r5) > -1) return r5;
                    var n5 = r5.lastIndexOf("-");
                    if (!~n5) return;
                    n5 >= 2 && "-" === r5[n5 - 2] && (n5 -= 2), r5 = r5.slice(0, n5);
                  }
                }(e13, o3);
                if (s3) return n4.locale = s3, a4 !== o3 && (n4.extension = a4.slice(o3.length, a4.length)), n4;
              }
              return n4.locale = r4(), n4;
            }(Array.from(e12), t11, o2) : (c2 = Array.from(e12), p2 = [], h2 = t11.reduce(function(e13, t12) {
              var r4 = t12.replace(tP, "");
              return p2.push(r4), e13[r4] = t12, e13;
            }, {}), (void 0 === _2 && (_2 = 838), y2 = 1 / 0, g2 = { matchedDesiredLocale: "", distances: {} }, p2.forEach(function(e13, t12) {
              g2.distances[e13] || (g2.distances[e13] = {}), c2.forEach(function(r4) {
                var n4, a4, o3, s3, u3, l3, c3 = (n4 = new Intl.Locale(e13).maximize(), a4 = new Intl.Locale(r4).maximize(), o3 = { language: n4.language, script: n4.script || "", region: n4.region || "" }, s3 = { language: a4.language, script: a4.script || "", region: a4.region || "" }, u3 = 0, l3 = function() {
                  var e14, t13;
                  if (!i) {
                    var r5 = null == (t13 = null == (e14 = tk.supplemental.languageMatching["written-new"][0]) ? void 0 : e14.paradigmLocales) ? void 0 : t13._locales.split(" "), n5 = tk.supplemental.languageMatching["written-new"].slice(1, 5);
                    i = { matches: tk.supplemental.languageMatching["written-new"].slice(5).map(function(e15) {
                      var t14 = Object.keys(e15)[0], r6 = e15[t14];
                      return { supported: t14, desired: r6._desired, distance: +r6._distance, oneway: "true" === r6.oneway };
                    }, {}), matchVariables: n5.reduce(function(e15, t14) {
                      var r6 = Object.keys(t14)[0], n6 = t14[r6];
                      return e15[r6.slice(1)] = n6._value.split("+"), e15;
                    }, {}), paradigmLocales: tT(tT([], r5, true), r5.map(function(e15) {
                      return new Intl.Locale(e15.replace(/_/g, "-")).maximize().toString();
                    }), true) };
                  }
                  return i;
                }(), o3.language !== s3.language && (u3 += tI({ language: n4.language, script: "", region: "" }, { language: a4.language, script: "", region: "" }, l3)), o3.script !== s3.script && (u3 += tI({ language: n4.language, script: o3.script, region: "" }, { language: a4.language, script: o3.script, region: "" }, l3)), o3.region !== s3.region && (u3 += tI(o3, s3, l3)), u3 + 0 + 40 * t12);
                g2.distances[e13][r4] = c3, c3 < y2 && (y2 = c3, g2.matchedDesiredLocale = e13, g2.matchedSupportedLocale = r4);
              });
            }), y2 >= _2 && (g2.matchedDesiredLocale = void 0, g2.matchedSupportedLocale = void 0), m2 = g2).matchedSupportedLocale && m2.matchedDesiredLocale && (d2 = m2.matchedSupportedLocale, f2 = h2[m2.matchedDesiredLocale].slice(m2.matchedDesiredLocale.length) || void 0), u2 = d2 ? { locale: d2, extension: f2 } : { locale: o2() }), null == u2 && (u2 = { locale: o2(), extension: "" });
            var s2, u2, l2, c2, d2, f2, p2, h2, _2, y2, g2, m2, v2 = u2.locale, b2 = a3[v2], w2 = { locale: "en", dataLocale: v2 };
            l2 = u2.extension ? function(e13) {
              tA(e13 === e13.toLowerCase(), "Expected extension to be lowercase"), tA("-u-" === e13.slice(0, 3), "Expected extension to be a Unicode locale extension");
              for (var t12, r4 = [], n4 = [], i2 = e13.length, a4 = 3; a4 < i2; ) {
                var o3 = e13.indexOf("-", a4), s3 = void 0;
                s3 = -1 === o3 ? i2 - a4 : o3 - a4;
                var u3 = e13.slice(a4, a4 + s3);
                tA(s3 >= 2, "Expected a subtag to have at least 2 characters"), void 0 === t12 && 2 != s3 ? -1 === r4.indexOf(u3) && r4.push(u3) : 2 === s3 ? (t12 = { key: u3, value: "" }, void 0 === n4.find(function(e14) {
                  return e14.key === (null == t12 ? void 0 : t12.key);
                }) && n4.push(t12)) : (null == t12 ? void 0 : t12.value) === "" ? t12.value = u3 : (tA(void 0 !== t12, "Expected keyword to be defined"), t12.value += "-" + u3), a4 += s3 + 1;
              }
              return { attributes: r4, keywords: n4 };
            }(u2.extension).keywords : [];
            for (var S2 = [], E2 = function(e13) {
              var t12, n4, i2 = null != (s2 = null == b2 ? void 0 : b2[e13]) ? s2 : [];
              tA(Array.isArray(i2), "keyLocaleData for ".concat(e13, " must be an array"));
              var a4 = i2[0];
              tA(void 0 === a4 || "string" == typeof a4, "value must be a string or undefined");
              var o3 = void 0, u3 = l2.find(function(t13) {
                return t13.key === e13;
              });
              if (u3) {
                var c3 = u3.value;
                "" !== c3 ? i2.indexOf(c3) > -1 && (o3 = { key: e13, value: a4 = c3 }) : i2.indexOf("true") > -1 && (o3 = { key: e13, value: a4 = "true" });
              }
              var d3 = r3[e13];
              tA(null == d3 || "string" == typeof d3, "optionsValue must be a string or undefined"), "string" == typeof d3 && (t12 = e13.toLowerCase(), n4 = d3.toLowerCase(), tA(void 0 !== t12, "ukey must be defined"), "" === (d3 = n4) && (d3 = "true")), d3 !== a4 && i2.indexOf(d3) > -1 && (a4 = d3, o3 = void 0), o3 && S2.push(o3), w2[e13] = a4;
            }, C2 = 0; C2 < n3.length; C2++) E2(n3[C2]);
            var R2 = [];
            return S2.length > 0 && (v2 = function(e13, t12, r4) {
              tA(-1 === e13.indexOf("-u-"), "Expected locale to not have a Unicode locale extension");
              for (var n4, i2 = "-u", a4 = 0; a4 < t12.length; a4++) {
                var o3 = t12[a4];
                i2 += "-".concat(o3);
              }
              for (var s3 = 0; s3 < r4.length; s3++) {
                var u3 = r4[s3], l3 = u3.key, c3 = u3.value;
                i2 += "-".concat(l3), "" !== c3 && (i2 += "-".concat(c3));
              }
              if ("-u" === i2) return tM(e13);
              var d3 = e13.indexOf("-x-");
              return tM(-1 === d3 ? e13 + i2 : e13.slice(0, d3) + i2 + e13.slice(d3));
            }(v2, [], S2)), w2.locale = v2, w2;
          }(e11, Intl.getCanonicalLocales(a2), { localeMatcher: "best fit" }, [], {}, function() {
            return r2;
          }).locale;
        } catch {
        }
        return n2;
      }
      function tD(e10, t10) {
        if (e10.localeCookie && t10.has(e10.localeCookie.name)) {
          let r2 = t10.get(e10.localeCookie.name)?.value;
          if (r2 && e10.locales.includes(r2)) return r2;
        }
      }
      function tq(e10, t10, r2, n2) {
        let i2;
        return n2 && (i2 = tS(n2, e10.locales, e10.localePrefix)?.locale), !i2 && e10.localeDetection && (i2 = tD(e10, r2)), !i2 && e10.localeDetection && (i2 = t$(t10, e10.locales, e10.defaultLocale)), i2 || (i2 = e10.defaultLocale), i2;
      }
      var tU = r(499), tG = r(149);
      let tB = r(496).s;
      function tH(e10, t10, r2) {
        void 0 === r2 && (r2 = tU.Q.TemporaryRedirect);
        let n2 = Object.defineProperty(Error(tG.oJ), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        return n2.digest = tG.oJ + ";" + t10 + ";" + e10 + ";" + r2 + ";", n2;
      }
      function tz(e10, t10) {
        var r2;
        throw null != t10 || (t10 = (null == tB || null == (r2 = tB.getStore()) ? void 0 : r2.isAction) ? tG.zB.push : tG.zB.replace), tH(e10, t10, tU.Q.TemporaryRedirect);
      }
      function tF(e10, t10) {
        throw void 0 === t10 && (t10 = tG.zB.replace), tH(e10, t10, tU.Q.PermanentRedirect);
      }
      var tW = r(441);
      tW.s8, tW.s8, tW.s8, r(678).X;
      var tV = r(457), tK = r.t(tV, 2)["use".trim()];
      let tX = (0, r(450).YR)(function() {
        throw Error(`Attempted to call the default export of "/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next-intl@4.3.3_next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1._43af9fa8fb36507fb4dd6e3d331a5e9b/node_modules/next-intl/dist/esm/production/navigation/shared/BaseLink.js" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.`);
      }, "/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next-intl@4.3.3_next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1._43af9fa8fb36507fb4dd6e3d331a5e9b/node_modules/next-intl/dist/esm/production/navigation/shared/BaseLink.js", "default");
      function tY(e10) {
        let t10 = new URLSearchParams();
        for (let [r2, n2] of Object.entries(e10)) Array.isArray(n2) ? n2.forEach((e11) => {
          t10.append(r2, String(e11));
        }) : t10.set(r2, String(n2));
        return "?" + t10.toString();
      }
      var tJ = r(794);
      function tZ(e10, t10, r2, n2) {
        var i2 = null == n2 || "number" == typeof n2 || "boolean" == typeof n2 ? n2 : r2(n2), a2 = t10.get(i2);
        return void 0 === a2 && (a2 = e10.call(this, n2), t10.set(i2, a2)), a2;
      }
      function tQ(e10, t10, r2) {
        var n2 = Array.prototype.slice.call(arguments, 3), i2 = r2(n2), a2 = t10.get(i2);
        return void 0 === a2 && (a2 = e10.apply(this, n2), t10.set(i2, a2)), a2;
      }
      var t0 = function() {
        return JSON.stringify(arguments);
      }, t1 = function() {
        function e10() {
          this.cache = /* @__PURE__ */ Object.create(null);
        }
        return e10.prototype.get = function(e11) {
          return this.cache[e11];
        }, e10.prototype.set = function(e11, t10) {
          this.cache[e11] = t10;
        }, e10;
      }(), t3 = { create: function() {
        return new t1();
      } }, t2 = { variadic: function(e10, t10) {
        var r2, n2;
        return r2 = t10.cache.create(), n2 = t10.serializer, tQ.bind(this, e10, r2, n2);
      }, monadic: function(e10, t10) {
        var r2, n2;
        return r2 = t10.cache.create(), n2 = t10.serializer, tZ.bind(this, e10, r2, n2);
      } }, t4 = function(e10) {
        return e10.MISSING_MESSAGE = "MISSING_MESSAGE", e10.MISSING_FORMAT = "MISSING_FORMAT", e10.ENVIRONMENT_FALLBACK = "ENVIRONMENT_FALLBACK", e10.INSUFFICIENT_PATH = "INSUFFICIENT_PATH", e10.INVALID_MESSAGE = "INVALID_MESSAGE", e10.INVALID_KEY = "INVALID_KEY", e10.FORMATTING_ERROR = "FORMATTING_ERROR", e10;
      }(t4 || {});
      function t5(...e10) {
        return e10.filter(Boolean).join(".");
      }
      function t6(e10) {
        return t5(e10.namespace, e10.key);
      }
      function t9(e10) {
        console.error(e10);
      }
      function t8(e10, t10) {
        var r2, n2, i2, a2, o2;
        return r2 = (...t11) => new e10(...t11), n2 = t10, a2 = (i2 = { cache: { create: () => ({ get: (e11) => n2[e11], set(e11, t11) {
          n2[e11] = t11;
        } }) }, strategy: t2.variadic }).cache ? i2.cache : t3, o2 = i2 && i2.serializer ? i2.serializer : t0, (i2 && i2.strategy ? i2.strategy : function(e11, t11) {
          var r3, n3, i3 = 1 === e11.length ? tZ : tQ;
          return r3 = t11.cache.create(), n3 = t11.serializer, i3.bind(this, e11, r3, n3);
        })(r2, { cache: a2, serializer: o2 });
      }
      let t7 = { current: null }, re = "function" == typeof tV.cache ? tV.cache : (e10) => e10, rt = console.warn;
      function rr(e10) {
        return function(...t10) {
          rt(e10(...t10));
        };
      }
      re((e10) => {
        try {
          rt(t7.current);
        } finally {
          t7.current = null;
        }
      });
      let rn = /* @__PURE__ */ new WeakMap(), ri = rr(function(e10, t10) {
        let r2 = e10 ? `Route "${e10}" ` : "This route ";
        return Object.defineProperty(Error(`${r2}used ${t10}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), "__NEXT_ERROR_CODE", { value: "E223", enumerable: false, configurable: true });
      });
      function ra() {
        return this.getAll().map((e10) => [e10.name, e10]).values();
      }
      function ro(e10) {
        for (let e11 of this.getAll()) this.delete(e11.name);
        return e10;
      }
      let rs = /* @__PURE__ */ new WeakMap();
      function ru(e10) {
        let t10 = rs.get(e10);
        if (t10) return t10;
        let r2 = Promise.resolve(e10);
        return rs.set(e10, r2), Object.defineProperties(r2, { append: { value: e10.append.bind(e10) }, delete: { value: e10.delete.bind(e10) }, get: { value: e10.get.bind(e10) }, has: { value: e10.has.bind(e10) }, set: { value: e10.set.bind(e10) }, getSetCookie: { value: e10.getSetCookie.bind(e10) }, forEach: { value: e10.forEach.bind(e10) }, keys: { value: e10.keys.bind(e10) }, values: { value: e10.values.bind(e10) }, entries: { value: e10.entries.bind(e10) }, [Symbol.iterator]: { value: e10[Symbol.iterator].bind(e10) } }), r2;
      }
      function rl(e10) {
        return "string" == typeof e10 ? `'${e10}'` : "...";
      }
      let rc = rr(rd);
      function rd(e10, t10) {
        let r2 = e10 ? `Route "${e10}" ` : "This route ";
        return Object.defineProperty(Error(`${r2}used ${t10}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), "__NEXT_ERROR_CODE", { value: "E277", enumerable: false, configurable: true });
      }
      function rf() {
        let e10 = workAsyncStorage.getStore(), t10 = workUnitAsyncStorage.getStore();
        switch ((!e10 || !t10) && throwForMissingRequestStore("draftMode"), t10.type) {
          case "request":
            return rp(t10.draftMode, e10);
          case "cache":
          case "unstable-cache":
            let r2 = getDraftModeProviderForCacheScope(e10, t10);
            if (r2) return rp(r2, e10);
          case "prerender":
          case "prerender-ppr":
          case "prerender-legacy":
            return r_(null);
          default:
            return t10;
        }
      }
      function rp(e10, t10) {
        let r2, n2 = rh.get(rf);
        return n2 || (r2 = r_(e10), rh.set(e10, r2), r2);
      }
      r(654);
      let rh = /* @__PURE__ */ new WeakMap();
      function r_(e10) {
        let t10 = new ry(e10), r2 = Promise.resolve(t10);
        return Object.defineProperty(r2, "isEnabled", { get: () => t10.isEnabled, set(e11) {
          Object.defineProperty(r2, "isEnabled", { value: e11, writable: true, enumerable: true });
        }, enumerable: true, configurable: true }), r2.enable = t10.enable.bind(t10), r2.disable = t10.disable.bind(t10), r2;
      }
      class ry {
        constructor(e10) {
          this._provider = e10;
        }
        get isEnabled() {
          return null !== this._provider && this._provider.isEnabled;
        }
        enable() {
          rm("draftMode().enable()"), null !== this._provider && this._provider.enable();
        }
        disable() {
          rm("draftMode().disable()"), null !== this._provider && this._provider.disable();
        }
      }
      let rg = rr(function(e10, t10) {
        let r2 = e10 ? `Route "${e10}" ` : "This route ";
        return Object.defineProperty(Error(`${r2}used ${t10}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), "__NEXT_ERROR_CODE", { value: "E377", enumerable: false, configurable: true });
      });
      function rm(e10) {
        let t10 = workAsyncStorage.getStore(), r2 = workUnitAsyncStorage.getStore();
        if (t10) {
          if (r2) {
            if ("cache" === r2.type) throw Object.defineProperty(Error(`Route ${t10.route} used "${e10}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), "__NEXT_ERROR_CODE", { value: "E246", enumerable: false, configurable: true });
            else if ("unstable-cache" === r2.type) throw Object.defineProperty(Error(`Route ${t10.route} used "${e10}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), "__NEXT_ERROR_CODE", { value: "E259", enumerable: false, configurable: true });
            else if ("after" === r2.phase) throw Object.defineProperty(Error(`Route ${t10.route} used "${e10}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), "__NEXT_ERROR_CODE", { value: "E348", enumerable: false, configurable: true });
          }
          if (t10.dynamicShouldError) throw Object.defineProperty(new StaticGenBailoutError(`Route ${t10.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e10}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), "__NEXT_ERROR_CODE", { value: "E553", enumerable: false, configurable: true });
          if (r2) {
            if ("prerender" === r2.type) {
              let n2 = Object.defineProperty(Error(`Route ${t10.route} used ${e10} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), "__NEXT_ERROR_CODE", { value: "E126", enumerable: false, configurable: true });
              abortAndThrowOnSynchronousRequestDataAccess(t10.route, e10, n2, r2);
            } else if ("prerender-ppr" === r2.type) postponeWithTracking(t10.route, e10, r2.dynamicTracking);
            else if ("prerender-legacy" === r2.type) {
              r2.revalidate = 0;
              let n2 = Object.defineProperty(new DynamicServerError(`Route ${t10.route} couldn't be rendered statically because it used \`${e10}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), "__NEXT_ERROR_CODE", { value: "E558", enumerable: false, configurable: true });
              throw t10.dynamicUsageDescription = e10, t10.dynamicUsageStack = n2.stack, n2;
            }
          }
        }
      }
      let rv = (0, tV.cache)(function() {
        return { locale: void 0 };
      }), rb = (0, tV.cache)(async function() {
        let e10 = function() {
          let e11 = J.J.getStore(), t10 = Z.FP.getStore();
          if (e11) {
            if (t10 && "after" === t10.phase && !function() {
              let e12 = eZ.getStore();
              return (null == e12 ? void 0 : e12.rootTaskSpawnPhase) === "action";
            }()) throw Object.defineProperty(Error(`Route ${e11.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), "__NEXT_ERROR_CODE", { value: "E367", enumerable: false, configurable: true });
            if (e11.forceStatic) return ru(Y.seal(new Headers({})));
            if (t10) {
              if ("cache" === t10.type) throw Object.defineProperty(Error(`Route ${e11.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), "__NEXT_ERROR_CODE", { value: "E304", enumerable: false, configurable: true });
              else if ("unstable-cache" === t10.type) throw Object.defineProperty(Error(`Route ${e11.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), "__NEXT_ERROR_CODE", { value: "E127", enumerable: false, configurable: true });
            }
            if (e11.dynamicShouldError) throw Object.defineProperty(new tn.f(`Route ${e11.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), "__NEXT_ERROR_CODE", { value: "E525", enumerable: false, configurable: true });
            if (t10) if ("prerender" === t10.type) {
              var r2 = e11.route, n2 = t10;
              let i2 = rs.get(n2);
              if (i2) return i2;
              let a2 = (0, ti.W)(n2.renderSignal, "`headers()`");
              return rs.set(n2, a2), Object.defineProperties(a2, { append: { value: function() {
                let e12 = `\`headers().append(${rl(arguments[0])}, ...)\``, t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, delete: { value: function() {
                let e12 = `\`headers().delete(${rl(arguments[0])})\``, t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, get: { value: function() {
                let e12 = `\`headers().get(${rl(arguments[0])})\``, t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, has: { value: function() {
                let e12 = `\`headers().has(${rl(arguments[0])})\``, t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, set: { value: function() {
                let e12 = `\`headers().set(${rl(arguments[0])}, ...)\``, t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, getSetCookie: { value: function() {
                let e12 = "`headers().getSetCookie()`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, forEach: { value: function() {
                let e12 = "`headers().forEach(...)`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, keys: { value: function() {
                let e12 = "`headers().keys()`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, values: { value: function() {
                let e12 = "`headers().values()`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, entries: { value: function() {
                let e12 = "`headers().entries()`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } }, [Symbol.iterator]: { value: function() {
                let e12 = "`headers()[Symbol.iterator]()`", t11 = rd(r2, e12);
                (0, tr.t3)(r2, e12, t11, n2);
              } } }), a2;
            } else "prerender-ppr" === t10.type ? (0, tr.Ui)(e11.route, "headers", t10.dynamicTracking) : "prerender-legacy" === t10.type && (0, tr.xI)("headers", e11, t10);
            (0, tr.Pk)(e11, t10);
          }
          return ru((0, Z.XN)("headers").headers);
        }();
        return tm(e10) ? await e10 : e10;
      }), rw = (0, tV.cache)(async function() {
        let e10;
        try {
          e10 = (await rb()).get(to) || void 0;
        } catch (e11) {
          if (e11 instanceof Error && "DYNAMIC_SERVER_USAGE" === e11.digest) {
            let t10 = Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering", { cause: e11 });
            throw t10.digest = e11.digest, t10;
          }
          throw e11;
        }
        return e10;
      });
      async function rS() {
        return rv().locale || await rw();
      }
      let rE = async ({ requestLocale: e10 }) => {
        let t10 = await e10;
        t10 && rP.locales.includes(t10) || (t10 = rP.defaultLocale), ["zh-CN"].includes(t10) && (t10 = "zh"), rP.locales.includes(t10) || (t10 = "en");
        try {
          let e11 = (await r(684)(`./${t10.toLowerCase()}.json`)).default, n2 = {};
          try {
            let e12 = (await r(962)(`./${t10.toLowerCase()}.json`)).default;
            n2["ai-dashboard"] = e12;
          } catch (e12) {
            try {
              let e13 = (await Promise.resolve().then(r.t.bind(r, 51, 19))).default;
              n2["ai-dashboard"] = e13;
            } catch (e13) {
            }
          }
          return { locale: t10, messages: { ...e11, ...n2 } };
        } catch (e11) {
          return { locale: "en", messages: (await Promise.resolve().then(r.t.bind(r, 673, 19))).default };
        }
      }, rC = (0, tV.cache)(function() {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
      }), rR = (0, tV.cache)(async function(e10, t10) {
        let r2 = e10({ locale: t10, get requestLocale() {
          return t10 ? Promise.resolve(t10) : rS();
        } });
        if (tm(r2) && (r2 = await r2), !r2.locale) throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");
        return r2;
      }), rx = (0, tV.cache)(function(e10) {
        return { getDateTimeFormat: t8(Intl.DateTimeFormat, e10.dateTime), getNumberFormat: t8(Intl.NumberFormat, e10.number), getPluralRules: t8(Intl.PluralRules, e10.pluralRules), getRelativeTimeFormat: t8(Intl.RelativeTimeFormat, e10.relativeTime), getListFormat: t8(Intl.ListFormat, e10.list), getDisplayNames: t8(Intl.DisplayNames, e10.displayNames) };
      }), rT = (0, tV.cache)(function() {
        return { dateTime: {}, number: {}, message: {}, relativeTime: {}, pluralRules: {}, list: {}, displayNames: {} };
      }), rk = (0, tV.cache)(async function(e10) {
        let t10 = await rR(rE, e10);
        return { ...function({ formats: e11, getMessageFallback: t11, messages: r2, onError: n2, ...i2 }) {
          return { ...i2, formats: e11 || void 0, messages: r2 || void 0, onError: n2 || t9, getMessageFallback: t11 || t6 };
        }(t10), _formatters: rx(rT()), timeZone: t10.timeZone || rC() };
      });
      async function rO() {
        return (await rk()).locale;
      }
      let rP = { locales: ["en", "zh"], defaultLocale: "en", localePrefix: "as-needed", pathnames: { en: { "privacy-policy": "/privacy-policy", "terms-of-service": "/terms-of-service" } }, localeDetection: false }, { Link: rA, redirect: rN, usePathname: rL, useRouter: rI } = function(e10) {
        let { config: t10, ...r2 } = function(e11, t11) {
          let r3 = ta(t11 || {}), n3 = r3.pathnames, i2 = (0, tV.forwardRef)(function({ href: t12, locale: i3, ...o3 }, s2) {
            let u2, l2;
            "object" == typeof t12 ? (u2 = t12.pathname, l2 = t12.params) : u2 = t12;
            let c2 = ts(t12), d2 = e11(), f2 = tm(d2) ? tK(d2) : d2, p2 = c2 ? a2({ locale: i3 || f2, href: null == n3 ? u2 : { pathname: u2, params: l2 }, forcePrefix: null != i3 || void 0 }) : u2;
            return (0, tJ.jsx)(tX, { ref: s2, href: "object" == typeof t12 ? { ...t12, pathname: p2 } : p2, locale: i3, localeCookie: r3.localeCookie, ...o3 });
          });
          function a2(e12) {
            let t12, { forcePrefix: i3, href: a3, locale: o3 } = e12;
            return null == n3 ? "object" == typeof a3 ? (t12 = a3.pathname, a3.query && (t12 += tY(a3.query))) : t12 = a3 : t12 = function({ pathname: e13, locale: t13, params: r4, pathnames: n4, query: i4 }) {
              function a4(e14) {
                let a5, o4 = n4[e14];
                return o4 ? (a5 = tl(o4, t13, e14), r4 && Object.entries(r4).forEach(([e15, t14]) => {
                  let r5, n5;
                  Array.isArray(t14) ? (r5 = `(\\[)?\\[...${e15}\\](\\])?`, n5 = t14.map((e16) => String(e16)).join("/")) : (r5 = `\\[${e15}\\]`, n5 = String(t14)), a5 = a5.replace(RegExp(r5, "g"), n5);
                }), a5 = (a5 = a5.replace(/\[\[\.\.\..+\]\]/g, "")).split("/").map((e15) => encodeURIComponent(e15)).join("/")) : a5 = e14, a5 = tc(a5), i4 && (a5 += tY(i4)), a5;
              }
              if ("string" == typeof e13) return a4(e13);
              {
                let { pathname: t14, ...r5 } = e13;
                return { ...r5, pathname: a4(t14) };
              }
            }({ locale: o3, ..."string" == typeof a3 ? { pathname: a3 } : a3, pathnames: r3.pathnames }), function(e13, t13, r4, n4) {
              let i4, { mode: a4 } = r4.localePrefix;
              return void 0 !== n4 ? i4 = n4 : ts(e13) && ("always" === a4 ? i4 = true : "as-needed" === a4 && (i4 = r4.domains ? !r4.domains.some((e14) => e14.defaultLocale === t13) : t13 !== r4.defaultLocale)), i4 ? tu(tf(t13, r4.localePrefix), e13) : e13;
            }(t12, o3, r3, i3);
          }
          function o2(e12) {
            return function(t12, ...r4) {
              return e12(a2(t12), ...r4);
            };
          }
          return { config: r3, Link: i2, redirect: o2(tz), permanentRedirect: o2(tF), getPathname: a2 };
        }(rO, e10);
        function n2(e11) {
          return () => {
            throw Error(`\`${e11}\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`);
          };
        }
        return { ...r2, usePathname: n2("usePathname"), useRouter: n2("useRouter") };
      }(rP), rM = function(e10) {
        let t10 = ta(e10);
        return function(e11) {
          var r2, n2;
          let i2;
          try {
            i2 = decodeURI(e11.nextUrl.pathname);
          } catch {
            return z.next();
          }
          let a2 = i2.replace(/\\/g, "%5C").replace(/\/+/g, "/"), { domain: o2, locale: s2 } = (r2 = e11.headers, n2 = e11.cookies, t10.domains ? function(e12, t11, r3, n3) {
            let i3, a3 = function(e13, t12) {
              let r4 = tC(e13);
              if (r4) return t12.find((e14) => e14.domain === r4);
            }(t11, e12.domains);
            if (!a3) return { locale: tq(e12, t11, r3, n3) };
            if (n3) {
              let t12 = tS(n3, e12.locales, e12.localePrefix, a3)?.locale;
              if (t12) {
                if (!tR(t12, a3)) return { locale: t12, domain: a3 };
                i3 = t12;
              }
            }
            if (!i3 && e12.localeDetection) {
              let t12 = tD(e12, r3);
              t12 && tR(t12, a3) && (i3 = t12);
            }
            if (!i3 && e12.localeDetection) {
              let e13 = t$(t11, a3.locales, a3.defaultLocale);
              e13 && (i3 = e13);
            }
            return i3 || (i3 = a3.defaultLocale), { locale: i3, domain: a3 };
          }(t10, r2, n2, a2) : { locale: tq(t10, r2, n2, a2) }), u2 = o2 ? o2.defaultLocale === s2 : s2 === t10.defaultLocale, l2 = t10.domains?.filter((e12) => tR(s2, e12)) || [], c2 = null != t10.domains && !o2;
          function d2(t11) {
            var r3;
            let n3 = new URL(t11, e11.url);
            e11.nextUrl.basePath && (r3 = n3.pathname, n3.pathname = tc(e11.nextUrl.basePath + r3));
            let i3 = new Headers(e11.headers);
            return i3.set(to, s2), z.rewrite(n3, { request: { headers: i3 } });
          }
          function f2(r3, n3) {
            var i3;
            let a3 = new URL(r3, e11.url);
            if (a3.pathname = tc(a3.pathname), l2.length > 0 && !n3 && o2) {
              let e12 = tx(o2, s2, l2);
              e12 && (n3 = e12.domain, e12.defaultLocale === s2 && "as-needed" === t10.localePrefix.mode && (a3.pathname = tb(a3.pathname, t10.locales, t10.localePrefix)));
            }
            return n3 && (a3.host = n3, e11.headers.get("x-forwarded-host")) && (a3.protocol = e11.headers.get("x-forwarded-proto") ?? e11.nextUrl.protocol, a3.port = n3.split(":")[1] ?? e11.headers.get("x-forwarded-port") ?? ""), e11.nextUrl.basePath && (i3 = a3.pathname, a3.pathname = tc(e11.nextUrl.basePath + i3)), v2 = true, z.redirect(a3.toString());
          }
          let p2 = tb(a2, t10.locales, t10.localePrefix), h2 = tS(a2, t10.locales, t10.localePrefix, o2), _2 = null != h2, y2 = "never" === t10.localePrefix.mode || u2 && "as-needed" === t10.localePrefix.mode, g2, m2, v2, b2 = p2, w2 = t10.pathnames;
          if (w2) {
            let r3;
            if ([r3, m2] = function(e12, t11, r4) {
              for (let n3 of Object.keys(e12).sort(tg)) {
                let i3 = e12[n3];
                if ("string" == typeof i3) {
                  if (td(i3, t11)) return [void 0, n3];
                } else {
                  let a3 = Object.entries(i3), o3 = a3.findIndex(([e13]) => e13 === r4);
                  for (let [r5] of (o3 > 0 && a3.unshift(a3.splice(o3, 1)[0]), a3)) if (td(tl(e12[n3], r5, n3), t11)) return [r5, n3];
                }
              }
              for (let r5 of Object.keys(e12)) if (td(r5, t11)) return [void 0, r5];
              return [void 0, void 0];
            }(w2, p2, s2), m2) {
              let n3 = w2[m2], i3 = tl(n3, s2, m2);
              if (td(i3, p2)) b2 = tv(p2, i3, m2);
              else {
                let a3;
                a3 = r3 ? tl(n3, r3, m2) : m2;
                let o3 = y2 ? void 0 : tf(s2, t10.localePrefix);
                g2 = f2(tE(tv(p2, a3, i3), o3, e11.nextUrl.search));
              }
            }
          }
          if (!g2) if ("/" !== b2 || _2) {
            let r3 = tE(b2, `/${s2}`, e11.nextUrl.search);
            if (_2) {
              let n3 = tE(p2, h2.prefix, e11.nextUrl.search);
              if ("never" === t10.localePrefix.mode) g2 = f2(tE(p2, void 0, e11.nextUrl.search));
              else if (h2.exact) if (u2 && y2) g2 = f2(tE(p2, void 0, e11.nextUrl.search));
              else if (t10.domains) {
                let e12 = tx(o2, h2.locale, l2);
                g2 = o2?.domain === e12?.domain || c2 ? d2(r3) : f2(n3, e12?.domain);
              } else g2 = d2(r3);
              else g2 = f2(n3);
            } else g2 = y2 ? d2(r3) : f2(tE(p2, tf(s2, t10.localePrefix), e11.nextUrl.search));
          } else g2 = y2 ? d2(tE(b2, `/${s2}`, e11.nextUrl.search)) : f2(tE(p2, tf(s2, t10.localePrefix), e11.nextUrl.search));
          return function(e12, t11, r3, n3, i3) {
            if (!n3.localeCookie) return;
            let { name: a3, ...o3 } = n3.localeCookie, s3 = t$(e12.headers, i3?.locales || n3.locales, n3.defaultLocale), u3 = e12.cookies.has(a3), l3 = u3 && e12.cookies.get(a3)?.value !== r3;
            (u3 ? l3 : s3 !== r3) && t11.cookies.set(a3, r3, { path: e12.nextUrl.basePath || void 0, ...o3 });
          }(e11, g2, s2, t10, o2), !v2 && "never" !== t10.localePrefix.mode && t10.alternateLinks && t10.locales.length > 1 && g2.headers.set("Link", function({ internalTemplateName: e12, localizedPathnames: t11, request: r3, resolvedLocale: n3, routing: i3 }) {
            let a3 = r3.nextUrl.clone(), o3 = tC(r3.headers);
            function s3(e13, t12) {
              var n4;
              return e13.pathname = tc(e13.pathname), r3.nextUrl.basePath && ((e13 = new URL(e13)).pathname = (n4 = e13.pathname, tc(r3.nextUrl.basePath + n4))), `<${e13.toString()}>; rel="alternate"; hreflang="${t12}"`;
            }
            function u3(r4, i4) {
              return t11 && "object" == typeof t11 ? tv(r4, t11[n3] ?? e12, t11[i4] ?? e12) : r4;
            }
            o3 && (a3.port = "", a3.host = o3), a3.protocol = r3.headers.get("x-forwarded-proto") ?? a3.protocol, a3.pathname = tb(a3.pathname, i3.locales, i3.localePrefix);
            let l3 = tw(i3.locales, i3.localePrefix, false).flatMap(([e13, r4]) => {
              let n4;
              function o4(e14) {
                return "/" === e14 ? r4 : r4 + e14;
              }
              if (i3.domains) return i3.domains.filter((t12) => tR(e13, t12)).map((t12) => ((n4 = new URL(a3)).port = "", n4.host = t12.domain, n4.pathname = u3(a3.pathname, e13), e13 === t12.defaultLocale && "always" !== i3.localePrefix.mode || (n4.pathname = o4(n4.pathname)), s3(n4, e13)));
              {
                let r5;
                r5 = t11 && "object" == typeof t11 ? u3(a3.pathname, e13) : a3.pathname, e13 === i3.defaultLocale && "always" !== i3.localePrefix.mode || (r5 = o4(r5)), n4 = new URL(r5, a3);
              }
              return s3(n4, e13);
            });
            if (!i3.domains || 0 === i3.domains.length) {
              let e13 = u3(a3.pathname, i3.defaultLocale);
              if (e13) {
                let t12 = new URL(e13, a3);
                l3.push(s3(t12, "x-default"));
              }
            }
            return l3.join(", ");
          }({ routing: t10, internalTemplateName: m2, localizedPathnames: null != m2 && w2 ? w2[m2] : void 0, request: e11, resolvedLocale: s2 })), g2;
        };
      }(rP), rj = { matcher: ["/", "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*", "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)"] };
      r(5);
      let r$ = { ...a }, rD = r$.middleware || r$.default, rq = "/middleware";
      if ("function" != typeof rD) throw Object.defineProperty(Error(`The Middleware "${rq}" must export a \`middleware\` or a \`default\` function`), "__NEXT_ERROR_CODE", { value: "E120", enumerable: false, configurable: true });
      function rU(e10) {
        return tt({ ...e10, page: rq, handler: async (...e11) => {
          try {
            return await rD(...e11);
          } catch (i2) {
            let t10 = e11[0], r2 = new URL(t10.url), n2 = r2.pathname + r2.search;
            throw await l(i2, { path: n2, method: t10.method, headers: Object.fromEntries(t10.headers.entries()) }, { routerKind: "Pages Router", routePath: "/middleware", routeType: "middleware", revalidateReason: void 0 }), i2;
          }
        } });
      }
    }, 144: (e) => {
      "use strict";
      e.exports = r, e.exports.preferredCharsets = r;
      var t = /^\s*([^\s;]+)\s*(?:;(.*))?$/;
      function r(e2, r2) {
        var o = function(e3) {
          for (var r3 = e3.split(","), n2 = 0, i2 = 0; n2 < r3.length; n2++) {
            var a2 = function(e4, r4) {
              var n3 = t.exec(e4);
              if (!n3) return null;
              var i3 = n3[1], a3 = 1;
              if (n3[2]) for (var o2 = n3[2].split(";"), s2 = 0; s2 < o2.length; s2++) {
                var u = o2[s2].trim().split("=");
                if ("q" === u[0]) {
                  a3 = parseFloat(u[1]);
                  break;
                }
              }
              return { charset: i3, q: a3, i: r4 };
            }(r3[n2].trim(), n2);
            a2 && (r3[i2++] = a2);
          }
          return r3.length = i2, r3;
        }(void 0 === e2 ? "*" : e2 || "");
        if (!r2) return o.filter(a).sort(n).map(i);
        var s = r2.map(function(e3, t2) {
          for (var r3 = { o: -1, q: 0, s: 0 }, n2 = 0; n2 < o.length; n2++) {
            var i2 = function(e4, t3, r4) {
              var n3 = 0;
              if (t3.charset.toLowerCase() === e4.toLowerCase()) n3 |= 1;
              else if ("*" !== t3.charset) return null;
              return { i: r4, o: t3.i, q: t3.q, s: n3 };
            }(e3, o[n2], t2);
            i2 && 0 > (r3.s - i2.s || r3.q - i2.q || r3.o - i2.o) && (r3 = i2);
          }
          return r3;
        });
        return s.filter(a).sort(n).map(function(e3) {
          return r2[s.indexOf(e3)];
        });
      }
      function n(e2, t2) {
        return t2.q - e2.q || t2.s - e2.s || e2.o - t2.o || e2.i - t2.i || 0;
      }
      function i(e2) {
        return e2.charset;
      }
      function a(e2) {
        return e2.q > 0;
      }
    }, 149: (e, t, r) => {
      "use strict";
      r.d(t, { nJ: () => o, oJ: () => i, zB: () => a });
      var n = r(499);
      let i = "NEXT_REDIRECT";
      var a = function(e2) {
        return e2.push = "push", e2.replace = "replace", e2;
      }({});
      function o(e2) {
        if ("object" != typeof e2 || null === e2 || !("digest" in e2) || "string" != typeof e2.digest) return false;
        let t2 = e2.digest.split(";"), [r2, a2] = t2, o2 = t2.slice(2, -2).join(";"), s = Number(t2.at(-2));
        return r2 === i && ("replace" === a2 || "push" === a2) && "string" == typeof o2 && !isNaN(s) && s in n.Q;
      }
    }, 213: (e, t, r) => {
      "use strict";
      r.d(t, { XN: () => i, FP: () => n });
      let n = (0, r(342).xl)();
      function i(e2) {
        let t2 = n.getStore();
        switch (!t2 && function(e3) {
          throw Object.defineProperty(Error(`\`${e3}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`), "__NEXT_ERROR_CODE", { value: "E251", enumerable: false, configurable: true });
        }(e2), t2.type) {
          case "request":
          default:
            return t2;
          case "prerender":
          case "prerender-ppr":
          case "prerender-legacy":
            throw Object.defineProperty(Error(`\`${e2}\` cannot be called inside a prerender. This is a bug in Next.js.`), "__NEXT_ERROR_CODE", { value: "E401", enumerable: false, configurable: true });
          case "cache":
            throw Object.defineProperty(Error(`\`${e2}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`), "__NEXT_ERROR_CODE", { value: "E37", enumerable: false, configurable: true });
          case "unstable-cache":
            throw Object.defineProperty(Error(`\`${e2}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), "__NEXT_ERROR_CODE", { value: "E69", enumerable: false, configurable: true });
        }
      }
    }, 223: (e, t, r) => {
      "use strict";
      r.d(t, { t3: () => u, I3: () => d, Ui: () => l, xI: () => o, Pk: () => s });
      var n = r(457), i = r(654);
      r(348), r(213), r(989), r(911);
      let a = "function" == typeof n.unstable_postpone;
      function o(e2, t2, r2) {
        let n2 = Object.defineProperty(new i.F(`Route ${t2.route} couldn't be rendered statically because it used \`${e2}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), "__NEXT_ERROR_CODE", { value: "E558", enumerable: false, configurable: true });
        throw r2.revalidate = 0, t2.dynamicUsageDescription = e2, t2.dynamicUsageStack = n2.stack, n2;
      }
      function s(e2, t2) {
        t2 && "cache" !== t2.type && "unstable-cache" !== t2.type && ("prerender" === t2.type || "prerender-legacy" === t2.type) && (t2.revalidate = 0);
      }
      function u(e2, t2, r2, n2) {
        if (false === n2.controller.signal.aborted) {
          let i2 = n2.dynamicTracking;
          i2 && null === i2.syncDynamicErrorWithStack && (i2.syncDynamicExpression = t2, i2.syncDynamicErrorWithStack = r2, true === n2.validating && (i2.syncDynamicLogged = true)), function(e3, t3, r3) {
            let n3 = p(`Route ${e3} needs to bail out of prerendering at this point because it used ${t3}.`);
            r3.controller.abort(n3);
            let i3 = r3.dynamicTracking;
            i3 && i3.dynamicAccesses.push({ stack: i3.isDebugDynamicAccesses ? Error().stack : void 0, expression: t3 });
          }(e2, t2, n2);
        }
        throw p(`Route ${e2} needs to bail out of prerendering at this point because it used ${t2}.`);
      }
      function l(e2, t2, r2) {
        (function() {
          if (!a) throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"), "__NEXT_ERROR_CODE", { value: "E224", enumerable: false, configurable: true });
        })(), r2 && r2.dynamicAccesses.push({ stack: r2.isDebugDynamicAccesses ? Error().stack : void 0, expression: t2 }), n.unstable_postpone(c(e2, t2));
      }
      function c(e2, t2) {
        return `Route ${e2} needs to bail out of prerendering at this point because it used ${t2}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;
      }
      function d(e2) {
        return "object" == typeof e2 && null !== e2 && "string" == typeof e2.message && f(e2.message);
      }
      function f(e2) {
        return e2.includes("needs to bail out of prerendering at this point because it used") && e2.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error");
      }
      if (false === f(c("%%%", "^^^"))) throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"), "__NEXT_ERROR_CODE", { value: "E296", enumerable: false, configurable: true });
      function p(e2) {
        let t2 = Object.defineProperty(Error(e2), "__NEXT_ERROR_CODE", { value: "E394", enumerable: false, configurable: true });
        return t2.digest = "NEXT_PRERENDER_INTERRUPTED", t2;
      }
      RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`), RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`), RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`);
    }, 224: (e) => {
      "use strict";
      e.exports = n, e.exports.preferredLanguages = n;
      var t = /^\s*([^\s\-;]+)(?:-([^\s;]+))?\s*(?:;(.*))?$/;
      function r(e2, r2) {
        var n2 = t.exec(e2);
        if (!n2) return null;
        var i2 = n2[1], a2 = n2[2], o2 = i2;
        a2 && (o2 += "-" + a2);
        var s = 1;
        if (n2[3]) for (var u = n2[3].split(";"), l = 0; l < u.length; l++) {
          var c = u[l].split("=");
          "q" === c[0] && (s = parseFloat(c[1]));
        }
        return { prefix: i2, suffix: a2, q: s, i: r2, full: o2 };
      }
      function n(e2, t2) {
        var n2 = function(e3) {
          for (var t3 = e3.split(","), n3 = 0, i2 = 0; n3 < t3.length; n3++) {
            var a2 = r(t3[n3].trim(), n3);
            a2 && (t3[i2++] = a2);
          }
          return t3.length = i2, t3;
        }(void 0 === e2 ? "*" : e2 || "");
        if (!t2) return n2.filter(o).sort(i).map(a);
        var s = t2.map(function(e3, t3) {
          for (var i2 = { o: -1, q: 0, s: 0 }, a2 = 0; a2 < n2.length; a2++) {
            var o2 = function(e4, t4, n3) {
              var i3 = r(e4);
              if (!i3) return null;
              var a3 = 0;
              if (t4.full.toLowerCase() === i3.full.toLowerCase()) a3 |= 4;
              else if (t4.prefix.toLowerCase() === i3.full.toLowerCase()) a3 |= 2;
              else if (t4.full.toLowerCase() === i3.prefix.toLowerCase()) a3 |= 1;
              else if ("*" !== t4.full) return null;
              return { i: n3, o: t4.i, q: t4.q, s: a3 };
            }(e3, n2[a2], t3);
            o2 && 0 > (i2.s - o2.s || i2.q - o2.q || i2.o - o2.o) && (i2 = o2);
          }
          return i2;
        });
        return s.filter(o).sort(i).map(function(e3) {
          return t2[s.indexOf(e3)];
        });
      }
      function i(e2, t2) {
        return t2.q - e2.q || t2.s - e2.s || e2.o - t2.o || e2.i - t2.i || 0;
      }
      function a(e2) {
        return e2.full;
      }
      function o(e2) {
        return e2.q > 0;
      }
    }, 230: (e, t, r) => {
      "use strict";
      var n = r(457), i = Symbol.for("react.transitional.element");
      if (Symbol.for("react.fragment"), !n.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE) throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
      t.jsx = function(e2, t2, r2) {
        var n2 = null;
        if (void 0 !== r2 && (n2 = "" + r2), void 0 !== t2.key && (n2 = "" + t2.key), "key" in t2) for (var a in r2 = {}, t2) "key" !== a && (r2[a] = t2[a]);
        else r2 = t2;
        return { $$typeof: i, type: e2, key: n2, ref: void 0 !== (t2 = r2.ref) ? t2 : null, props: r2 };
      };
    }, 245: (e, t, r) => {
      "use strict";
      e.exports = r(49);
    }, 270: (e, t, r) => {
      var n;
      (() => {
        var i = { 226: function(i2, a2) {
          !function(o2, s) {
            "use strict";
            var u = "function", l = "undefined", c = "object", d = "string", f = "major", p = "model", h = "name", _ = "type", y = "vendor", g = "version", m = "architecture", v = "console", b = "mobile", w = "tablet", S = "smarttv", E = "wearable", C = "embedded", R = "Amazon", x = "Apple", T = "ASUS", k = "BlackBerry", O = "Browser", P = "Chrome", A = "Firefox", N = "Google", L = "Huawei", I = "Microsoft", M = "Motorola", j = "Opera", $ = "Samsung", D = "Sharp", q = "Sony", U = "Xiaomi", G = "Zebra", B = "Facebook", H = "Chromium OS", z = "Mac OS", F = function(e2, t2) {
              var r2 = {};
              for (var n2 in e2) t2[n2] && t2[n2].length % 2 == 0 ? r2[n2] = t2[n2].concat(e2[n2]) : r2[n2] = e2[n2];
              return r2;
            }, W = function(e2) {
              for (var t2 = {}, r2 = 0; r2 < e2.length; r2++) t2[e2[r2].toUpperCase()] = e2[r2];
              return t2;
            }, V = function(e2, t2) {
              return typeof e2 === d && -1 !== K(t2).indexOf(K(e2));
            }, K = function(e2) {
              return e2.toLowerCase();
            }, X = function(e2, t2) {
              if (typeof e2 === d) return e2 = e2.replace(/^\s\s*/, ""), typeof t2 === l ? e2 : e2.substring(0, 350);
            }, Y = function(e2, t2) {
              for (var r2, n2, i3, a3, o3, l2, d2 = 0; d2 < t2.length && !o3; ) {
                var f2 = t2[d2], p2 = t2[d2 + 1];
                for (r2 = n2 = 0; r2 < f2.length && !o3 && f2[r2]; ) if (o3 = f2[r2++].exec(e2)) for (i3 = 0; i3 < p2.length; i3++) l2 = o3[++n2], typeof (a3 = p2[i3]) === c && a3.length > 0 ? 2 === a3.length ? typeof a3[1] == u ? this[a3[0]] = a3[1].call(this, l2) : this[a3[0]] = a3[1] : 3 === a3.length ? typeof a3[1] !== u || a3[1].exec && a3[1].test ? this[a3[0]] = l2 ? l2.replace(a3[1], a3[2]) : void 0 : this[a3[0]] = l2 ? a3[1].call(this, l2, a3[2]) : void 0 : 4 === a3.length && (this[a3[0]] = l2 ? a3[3].call(this, l2.replace(a3[1], a3[2])) : s) : this[a3] = l2 || s;
                d2 += 2;
              }
            }, J = function(e2, t2) {
              for (var r2 in t2) if (typeof t2[r2] === c && t2[r2].length > 0) {
                for (var n2 = 0; n2 < t2[r2].length; n2++) if (V(t2[r2][n2], e2)) return "?" === r2 ? s : r2;
              } else if (V(t2[r2], e2)) return "?" === r2 ? s : r2;
              return e2;
            }, Z = { ME: "4.90", "NT 3.11": "NT3.51", "NT 4.0": "NT4.0", 2e3: "NT 5.0", XP: ["NT 5.1", "NT 5.2"], Vista: "NT 6.0", 7: "NT 6.1", 8: "NT 6.2", 8.1: "NT 6.3", 10: ["NT 6.4", "NT 10.0"], RT: "ARM" }, Q = { browser: [[/\b(?:crmo|crios)\/([\w\.]+)/i], [g, [h, "Chrome"]], [/edg(?:e|ios|a)?\/([\w\.]+)/i], [g, [h, "Edge"]], [/(opera mini)\/([-\w\.]+)/i, /(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i, /(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i], [h, g], [/opios[\/ ]+([\w\.]+)/i], [g, [h, j + " Mini"]], [/\bopr\/([\w\.]+)/i], [g, [h, j]], [/(kindle)\/([\w\.]+)/i, /(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i, /(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i, /(ba?idubrowser)[\/ ]?([\w\.]+)/i, /(?:ms|\()(ie) ([\w\.]+)/i, /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i, /(heytap|ovi)browser\/([\d\.]+)/i, /(weibo)__([\d\.]+)/i], [h, g], [/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i], [g, [h, "UC" + O]], [/microm.+\bqbcore\/([\w\.]+)/i, /\bqbcore\/([\w\.]+).+microm/i], [g, [h, "WeChat(Win) Desktop"]], [/micromessenger\/([\w\.]+)/i], [g, [h, "WeChat"]], [/konqueror\/([\w\.]+)/i], [g, [h, "Konqueror"]], [/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i], [g, [h, "IE"]], [/ya(?:search)?browser\/([\w\.]+)/i], [g, [h, "Yandex"]], [/(avast|avg)\/([\w\.]+)/i], [[h, /(.+)/, "$1 Secure " + O], g], [/\bfocus\/([\w\.]+)/i], [g, [h, A + " Focus"]], [/\bopt\/([\w\.]+)/i], [g, [h, j + " Touch"]], [/coc_coc\w+\/([\w\.]+)/i], [g, [h, "Coc Coc"]], [/dolfin\/([\w\.]+)/i], [g, [h, "Dolphin"]], [/coast\/([\w\.]+)/i], [g, [h, j + " Coast"]], [/miuibrowser\/([\w\.]+)/i], [g, [h, "MIUI " + O]], [/fxios\/([-\w\.]+)/i], [g, [h, A]], [/\bqihu|(qi?ho?o?|360)browser/i], [[h, "360 " + O]], [/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i], [[h, /(.+)/, "$1 " + O], g], [/(comodo_dragon)\/([\w\.]+)/i], [[h, /_/g, " "], g], [/(electron)\/([\w\.]+) safari/i, /(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i, /m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i], [h, g], [/(metasr)[\/ ]?([\w\.]+)/i, /(lbbrowser)/i, /\[(linkedin)app\]/i], [h], [/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i], [[h, B], g], [/(kakao(?:talk|story))[\/ ]([\w\.]+)/i, /(naver)\(.*?(\d+\.[\w\.]+).*\)/i, /safari (line)\/([\w\.]+)/i, /\b(line)\/([\w\.]+)\/iab/i, /(chromium|instagram)[\/ ]([-\w\.]+)/i], [h, g], [/\bgsa\/([\w\.]+) .*safari\//i], [g, [h, "GSA"]], [/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i], [g, [h, "TikTok"]], [/headlesschrome(?:\/([\w\.]+)| )/i], [g, [h, P + " Headless"]], [/ wv\).+(chrome)\/([\w\.]+)/i], [[h, P + " WebView"], g], [/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i], [g, [h, "Android " + O]], [/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i], [h, g], [/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i], [g, [h, "Mobile Safari"]], [/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i], [g, h], [/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i], [h, [g, J, { "1.0": "/8", 1.2: "/1", 1.3: "/3", "2.0": "/412", "2.0.2": "/416", "2.0.3": "/417", "2.0.4": "/419", "?": "/" }]], [/(webkit|khtml)\/([\w\.]+)/i], [h, g], [/(navigator|netscape\d?)\/([-\w\.]+)/i], [[h, "Netscape"], g], [/mobile vr; rv:([\w\.]+)\).+firefox/i], [g, [h, A + " Reality"]], [/ekiohf.+(flow)\/([\w\.]+)/i, /(swiftfox)/i, /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i, /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i, /(firefox)\/([\w\.]+)/i, /(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i, /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i, /(links) \(([\w\.]+)/i, /panasonic;(viera)/i], [h, g], [/(cobalt)\/([\w\.]+)/i], [h, [g, /master.|lts./, ""]]], cpu: [[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i], [[m, "amd64"]], [/(ia32(?=;))/i], [[m, K]], [/((?:i[346]|x)86)[;\)]/i], [[m, "ia32"]], [/\b(aarch64|arm(v?8e?l?|_?64))\b/i], [[m, "arm64"]], [/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i], [[m, "armhf"]], [/windows (ce|mobile); ppc;/i], [[m, "arm"]], [/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i], [[m, /ower/, "", K]], [/(sun4\w)[;\)]/i], [[m, "sparc"]], [/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i], [[m, K]]], device: [[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i], [p, [y, $], [_, w]], [/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i, /samsung[- ]([-\w]+)/i, /sec-(sgh\w+)/i], [p, [y, $], [_, b]], [/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i], [p, [y, x], [_, b]], [/\((ipad);[-\w\),; ]+apple/i, /applecoremedia\/[\w\.]+ \((ipad)/i, /\b(ipad)\d\d?,\d\d?[;\]].+ios/i], [p, [y, x], [_, w]], [/(macintosh);/i], [p, [y, x]], [/\b(sh-?[altvz]?\d\d[a-ekm]?)/i], [p, [y, D], [_, b]], [/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i], [p, [y, L], [_, w]], [/(?:huawei|honor)([-\w ]+)[;\)]/i, /\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i], [p, [y, L], [_, b]], [/\b(poco[\w ]+)(?: bui|\))/i, /\b; (\w+) build\/hm\1/i, /\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i, /\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i, /\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i], [[p, /_/g, " "], [y, U], [_, b]], [/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i], [[p, /_/g, " "], [y, U], [_, w]], [/; (\w+) bui.+ oppo/i, /\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i], [p, [y, "OPPO"], [_, b]], [/vivo (\w+)(?: bui|\))/i, /\b(v[12]\d{3}\w?[at])(?: bui|;)/i], [p, [y, "Vivo"], [_, b]], [/\b(rmx[12]\d{3})(?: bui|;|\))/i], [p, [y, "Realme"], [_, b]], [/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i, /\bmot(?:orola)?[- ](\w*)/i, /((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i], [p, [y, M], [_, b]], [/\b(mz60\d|xoom[2 ]{0,2}) build\//i], [p, [y, M], [_, w]], [/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i], [p, [y, "LG"], [_, w]], [/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i, /\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i, /\blg-?([\d\w]+) bui/i], [p, [y, "LG"], [_, b]], [/(ideatab[-\w ]+)/i, /lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i], [p, [y, "Lenovo"], [_, w]], [/(?:maemo|nokia).*(n900|lumia \d+)/i, /nokia[-_ ]?([-\w\.]*)/i], [[p, /_/g, " "], [y, "Nokia"], [_, b]], [/(pixel c)\b/i], [p, [y, N], [_, w]], [/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i], [p, [y, N], [_, b]], [/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i], [p, [y, q], [_, b]], [/sony tablet [ps]/i, /\b(?:sony)?sgp\w+(?: bui|\))/i], [[p, "Xperia Tablet"], [y, q], [_, w]], [/ (kb2005|in20[12]5|be20[12][59])\b/i, /(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i], [p, [y, "OnePlus"], [_, b]], [/(alexa)webm/i, /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i, /(kf[a-z]+)( bui|\)).+silk\//i], [p, [y, R], [_, w]], [/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i], [[p, /(.+)/g, "Fire Phone $1"], [y, R], [_, b]], [/(playbook);[-\w\),; ]+(rim)/i], [p, y, [_, w]], [/\b((?:bb[a-f]|st[hv])100-\d)/i, /\(bb10; (\w+)/i], [p, [y, k], [_, b]], [/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i], [p, [y, T], [_, w]], [/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i], [p, [y, T], [_, b]], [/(nexus 9)/i], [p, [y, "HTC"], [_, w]], [/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i, /(zte)[- ]([\w ]+?)(?: bui|\/|\))/i, /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i], [y, [p, /_/g, " "], [_, b]], [/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i], [p, [y, "Acer"], [_, w]], [/droid.+; (m[1-5] note) bui/i, /\bmz-([-\w]{2,})/i], [p, [y, "Meizu"], [_, b]], [/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i, /(hp) ([\w ]+\w)/i, /(asus)-?(\w+)/i, /(microsoft); (lumia[\w ]+)/i, /(lenovo)[-_ ]?([-\w]+)/i, /(jolla)/i, /(oppo) ?([\w ]+) bui/i], [y, p, [_, b]], [/(kobo)\s(ereader|touch)/i, /(archos) (gamepad2?)/i, /(hp).+(touchpad(?!.+tablet)|tablet)/i, /(kindle)\/([\w\.]+)/i, /(nook)[\w ]+build\/(\w+)/i, /(dell) (strea[kpr\d ]*[\dko])/i, /(le[- ]+pan)[- ]+(\w{1,9}) bui/i, /(trinity)[- ]*(t\d{3}) bui/i, /(gigaset)[- ]+(q\w{1,9}) bui/i, /(vodafone) ([\w ]+)(?:\)| bui)/i], [y, p, [_, w]], [/(surface duo)/i], [p, [y, I], [_, w]], [/droid [\d\.]+; (fp\du?)(?: b|\))/i], [p, [y, "Fairphone"], [_, b]], [/(u304aa)/i], [p, [y, "AT&T"], [_, b]], [/\bsie-(\w*)/i], [p, [y, "Siemens"], [_, b]], [/\b(rct\w+) b/i], [p, [y, "RCA"], [_, w]], [/\b(venue[\d ]{2,7}) b/i], [p, [y, "Dell"], [_, w]], [/\b(q(?:mv|ta)\w+) b/i], [p, [y, "Verizon"], [_, w]], [/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i], [p, [y, "Barnes & Noble"], [_, w]], [/\b(tm\d{3}\w+) b/i], [p, [y, "NuVision"], [_, w]], [/\b(k88) b/i], [p, [y, "ZTE"], [_, w]], [/\b(nx\d{3}j) b/i], [p, [y, "ZTE"], [_, b]], [/\b(gen\d{3}) b.+49h/i], [p, [y, "Swiss"], [_, b]], [/\b(zur\d{3}) b/i], [p, [y, "Swiss"], [_, w]], [/\b((zeki)?tb.*\b) b/i], [p, [y, "Zeki"], [_, w]], [/\b([yr]\d{2}) b/i, /\b(dragon[- ]+touch |dt)(\w{5}) b/i], [[y, "Dragon Touch"], p, [_, w]], [/\b(ns-?\w{0,9}) b/i], [p, [y, "Insignia"], [_, w]], [/\b((nxa|next)-?\w{0,9}) b/i], [p, [y, "NextBook"], [_, w]], [/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i], [[y, "Voice"], p, [_, b]], [/\b(lvtel\-)?(v1[12]) b/i], [[y, "LvTel"], p, [_, b]], [/\b(ph-1) /i], [p, [y, "Essential"], [_, b]], [/\b(v(100md|700na|7011|917g).*\b) b/i], [p, [y, "Envizen"], [_, w]], [/\b(trio[-\w\. ]+) b/i], [p, [y, "MachSpeed"], [_, w]], [/\btu_(1491) b/i], [p, [y, "Rotor"], [_, w]], [/(shield[\w ]+) b/i], [p, [y, "Nvidia"], [_, w]], [/(sprint) (\w+)/i], [y, p, [_, b]], [/(kin\.[onetw]{3})/i], [[p, /\./g, " "], [y, I], [_, b]], [/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i], [p, [y, G], [_, w]], [/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i], [p, [y, G], [_, b]], [/smart-tv.+(samsung)/i], [y, [_, S]], [/hbbtv.+maple;(\d+)/i], [[p, /^/, "SmartTV"], [y, $], [_, S]], [/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i], [[y, "LG"], [_, S]], [/(apple) ?tv/i], [y, [p, x + " TV"], [_, S]], [/crkey/i], [[p, P + "cast"], [y, N], [_, S]], [/droid.+aft(\w)( bui|\))/i], [p, [y, R], [_, S]], [/\(dtv[\);].+(aquos)/i, /(aquos-tv[\w ]+)\)/i], [p, [y, D], [_, S]], [/(bravia[\w ]+)( bui|\))/i], [p, [y, q], [_, S]], [/(mitv-\w{5}) bui/i], [p, [y, U], [_, S]], [/Hbbtv.*(technisat) (.*);/i], [y, p, [_, S]], [/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i, /hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i], [[y, X], [p, X], [_, S]], [/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i], [[_, S]], [/(ouya)/i, /(nintendo) ([wids3utch]+)/i], [y, p, [_, v]], [/droid.+; (shield) bui/i], [p, [y, "Nvidia"], [_, v]], [/(playstation [345portablevi]+)/i], [p, [y, q], [_, v]], [/\b(xbox(?: one)?(?!; xbox))[\); ]/i], [p, [y, I], [_, v]], [/((pebble))app/i], [y, p, [_, E]], [/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i], [p, [y, x], [_, E]], [/droid.+; (glass) \d/i], [p, [y, N], [_, E]], [/droid.+; (wt63?0{2,3})\)/i], [p, [y, G], [_, E]], [/(quest( 2| pro)?)/i], [p, [y, B], [_, E]], [/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i], [y, [_, C]], [/(aeobc)\b/i], [p, [y, R], [_, C]], [/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i], [p, [_, b]], [/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i], [p, [_, w]], [/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i], [[_, w]], [/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i], [[_, b]], [/(android[-\w\. ]{0,9});.+buil/i], [p, [y, "Generic"]]], engine: [[/windows.+ edge\/([\w\.]+)/i], [g, [h, "EdgeHTML"]], [/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i], [g, [h, "Blink"]], [/(presto)\/([\w\.]+)/i, /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i, /ekioh(flow)\/([\w\.]+)/i, /(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i, /(icab)[\/ ]([23]\.[\d\.]+)/i, /\b(libweb)/i], [h, g], [/rv\:([\w\.]{1,9})\b.+(gecko)/i], [g, h]], os: [[/microsoft (windows) (vista|xp)/i], [h, g], [/(windows) nt 6\.2; (arm)/i, /(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i, /(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i], [h, [g, J, Z]], [/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i], [[h, "Windows"], [g, J, Z]], [/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i, /ios;fbsv\/([\d\.]+)/i, /cfnetwork\/.+darwin/i], [[g, /_/g, "."], [h, "iOS"]], [/(mac os x) ?([\w\. ]*)/i, /(macintosh|mac_powerpc\b)(?!.+haiku)/i], [[h, z], [g, /_/g, "."]], [/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i], [g, h], [/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i, /(blackberry)\w*\/([\w\.]*)/i, /(tizen|kaios)[\/ ]([\w\.]+)/i, /\((series40);/i], [h, g], [/\(bb(10);/i], [g, [h, k]], [/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i], [g, [h, "Symbian"]], [/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i], [g, [h, A + " OS"]], [/web0s;.+rt(tv)/i, /\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i], [g, [h, "webOS"]], [/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i], [g, [h, "watchOS"]], [/crkey\/([\d\.]+)/i], [g, [h, P + "cast"]], [/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i], [[h, H], g], [/panasonic;(viera)/i, /(netrange)mmh/i, /(nettv)\/(\d+\.[\w\.]+)/i, /(nintendo|playstation) ([wids345portablevuch]+)/i, /(xbox); +xbox ([^\);]+)/i, /\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i, /(mint)[\/\(\) ]?(\w*)/i, /(mageia|vectorlinux)[; ]/i, /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i, /(hurd|linux) ?([\w\.]*)/i, /(gnu) ?([\w\.]*)/i, /\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i, /(haiku) (\w+)/i], [h, g], [/(sunos) ?([\w\.\d]*)/i], [[h, "Solaris"], g], [/((?:open)?solaris)[-\/ ]?([\w\.]*)/i, /(aix) ((\d)(?=\.|\)| )[\w\.])*/i, /\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, /(unix) ?([\w\.]*)/i], [h, g]] }, ee = function(e2, t2) {
              if (typeof e2 === c && (t2 = e2, e2 = s), !(this instanceof ee)) return new ee(e2, t2).getResult();
              var r2 = typeof o2 !== l && o2.navigator ? o2.navigator : s, n2 = e2 || (r2 && r2.userAgent ? r2.userAgent : ""), i3 = r2 && r2.userAgentData ? r2.userAgentData : s, a3 = t2 ? F(Q, t2) : Q, v2 = r2 && r2.userAgent == n2;
              return this.getBrowser = function() {
                var e3, t3 = {};
                return t3[h] = s, t3[g] = s, Y.call(t3, n2, a3.browser), t3[f] = typeof (e3 = t3[g]) === d ? e3.replace(/[^\d\.]/g, "").split(".")[0] : s, v2 && r2 && r2.brave && typeof r2.brave.isBrave == u && (t3[h] = "Brave"), t3;
              }, this.getCPU = function() {
                var e3 = {};
                return e3[m] = s, Y.call(e3, n2, a3.cpu), e3;
              }, this.getDevice = function() {
                var e3 = {};
                return e3[y] = s, e3[p] = s, e3[_] = s, Y.call(e3, n2, a3.device), v2 && !e3[_] && i3 && i3.mobile && (e3[_] = b), v2 && "Macintosh" == e3[p] && r2 && typeof r2.standalone !== l && r2.maxTouchPoints && r2.maxTouchPoints > 2 && (e3[p] = "iPad", e3[_] = w), e3;
              }, this.getEngine = function() {
                var e3 = {};
                return e3[h] = s, e3[g] = s, Y.call(e3, n2, a3.engine), e3;
              }, this.getOS = function() {
                var e3 = {};
                return e3[h] = s, e3[g] = s, Y.call(e3, n2, a3.os), v2 && !e3[h] && i3 && "Unknown" != i3.platform && (e3[h] = i3.platform.replace(/chrome os/i, H).replace(/macos/i, z)), e3;
              }, this.getResult = function() {
                return { ua: this.getUA(), browser: this.getBrowser(), engine: this.getEngine(), os: this.getOS(), device: this.getDevice(), cpu: this.getCPU() };
              }, this.getUA = function() {
                return n2;
              }, this.setUA = function(e3) {
                return n2 = typeof e3 === d && e3.length > 350 ? X(e3, 350) : e3, this;
              }, this.setUA(n2), this;
            };
            ee.VERSION = "1.0.35", ee.BROWSER = W([h, g, f]), ee.CPU = W([m]), ee.DEVICE = W([p, y, _, v, b, S, w, E, C]), ee.ENGINE = ee.OS = W([h, g]), typeof a2 !== l ? (i2.exports && (a2 = i2.exports = ee), a2.UAParser = ee) : r.amdO ? void 0 === (n = function() {
              return ee;
            }.call(t, r, t, e)) || (e.exports = n) : typeof o2 !== l && (o2.UAParser = ee);
            var et = typeof o2 !== l && (o2.jQuery || o2.Zepto);
            if (et && !et.ua) {
              var er = new ee();
              et.ua = er.getResult(), et.ua.get = function() {
                return er.getUA();
              }, et.ua.set = function(e2) {
                er.setUA(e2);
                var t2 = er.getResult();
                for (var r2 in t2) et.ua[r2] = t2[r2];
              };
            }
          }("object" == typeof window ? window : this);
        } }, a = {};
        function o(e2) {
          var t2 = a[e2];
          if (void 0 !== t2) return t2.exports;
          var r2 = a[e2] = { exports: {} }, n2 = true;
          try {
            i[e2].call(r2.exports, r2, r2.exports, o), n2 = false;
          } finally {
            n2 && delete a[e2];
          }
          return r2.exports;
        }
        o.ab = "//", e.exports = o(226);
      })();
    }, 304: (e) => {
      "use strict";
      e.exports = n, e.exports.preferredMediaTypes = n;
      var t = /^\s*([^\s\/;]+)\/([^;\s]+)\s*(?:;(.*))?$/;
      function r(e2, r2) {
        var n2 = t.exec(e2);
        if (!n2) return null;
        var i2 = /* @__PURE__ */ Object.create(null), a2 = 1, o2 = n2[2], l = n2[1];
        if (n2[3]) for (var c = function(e3) {
          for (var t2 = e3.split(";"), r3 = 1, n3 = 0; r3 < t2.length; r3++) s(t2[n3]) % 2 == 0 ? t2[++n3] = t2[r3] : t2[n3] += ";" + t2[r3];
          t2.length = n3 + 1;
          for (var r3 = 0; r3 < t2.length; r3++) t2[r3] = t2[r3].trim();
          return t2;
        }(n2[3]).map(u), d = 0; d < c.length; d++) {
          var f = c[d], p = f[0].toLowerCase(), h = f[1], _ = h && '"' === h[0] && '"' === h[h.length - 1] ? h.slice(1, -1) : h;
          if ("q" === p) {
            a2 = parseFloat(_);
            break;
          }
          i2[p] = _;
        }
        return { type: l, subtype: o2, params: i2, q: a2, i: r2 };
      }
      function n(e2, t2) {
        var n2 = function(e3) {
          for (var t3 = function(e4) {
            for (var t4 = e4.split(","), r2 = 1, n4 = 0; r2 < t4.length; r2++) s(t4[n4]) % 2 == 0 ? t4[++n4] = t4[r2] : t4[n4] += "," + t4[r2];
            return t4.length = n4 + 1, t4;
          }(e3), n3 = 0, i2 = 0; n3 < t3.length; n3++) {
            var a2 = r(t3[n3].trim(), n3);
            a2 && (t3[i2++] = a2);
          }
          return t3.length = i2, t3;
        }(void 0 === e2 ? "*/*" : e2 || "");
        if (!t2) return n2.filter(o).sort(i).map(a);
        var u2 = t2.map(function(e3, t3) {
          for (var i2 = { o: -1, q: 0, s: 0 }, a2 = 0; a2 < n2.length; a2++) {
            var o2 = function(e4, t4, n3) {
              var i3 = r(e4), a3 = 0;
              if (!i3) return null;
              if (t4.type.toLowerCase() == i3.type.toLowerCase()) a3 |= 4;
              else if ("*" != t4.type) return null;
              if (t4.subtype.toLowerCase() == i3.subtype.toLowerCase()) a3 |= 2;
              else if ("*" != t4.subtype) return null;
              var o3 = Object.keys(t4.params);
              if (o3.length > 0) if (!o3.every(function(e5) {
                return "*" == t4.params[e5] || (t4.params[e5] || "").toLowerCase() == (i3.params[e5] || "").toLowerCase();
              })) return null;
              else a3 |= 1;
              return { i: n3, o: t4.i, q: t4.q, s: a3 };
            }(e3, n2[a2], t3);
            o2 && 0 > (i2.s - o2.s || i2.q - o2.q || i2.o - o2.o) && (i2 = o2);
          }
          return i2;
        });
        return u2.filter(o).sort(i).map(function(e3) {
          return t2[u2.indexOf(e3)];
        });
      }
      function i(e2, t2) {
        return t2.q - e2.q || t2.s - e2.s || e2.o - t2.o || e2.i - t2.i || 0;
      }
      function a(e2) {
        return e2.type + "/" + e2.subtype;
      }
      function o(e2) {
        return e2.q > 0;
      }
      function s(e2) {
        for (var t2 = 0, r2 = 0; -1 !== (r2 = e2.indexOf('"', r2)); ) t2++, r2++;
        return t2;
      }
      function u(e2) {
        var t2, r2, n2 = e2.indexOf("=");
        return -1 === n2 ? t2 = e2 : (t2 = e2.slice(0, n2), r2 = e2.slice(n2 + 1)), [t2, r2];
      }
    }, 318: (e, t, r) => {
      "use strict";
      var n = r(356).Buffer;
      Object.defineProperty(t, "__esModule", { value: true }), !function(e2, t2) {
        for (var r2 in t2) Object.defineProperty(e2, r2, { enumerable: true, get: t2[r2] });
      }(t, { handleFetch: function() {
        return s;
      }, interceptFetch: function() {
        return u;
      }, reader: function() {
        return a;
      } });
      let i = r(395), a = { url: (e2) => e2.url, header: (e2, t2) => e2.headers.get(t2) };
      async function o(e2, t2) {
        let { url: r2, method: i2, headers: a2, body: o2, cache: s2, credentials: u2, integrity: l, mode: c, redirect: d, referrer: f, referrerPolicy: p } = t2;
        return { testData: e2, api: "fetch", request: { url: r2, method: i2, headers: [...Array.from(a2), ["next-test-stack", function() {
          let e3 = (Error().stack ?? "").split("\n");
          for (let t3 = 1; t3 < e3.length; t3++) if (e3[t3].length > 0) {
            e3 = e3.slice(t3);
            break;
          }
          return (e3 = (e3 = (e3 = e3.filter((e4) => !e4.includes("/next/dist/"))).slice(0, 5)).map((e4) => e4.replace("webpack-internal:///(rsc)/", "").trim())).join("    ");
        }()]], body: o2 ? n.from(await t2.arrayBuffer()).toString("base64") : null, cache: s2, credentials: u2, integrity: l, mode: c, redirect: d, referrer: f, referrerPolicy: p } };
      }
      async function s(e2, t2) {
        let r2 = (0, i.getTestReqInfo)(t2, a);
        if (!r2) return e2(t2);
        let { testData: s2, proxyPort: u2 } = r2, l = await o(s2, t2), c = await e2(`http://localhost:${u2}`, { method: "POST", body: JSON.stringify(l), next: { internal: true } });
        if (!c.ok) throw Object.defineProperty(Error(`Proxy request failed: ${c.status}`), "__NEXT_ERROR_CODE", { value: "E146", enumerable: false, configurable: true });
        let d = await c.json(), { api: f } = d;
        switch (f) {
          case "continue":
            return e2(t2);
          case "abort":
          case "unhandled":
            throw Object.defineProperty(Error(`Proxy request aborted [${t2.method} ${t2.url}]`), "__NEXT_ERROR_CODE", { value: "E145", enumerable: false, configurable: true });
        }
        let { status: p, headers: h, body: _ } = d.response;
        return new Response(_ ? n.from(_, "base64") : null, { status: p, headers: new Headers(h) });
      }
      function u(e2) {
        return r.g.fetch = function(t2, r2) {
          var n2;
          return (null == r2 || null == (n2 = r2.next) ? void 0 : n2.internal) ? e2(t2, r2) : s(e2, new Request(t2, r2));
        }, () => {
          r.g.fetch = e2;
        };
      }
    }, 338: (e, t, r) => {
      "use strict";
      r.r(t), r.d(t, { DiagConsoleLogger: () => I, DiagLogLevel: () => n, INVALID_SPANID: () => ed, INVALID_SPAN_CONTEXT: () => ep, INVALID_TRACEID: () => ef, ProxyTracer: () => eA, ProxyTracerProvider: () => eL, ROOT_CONTEXT: () => N, SamplingDecision: () => o, SpanKind: () => s, SpanStatusCode: () => u, TraceFlags: () => a, ValueType: () => i, baggageEntryMetadataFromString: () => P, context: () => eU, createContextKey: () => A, createNoopMeter: () => ee, createTraceState: () => eq, default: () => e3, defaultTextMapGetter: () => et, defaultTextMapSetter: () => er, diag: () => eG, isSpanContextValid: () => ex, isValidSpanId: () => eR, isValidTraceId: () => eC, metrics: () => ez, propagation: () => eQ, trace: () => e1 });
      var n, i, a, o, s, u, l = "object" == typeof globalThis ? globalThis : "object" == typeof self ? self : "object" == typeof window ? window : "object" == typeof r.g ? r.g : {}, c = "1.9.0", d = /^(\d+)\.(\d+)\.(\d+)(-(.+))?$/, f = function(e2) {
        var t2 = /* @__PURE__ */ new Set([e2]), r2 = /* @__PURE__ */ new Set(), n2 = e2.match(d);
        if (!n2) return function() {
          return false;
        };
        var i2 = { major: +n2[1], minor: +n2[2], patch: +n2[3], prerelease: n2[4] };
        if (null != i2.prerelease) return function(t3) {
          return t3 === e2;
        };
        function a2(e4) {
          return r2.add(e4), false;
        }
        return function(e4) {
          if (t2.has(e4)) return true;
          if (r2.has(e4)) return false;
          var n3 = e4.match(d);
          if (!n3) return a2(e4);
          var o2 = { major: +n3[1], minor: +n3[2], patch: +n3[3], prerelease: n3[4] };
          if (null != o2.prerelease || i2.major !== o2.major) return a2(e4);
          if (0 === i2.major) return i2.minor === o2.minor && i2.patch <= o2.patch ? (t2.add(e4), true) : a2(e4);
          return i2.minor <= o2.minor ? (t2.add(e4), true) : a2(e4);
        };
      }(c), p = Symbol.for("opentelemetry.js.api." + c.split(".")[0]);
      function h(e2, t2, r2, n2) {
        void 0 === n2 && (n2 = false);
        var i2, a2 = l[p] = null != (i2 = l[p]) ? i2 : { version: c };
        if (!n2 && a2[e2]) {
          var o2 = Error("@opentelemetry/api: Attempted duplicate registration of API: " + e2);
          return r2.error(o2.stack || o2.message), false;
        }
        if (a2.version !== c) {
          var o2 = Error("@opentelemetry/api: Registration of version v" + a2.version + " for " + e2 + " does not match previously registered API v" + c);
          return r2.error(o2.stack || o2.message), false;
        }
        return a2[e2] = t2, r2.debug("@opentelemetry/api: Registered a global for " + e2 + " v" + c + "."), true;
      }
      function _(e2) {
        var t2, r2, n2 = null == (t2 = l[p]) ? void 0 : t2.version;
        if (n2 && f(n2)) return null == (r2 = l[p]) ? void 0 : r2[e2];
      }
      function y(e2, t2) {
        t2.debug("@opentelemetry/api: Unregistering a global for " + e2 + " v" + c + ".");
        var r2 = l[p];
        r2 && delete r2[e2];
      }
      var g = function(e2, t2) {
        var r2 = "function" == typeof Symbol && e2[Symbol.iterator];
        if (!r2) return e2;
        var n2, i2, a2 = r2.call(e2), o2 = [];
        try {
          for (; (void 0 === t2 || t2-- > 0) && !(n2 = a2.next()).done; ) o2.push(n2.value);
        } catch (e4) {
          i2 = { error: e4 };
        } finally {
          try {
            n2 && !n2.done && (r2 = a2.return) && r2.call(a2);
          } finally {
            if (i2) throw i2.error;
          }
        }
        return o2;
      }, m = function(e2, t2, r2) {
        if (r2 || 2 == arguments.length) for (var n2, i2 = 0, a2 = t2.length; i2 < a2; i2++) !n2 && i2 in t2 || (n2 || (n2 = Array.prototype.slice.call(t2, 0, i2)), n2[i2] = t2[i2]);
        return e2.concat(n2 || Array.prototype.slice.call(t2));
      }, v = function() {
        function e2(e4) {
          this._namespace = e4.namespace || "DiagComponentLogger";
        }
        return e2.prototype.debug = function() {
          for (var e4 = [], t2 = 0; t2 < arguments.length; t2++) e4[t2] = arguments[t2];
          return b("debug", this._namespace, e4);
        }, e2.prototype.error = function() {
          for (var e4 = [], t2 = 0; t2 < arguments.length; t2++) e4[t2] = arguments[t2];
          return b("error", this._namespace, e4);
        }, e2.prototype.info = function() {
          for (var e4 = [], t2 = 0; t2 < arguments.length; t2++) e4[t2] = arguments[t2];
          return b("info", this._namespace, e4);
        }, e2.prototype.warn = function() {
          for (var e4 = [], t2 = 0; t2 < arguments.length; t2++) e4[t2] = arguments[t2];
          return b("warn", this._namespace, e4);
        }, e2.prototype.verbose = function() {
          for (var e4 = [], t2 = 0; t2 < arguments.length; t2++) e4[t2] = arguments[t2];
          return b("verbose", this._namespace, e4);
        }, e2;
      }();
      function b(e2, t2, r2) {
        var n2 = _("diag");
        if (n2) return r2.unshift(t2), n2[e2].apply(n2, m([], g(r2), false));
      }
      !function(e2) {
        e2[e2.NONE = 0] = "NONE", e2[e2.ERROR = 30] = "ERROR", e2[e2.WARN = 50] = "WARN", e2[e2.INFO = 60] = "INFO", e2[e2.DEBUG = 70] = "DEBUG", e2[e2.VERBOSE = 80] = "VERBOSE", e2[e2.ALL = 9999] = "ALL";
      }(n || (n = {}));
      var w = function(e2, t2) {
        var r2 = "function" == typeof Symbol && e2[Symbol.iterator];
        if (!r2) return e2;
        var n2, i2, a2 = r2.call(e2), o2 = [];
        try {
          for (; (void 0 === t2 || t2-- > 0) && !(n2 = a2.next()).done; ) o2.push(n2.value);
        } catch (e4) {
          i2 = { error: e4 };
        } finally {
          try {
            n2 && !n2.done && (r2 = a2.return) && r2.call(a2);
          } finally {
            if (i2) throw i2.error;
          }
        }
        return o2;
      }, S = function(e2, t2, r2) {
        if (r2 || 2 == arguments.length) for (var n2, i2 = 0, a2 = t2.length; i2 < a2; i2++) !n2 && i2 in t2 || (n2 || (n2 = Array.prototype.slice.call(t2, 0, i2)), n2[i2] = t2[i2]);
        return e2.concat(n2 || Array.prototype.slice.call(t2));
      }, E = function() {
        function e2() {
          function e4(e5) {
            return function() {
              for (var t3 = [], r2 = 0; r2 < arguments.length; r2++) t3[r2] = arguments[r2];
              var n2 = _("diag");
              if (n2) return n2[e5].apply(n2, S([], w(t3), false));
            };
          }
          var t2 = this;
          t2.setLogger = function(e5, r2) {
            if (void 0 === r2 && (r2 = { logLevel: n.INFO }), e5 === t2) {
              var i2, a2, o2, s2 = Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");
              return t2.error(null != (i2 = s2.stack) ? i2 : s2.message), false;
            }
            "number" == typeof r2 && (r2 = { logLevel: r2 });
            var u2 = _("diag"), l2 = function(e6, t3) {
              function r3(r4, n2) {
                var i3 = t3[r4];
                return "function" == typeof i3 && e6 >= n2 ? i3.bind(t3) : function() {
                };
              }
              return e6 < n.NONE ? e6 = n.NONE : e6 > n.ALL && (e6 = n.ALL), t3 = t3 || {}, { error: r3("error", n.ERROR), warn: r3("warn", n.WARN), info: r3("info", n.INFO), debug: r3("debug", n.DEBUG), verbose: r3("verbose", n.VERBOSE) };
            }(null != (a2 = r2.logLevel) ? a2 : n.INFO, e5);
            if (u2 && !r2.suppressOverrideMessage) {
              var c2 = null != (o2 = Error().stack) ? o2 : "<failed to generate stacktrace>";
              u2.warn("Current logger will be overwritten from " + c2), l2.warn("Current logger will overwrite one already registered from " + c2);
            }
            return h("diag", l2, t2, true);
          }, t2.disable = function() {
            y("diag", t2);
          }, t2.createComponentLogger = function(e5) {
            return new v(e5);
          }, t2.verbose = e4("verbose"), t2.debug = e4("debug"), t2.info = e4("info"), t2.warn = e4("warn"), t2.error = e4("error");
        }
        return e2.instance = function() {
          return this._instance || (this._instance = new e2()), this._instance;
        }, e2;
      }(), C = function(e2, t2) {
        var r2 = "function" == typeof Symbol && e2[Symbol.iterator];
        if (!r2) return e2;
        var n2, i2, a2 = r2.call(e2), o2 = [];
        try {
          for (; (void 0 === t2 || t2-- > 0) && !(n2 = a2.next()).done; ) o2.push(n2.value);
        } catch (e4) {
          i2 = { error: e4 };
        } finally {
          try {
            n2 && !n2.done && (r2 = a2.return) && r2.call(a2);
          } finally {
            if (i2) throw i2.error;
          }
        }
        return o2;
      }, R = function(e2) {
        var t2 = "function" == typeof Symbol && Symbol.iterator, r2 = t2 && e2[t2], n2 = 0;
        if (r2) return r2.call(e2);
        if (e2 && "number" == typeof e2.length) return { next: function() {
          return e2 && n2 >= e2.length && (e2 = void 0), { value: e2 && e2[n2++], done: !e2 };
        } };
        throw TypeError(t2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
      }, x = function() {
        function e2(e4) {
          this._entries = e4 ? new Map(e4) : /* @__PURE__ */ new Map();
        }
        return e2.prototype.getEntry = function(e4) {
          var t2 = this._entries.get(e4);
          if (t2) return Object.assign({}, t2);
        }, e2.prototype.getAllEntries = function() {
          return Array.from(this._entries.entries()).map(function(e4) {
            var t2 = C(e4, 2);
            return [t2[0], t2[1]];
          });
        }, e2.prototype.setEntry = function(t2, r2) {
          var n2 = new e2(this._entries);
          return n2._entries.set(t2, r2), n2;
        }, e2.prototype.removeEntry = function(t2) {
          var r2 = new e2(this._entries);
          return r2._entries.delete(t2), r2;
        }, e2.prototype.removeEntries = function() {
          for (var t2, r2, n2 = [], i2 = 0; i2 < arguments.length; i2++) n2[i2] = arguments[i2];
          var a2 = new e2(this._entries);
          try {
            for (var o2 = R(n2), s2 = o2.next(); !s2.done; s2 = o2.next()) {
              var u2 = s2.value;
              a2._entries.delete(u2);
            }
          } catch (e4) {
            t2 = { error: e4 };
          } finally {
            try {
              s2 && !s2.done && (r2 = o2.return) && r2.call(o2);
            } finally {
              if (t2) throw t2.error;
            }
          }
          return a2;
        }, e2.prototype.clear = function() {
          return new e2();
        }, e2;
      }(), T = Symbol("BaggageEntryMetadata"), k = E.instance();
      function O(e2) {
        return void 0 === e2 && (e2 = {}), new x(new Map(Object.entries(e2)));
      }
      function P(e2) {
        return "string" != typeof e2 && (k.error("Cannot create baggage metadata from unknown type: " + typeof e2), e2 = ""), { __TYPE__: T, toString: function() {
          return e2;
        } };
      }
      function A(e2) {
        return Symbol.for(e2);
      }
      var N = new function e2(t2) {
        var r2 = this;
        r2._currentContext = t2 ? new Map(t2) : /* @__PURE__ */ new Map(), r2.getValue = function(e4) {
          return r2._currentContext.get(e4);
        }, r2.setValue = function(t3, n2) {
          var i2 = new e2(r2._currentContext);
          return i2._currentContext.set(t3, n2), i2;
        }, r2.deleteValue = function(t3) {
          var n2 = new e2(r2._currentContext);
          return n2._currentContext.delete(t3), n2;
        };
      }(), L = [{ n: "error", c: "error" }, { n: "warn", c: "warn" }, { n: "info", c: "info" }, { n: "debug", c: "debug" }, { n: "verbose", c: "trace" }], I = function() {
        for (var e2 = 0; e2 < L.length; e2++) this[L[e2].n] = /* @__PURE__ */ function(e4) {
          return function() {
            for (var t2 = [], r2 = 0; r2 < arguments.length; r2++) t2[r2] = arguments[r2];
            if (console) {
              var n2 = console[e4];
              if ("function" != typeof n2 && (n2 = console.log), "function" == typeof n2) return n2.apply(console, t2);
            }
          };
        }(L[e2].c);
      }, M = /* @__PURE__ */ function() {
        var e2 = function(t2, r2) {
          return (e2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(e4, t3) {
            e4.__proto__ = t3;
          } || function(e4, t3) {
            for (var r3 in t3) Object.prototype.hasOwnProperty.call(t3, r3) && (e4[r3] = t3[r3]);
          })(t2, r2);
        };
        return function(t2, r2) {
          if ("function" != typeof r2 && null !== r2) throw TypeError("Class extends value " + String(r2) + " is not a constructor or null");
          function n2() {
            this.constructor = t2;
          }
          e2(t2, r2), t2.prototype = null === r2 ? Object.create(r2) : (n2.prototype = r2.prototype, new n2());
        };
      }(), j = function() {
        function e2() {
        }
        return e2.prototype.createGauge = function(e4, t2) {
          return K;
        }, e2.prototype.createHistogram = function(e4, t2) {
          return X;
        }, e2.prototype.createCounter = function(e4, t2) {
          return V;
        }, e2.prototype.createUpDownCounter = function(e4, t2) {
          return Y;
        }, e2.prototype.createObservableGauge = function(e4, t2) {
          return Z;
        }, e2.prototype.createObservableCounter = function(e4, t2) {
          return J;
        }, e2.prototype.createObservableUpDownCounter = function(e4, t2) {
          return Q;
        }, e2.prototype.addBatchObservableCallback = function(e4, t2) {
        }, e2.prototype.removeBatchObservableCallback = function(e4) {
        }, e2;
      }(), $ = function() {
      }, D = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2.prototype.add = function(e4, t3) {
        }, t2;
      }($), q = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2.prototype.add = function(e4, t3) {
        }, t2;
      }($), U = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2.prototype.record = function(e4, t3) {
        }, t2;
      }($), G = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2.prototype.record = function(e4, t3) {
        }, t2;
      }($), B = function() {
        function e2() {
        }
        return e2.prototype.addCallback = function(e4) {
        }, e2.prototype.removeCallback = function(e4) {
        }, e2;
      }(), H = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2;
      }(B), z = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2;
      }(B), F = function(e2) {
        function t2() {
          return null !== e2 && e2.apply(this, arguments) || this;
        }
        return M(t2, e2), t2;
      }(B), W = new j(), V = new D(), K = new U(), X = new G(), Y = new q(), J = new H(), Z = new z(), Q = new F();
      function ee() {
        return W;
      }
      !function(e2) {
        e2[e2.INT = 0] = "INT", e2[e2.DOUBLE = 1] = "DOUBLE";
      }(i || (i = {}));
      var et = { get: function(e2, t2) {
        if (null != e2) return e2[t2];
      }, keys: function(e2) {
        return null == e2 ? [] : Object.keys(e2);
      } }, er = { set: function(e2, t2, r2) {
        null != e2 && (e2[t2] = r2);
      } }, en = function(e2, t2) {
        var r2 = "function" == typeof Symbol && e2[Symbol.iterator];
        if (!r2) return e2;
        var n2, i2, a2 = r2.call(e2), o2 = [];
        try {
          for (; (void 0 === t2 || t2-- > 0) && !(n2 = a2.next()).done; ) o2.push(n2.value);
        } catch (e4) {
          i2 = { error: e4 };
        } finally {
          try {
            n2 && !n2.done && (r2 = a2.return) && r2.call(a2);
          } finally {
            if (i2) throw i2.error;
          }
        }
        return o2;
      }, ei = function(e2, t2, r2) {
        if (r2 || 2 == arguments.length) for (var n2, i2 = 0, a2 = t2.length; i2 < a2; i2++) !n2 && i2 in t2 || (n2 || (n2 = Array.prototype.slice.call(t2, 0, i2)), n2[i2] = t2[i2]);
        return e2.concat(n2 || Array.prototype.slice.call(t2));
      }, ea = function() {
        function e2() {
        }
        return e2.prototype.active = function() {
          return N;
        }, e2.prototype.with = function(e4, t2, r2) {
          for (var n2 = [], i2 = 3; i2 < arguments.length; i2++) n2[i2 - 3] = arguments[i2];
          return t2.call.apply(t2, ei([r2], en(n2), false));
        }, e2.prototype.bind = function(e4, t2) {
          return t2;
        }, e2.prototype.enable = function() {
          return this;
        }, e2.prototype.disable = function() {
          return this;
        }, e2;
      }(), eo = function(e2, t2) {
        var r2 = "function" == typeof Symbol && e2[Symbol.iterator];
        if (!r2) return e2;
        var n2, i2, a2 = r2.call(e2), o2 = [];
        try {
          for (; (void 0 === t2 || t2-- > 0) && !(n2 = a2.next()).done; ) o2.push(n2.value);
        } catch (e4) {
          i2 = { error: e4 };
        } finally {
          try {
            n2 && !n2.done && (r2 = a2.return) && r2.call(a2);
          } finally {
            if (i2) throw i2.error;
          }
        }
        return o2;
      }, es = function(e2, t2, r2) {
        if (r2 || 2 == arguments.length) for (var n2, i2 = 0, a2 = t2.length; i2 < a2; i2++) !n2 && i2 in t2 || (n2 || (n2 = Array.prototype.slice.call(t2, 0, i2)), n2[i2] = t2[i2]);
        return e2.concat(n2 || Array.prototype.slice.call(t2));
      }, eu = "context", el = new ea(), ec = function() {
        function e2() {
        }
        return e2.getInstance = function() {
          return this._instance || (this._instance = new e2()), this._instance;
        }, e2.prototype.setGlobalContextManager = function(e4) {
          return h(eu, e4, E.instance());
        }, e2.prototype.active = function() {
          return this._getContextManager().active();
        }, e2.prototype.with = function(e4, t2, r2) {
          for (var n2, i2 = [], a2 = 3; a2 < arguments.length; a2++) i2[a2 - 3] = arguments[a2];
          return (n2 = this._getContextManager()).with.apply(n2, es([e4, t2, r2], eo(i2), false));
        }, e2.prototype.bind = function(e4, t2) {
          return this._getContextManager().bind(e4, t2);
        }, e2.prototype._getContextManager = function() {
          return _(eu) || el;
        }, e2.prototype.disable = function() {
          this._getContextManager().disable(), y(eu, E.instance());
        }, e2;
      }();
      !function(e2) {
        e2[e2.NONE = 0] = "NONE", e2[e2.SAMPLED = 1] = "SAMPLED";
      }(a || (a = {}));
      var ed = "0000000000000000", ef = "00000000000000000000000000000000", ep = { traceId: ef, spanId: ed, traceFlags: a.NONE }, eh = function() {
        function e2(e4) {
          void 0 === e4 && (e4 = ep), this._spanContext = e4;
        }
        return e2.prototype.spanContext = function() {
          return this._spanContext;
        }, e2.prototype.setAttribute = function(e4, t2) {
          return this;
        }, e2.prototype.setAttributes = function(e4) {
          return this;
        }, e2.prototype.addEvent = function(e4, t2) {
          return this;
        }, e2.prototype.addLink = function(e4) {
          return this;
        }, e2.prototype.addLinks = function(e4) {
          return this;
        }, e2.prototype.setStatus = function(e4) {
          return this;
        }, e2.prototype.updateName = function(e4) {
          return this;
        }, e2.prototype.end = function(e4) {
        }, e2.prototype.isRecording = function() {
          return false;
        }, e2.prototype.recordException = function(e4, t2) {
        }, e2;
      }(), e_ = A("OpenTelemetry Context Key SPAN");
      function ey(e2) {
        return e2.getValue(e_) || void 0;
      }
      function eg() {
        return ey(ec.getInstance().active());
      }
      function em(e2, t2) {
        return e2.setValue(e_, t2);
      }
      function ev(e2) {
        return e2.deleteValue(e_);
      }
      function eb(e2, t2) {
        return em(e2, new eh(t2));
      }
      function ew(e2) {
        var t2;
        return null == (t2 = ey(e2)) ? void 0 : t2.spanContext();
      }
      var eS = /^([0-9a-f]{32})$/i, eE = /^[0-9a-f]{16}$/i;
      function eC(e2) {
        return eS.test(e2) && e2 !== ef;
      }
      function eR(e2) {
        return eE.test(e2) && e2 !== ed;
      }
      function ex(e2) {
        return eC(e2.traceId) && eR(e2.spanId);
      }
      function eT(e2) {
        return new eh(e2);
      }
      var ek = ec.getInstance(), eO = function() {
        function e2() {
        }
        return e2.prototype.startSpan = function(e4, t2, r2) {
          if (void 0 === r2 && (r2 = ek.active()), null == t2 ? void 0 : t2.root) return new eh();
          var n2, i2 = r2 && ew(r2);
          return "object" == typeof (n2 = i2) && "string" == typeof n2.spanId && "string" == typeof n2.traceId && "number" == typeof n2.traceFlags && ex(i2) ? new eh(i2) : new eh();
        }, e2.prototype.startActiveSpan = function(e4, t2, r2, n2) {
          if (!(arguments.length < 2)) {
            2 == arguments.length ? o2 = t2 : 3 == arguments.length ? (i2 = t2, o2 = r2) : (i2 = t2, a2 = r2, o2 = n2);
            var i2, a2, o2, s2 = null != a2 ? a2 : ek.active(), u2 = this.startSpan(e4, i2, s2), l2 = em(s2, u2);
            return ek.with(l2, o2, void 0, u2);
          }
        }, e2;
      }(), eP = new eO(), eA = function() {
        function e2(e4, t2, r2, n2) {
          this._provider = e4, this.name = t2, this.version = r2, this.options = n2;
        }
        return e2.prototype.startSpan = function(e4, t2, r2) {
          return this._getTracer().startSpan(e4, t2, r2);
        }, e2.prototype.startActiveSpan = function(e4, t2, r2, n2) {
          var i2 = this._getTracer();
          return Reflect.apply(i2.startActiveSpan, i2, arguments);
        }, e2.prototype._getTracer = function() {
          if (this._delegate) return this._delegate;
          var e4 = this._provider.getDelegateTracer(this.name, this.version, this.options);
          return e4 ? (this._delegate = e4, this._delegate) : eP;
        }, e2;
      }(), eN = new (function() {
        function e2() {
        }
        return e2.prototype.getTracer = function(e4, t2, r2) {
          return new eO();
        }, e2;
      }())(), eL = function() {
        function e2() {
        }
        return e2.prototype.getTracer = function(e4, t2, r2) {
          var n2;
          return null != (n2 = this.getDelegateTracer(e4, t2, r2)) ? n2 : new eA(this, e4, t2, r2);
        }, e2.prototype.getDelegate = function() {
          var e4;
          return null != (e4 = this._delegate) ? e4 : eN;
        }, e2.prototype.setDelegate = function(e4) {
          this._delegate = e4;
        }, e2.prototype.getDelegateTracer = function(e4, t2, r2) {
          var n2;
          return null == (n2 = this._delegate) ? void 0 : n2.getTracer(e4, t2, r2);
        }, e2;
      }();
      !function(e2) {
        e2[e2.NOT_RECORD = 0] = "NOT_RECORD", e2[e2.RECORD = 1] = "RECORD", e2[e2.RECORD_AND_SAMPLED = 2] = "RECORD_AND_SAMPLED";
      }(o || (o = {})), function(e2) {
        e2[e2.INTERNAL = 0] = "INTERNAL", e2[e2.SERVER = 1] = "SERVER", e2[e2.CLIENT = 2] = "CLIENT", e2[e2.PRODUCER = 3] = "PRODUCER", e2[e2.CONSUMER = 4] = "CONSUMER";
      }(s || (s = {})), function(e2) {
        e2[e2.UNSET = 0] = "UNSET", e2[e2.OK = 1] = "OK", e2[e2.ERROR = 2] = "ERROR";
      }(u || (u = {}));
      var eI = "[_0-9a-z-*/]", eM = RegExp("^(?:[a-z]" + eI + "{0,255}|" + ("[a-z0-9]" + eI + "{0,240}@[a-z]") + eI + "{0,13})$"), ej = /^[ -~]{0,255}[!-~]$/, e$ = /,|=/, eD = function() {
        function e2(e4) {
          this._internalState = /* @__PURE__ */ new Map(), e4 && this._parse(e4);
        }
        return e2.prototype.set = function(e4, t2) {
          var r2 = this._clone();
          return r2._internalState.has(e4) && r2._internalState.delete(e4), r2._internalState.set(e4, t2), r2;
        }, e2.prototype.unset = function(e4) {
          var t2 = this._clone();
          return t2._internalState.delete(e4), t2;
        }, e2.prototype.get = function(e4) {
          return this._internalState.get(e4);
        }, e2.prototype.serialize = function() {
          var e4 = this;
          return this._keys().reduce(function(t2, r2) {
            return t2.push(r2 + "=" + e4.get(r2)), t2;
          }, []).join(",");
        }, e2.prototype._parse = function(e4) {
          !(e4.length > 512) && (this._internalState = e4.split(",").reverse().reduce(function(e5, t2) {
            var r2 = t2.trim(), n2 = r2.indexOf("=");
            if (-1 !== n2) {
              var i2 = r2.slice(0, n2), a2 = r2.slice(n2 + 1, t2.length);
              eM.test(i2) && ej.test(a2) && !e$.test(a2) && e5.set(i2, a2);
            }
            return e5;
          }, /* @__PURE__ */ new Map()), this._internalState.size > 32 && (this._internalState = new Map(Array.from(this._internalState.entries()).reverse().slice(0, 32))));
        }, e2.prototype._keys = function() {
          return Array.from(this._internalState.keys()).reverse();
        }, e2.prototype._clone = function() {
          var t2 = new e2();
          return t2._internalState = new Map(this._internalState), t2;
        }, e2;
      }();
      function eq(e2) {
        return new eD(e2);
      }
      var eU = ec.getInstance(), eG = E.instance(), eB = new (function() {
        function e2() {
        }
        return e2.prototype.getMeter = function(e4, t2, r2) {
          return W;
        }, e2;
      }())(), eH = "metrics", ez = function() {
        function e2() {
        }
        return e2.getInstance = function() {
          return this._instance || (this._instance = new e2()), this._instance;
        }, e2.prototype.setGlobalMeterProvider = function(e4) {
          return h(eH, e4, E.instance());
        }, e2.prototype.getMeterProvider = function() {
          return _(eH) || eB;
        }, e2.prototype.getMeter = function(e4, t2, r2) {
          return this.getMeterProvider().getMeter(e4, t2, r2);
        }, e2.prototype.disable = function() {
          y(eH, E.instance());
        }, e2;
      }().getInstance(), eF = function() {
        function e2() {
        }
        return e2.prototype.inject = function(e4, t2) {
        }, e2.prototype.extract = function(e4, t2) {
          return e4;
        }, e2.prototype.fields = function() {
          return [];
        }, e2;
      }(), eW = A("OpenTelemetry Baggage Key");
      function eV(e2) {
        return e2.getValue(eW) || void 0;
      }
      function eK() {
        return eV(ec.getInstance().active());
      }
      function eX(e2, t2) {
        return e2.setValue(eW, t2);
      }
      function eY(e2) {
        return e2.deleteValue(eW);
      }
      var eJ = "propagation", eZ = new eF(), eQ = function() {
        function e2() {
          this.createBaggage = O, this.getBaggage = eV, this.getActiveBaggage = eK, this.setBaggage = eX, this.deleteBaggage = eY;
        }
        return e2.getInstance = function() {
          return this._instance || (this._instance = new e2()), this._instance;
        }, e2.prototype.setGlobalPropagator = function(e4) {
          return h(eJ, e4, E.instance());
        }, e2.prototype.inject = function(e4, t2, r2) {
          return void 0 === r2 && (r2 = er), this._getGlobalPropagator().inject(e4, t2, r2);
        }, e2.prototype.extract = function(e4, t2, r2) {
          return void 0 === r2 && (r2 = et), this._getGlobalPropagator().extract(e4, t2, r2);
        }, e2.prototype.fields = function() {
          return this._getGlobalPropagator().fields();
        }, e2.prototype.disable = function() {
          y(eJ, E.instance());
        }, e2.prototype._getGlobalPropagator = function() {
          return _(eJ) || eZ;
        }, e2;
      }().getInstance(), e0 = "trace", e1 = function() {
        function e2() {
          this._proxyTracerProvider = new eL(), this.wrapSpanContext = eT, this.isSpanContextValid = ex, this.deleteSpan = ev, this.getSpan = ey, this.getActiveSpan = eg, this.getSpanContext = ew, this.setSpan = em, this.setSpanContext = eb;
        }
        return e2.getInstance = function() {
          return this._instance || (this._instance = new e2()), this._instance;
        }, e2.prototype.setGlobalTracerProvider = function(e4) {
          var t2 = h(e0, this._proxyTracerProvider, E.instance());
          return t2 && this._proxyTracerProvider.setDelegate(e4), t2;
        }, e2.prototype.getTracerProvider = function() {
          return _(e0) || this._proxyTracerProvider;
        }, e2.prototype.getTracer = function(e4, t2) {
          return this.getTracerProvider().getTracer(e4, t2);
        }, e2.prototype.disable = function() {
          y(e0, E.instance()), this._proxyTracerProvider = new eL();
        }, e2;
      }().getInstance();
      let e3 = { context: eU, diag: eG, metrics: ez, propagation: eQ, trace: e1 };
    }, 342: (e, t, r) => {
      "use strict";
      r.d(t, { xl: () => o });
      let n = Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"), "__NEXT_ERROR_CODE", { value: "E504", enumerable: false, configurable: true });
      class i {
        disable() {
          throw n;
        }
        getStore() {
        }
        run() {
          throw n;
        }
        exit() {
          throw n;
        }
        enterWith() {
          throw n;
        }
        static bind(e2) {
          return e2;
        }
      }
      let a = "undefined" != typeof globalThis && globalThis.AsyncLocalStorage;
      function o() {
        return a ? new a() : new i();
      }
    }, 348: (e, t, r) => {
      "use strict";
      r.d(t, { f: () => n });
      class n extends Error {
        constructor(...e2) {
          super(...e2), this.code = "NEXT_STATIC_GEN_BAILOUT";
        }
      }
    }, 356: (e) => {
      "use strict";
      e.exports = (init_node_buffer(), __toCommonJS(node_buffer_exports));
    }, 363: (e, t, r) => {
      "use strict";
      Object.defineProperty(t, "__esModule", { value: true }), !function(e2, t2) {
        for (var r2 in t2) Object.defineProperty(e2, r2, { enumerable: true, get: t2[r2] });
      }(t, { interceptTestApis: function() {
        return a;
      }, wrapRequestHandler: function() {
        return o;
      } });
      let n = r(395), i = r(318);
      function a() {
        return (0, i.interceptFetch)(r.g.fetch);
      }
      function o(e2) {
        return (t2, r2) => (0, n.withRequest)(t2, i.reader, () => e2(t2, r2));
      }
    }, 395: (e, t, r) => {
      "use strict";
      Object.defineProperty(t, "__esModule", { value: true }), !function(e2, t2) {
        for (var r2 in t2) Object.defineProperty(e2, r2, { enumerable: true, get: t2[r2] });
      }(t, { getTestReqInfo: function() {
        return o;
      }, withRequest: function() {
        return a;
      } });
      let n = new (r(521)).AsyncLocalStorage();
      function i(e2, t2) {
        let r2 = t2.header(e2, "next-test-proxy-port");
        if (!r2) return;
        let n2 = t2.url(e2);
        return { url: n2, proxyPort: Number(r2), testData: t2.header(e2, "next-test-data") || "" };
      }
      function a(e2, t2, r2) {
        let a2 = i(e2, t2);
        return a2 ? n.run(a2, r2) : r2();
      }
      function o(e2, t2) {
        let r2 = n.getStore();
        return r2 || (e2 && t2 ? i(e2, t2) : void 0);
      }
    }, 408: (e) => {
      (() => {
        "use strict";
        var t = { 993: (e2) => {
          var t2 = Object.prototype.hasOwnProperty, r2 = "~";
          function n2() {
          }
          function i2(e3, t3, r3) {
            this.fn = e3, this.context = t3, this.once = r3 || false;
          }
          function a(e3, t3, n3, a2, o2) {
            if ("function" != typeof n3) throw TypeError("The listener must be a function");
            var s2 = new i2(n3, a2 || e3, o2), u = r2 ? r2 + t3 : t3;
            return e3._events[u] ? e3._events[u].fn ? e3._events[u] = [e3._events[u], s2] : e3._events[u].push(s2) : (e3._events[u] = s2, e3._eventsCount++), e3;
          }
          function o(e3, t3) {
            0 == --e3._eventsCount ? e3._events = new n2() : delete e3._events[t3];
          }
          function s() {
            this._events = new n2(), this._eventsCount = 0;
          }
          Object.create && (n2.prototype = /* @__PURE__ */ Object.create(null), new n2().__proto__ || (r2 = false)), s.prototype.eventNames = function() {
            var e3, n3, i3 = [];
            if (0 === this._eventsCount) return i3;
            for (n3 in e3 = this._events) t2.call(e3, n3) && i3.push(r2 ? n3.slice(1) : n3);
            return Object.getOwnPropertySymbols ? i3.concat(Object.getOwnPropertySymbols(e3)) : i3;
          }, s.prototype.listeners = function(e3) {
            var t3 = r2 ? r2 + e3 : e3, n3 = this._events[t3];
            if (!n3) return [];
            if (n3.fn) return [n3.fn];
            for (var i3 = 0, a2 = n3.length, o2 = Array(a2); i3 < a2; i3++) o2[i3] = n3[i3].fn;
            return o2;
          }, s.prototype.listenerCount = function(e3) {
            var t3 = r2 ? r2 + e3 : e3, n3 = this._events[t3];
            return n3 ? n3.fn ? 1 : n3.length : 0;
          }, s.prototype.emit = function(e3, t3, n3, i3, a2, o2) {
            var s2 = r2 ? r2 + e3 : e3;
            if (!this._events[s2]) return false;
            var u, l, c = this._events[s2], d = arguments.length;
            if (c.fn) {
              switch (c.once && this.removeListener(e3, c.fn, void 0, true), d) {
                case 1:
                  return c.fn.call(c.context), true;
                case 2:
                  return c.fn.call(c.context, t3), true;
                case 3:
                  return c.fn.call(c.context, t3, n3), true;
                case 4:
                  return c.fn.call(c.context, t3, n3, i3), true;
                case 5:
                  return c.fn.call(c.context, t3, n3, i3, a2), true;
                case 6:
                  return c.fn.call(c.context, t3, n3, i3, a2, o2), true;
              }
              for (l = 1, u = Array(d - 1); l < d; l++) u[l - 1] = arguments[l];
              c.fn.apply(c.context, u);
            } else {
              var f, p = c.length;
              for (l = 0; l < p; l++) switch (c[l].once && this.removeListener(e3, c[l].fn, void 0, true), d) {
                case 1:
                  c[l].fn.call(c[l].context);
                  break;
                case 2:
                  c[l].fn.call(c[l].context, t3);
                  break;
                case 3:
                  c[l].fn.call(c[l].context, t3, n3);
                  break;
                case 4:
                  c[l].fn.call(c[l].context, t3, n3, i3);
                  break;
                default:
                  if (!u) for (f = 1, u = Array(d - 1); f < d; f++) u[f - 1] = arguments[f];
                  c[l].fn.apply(c[l].context, u);
              }
            }
            return true;
          }, s.prototype.on = function(e3, t3, r3) {
            return a(this, e3, t3, r3, false);
          }, s.prototype.once = function(e3, t3, r3) {
            return a(this, e3, t3, r3, true);
          }, s.prototype.removeListener = function(e3, t3, n3, i3) {
            var a2 = r2 ? r2 + e3 : e3;
            if (!this._events[a2]) return this;
            if (!t3) return o(this, a2), this;
            var s2 = this._events[a2];
            if (s2.fn) s2.fn !== t3 || i3 && !s2.once || n3 && s2.context !== n3 || o(this, a2);
            else {
              for (var u = 0, l = [], c = s2.length; u < c; u++) (s2[u].fn !== t3 || i3 && !s2[u].once || n3 && s2[u].context !== n3) && l.push(s2[u]);
              l.length ? this._events[a2] = 1 === l.length ? l[0] : l : o(this, a2);
            }
            return this;
          }, s.prototype.removeAllListeners = function(e3) {
            var t3;
            return e3 ? (t3 = r2 ? r2 + e3 : e3, this._events[t3] && o(this, t3)) : (this._events = new n2(), this._eventsCount = 0), this;
          }, s.prototype.off = s.prototype.removeListener, s.prototype.addListener = s.prototype.on, s.prefixed = r2, s.EventEmitter = s, e2.exports = s;
        }, 213: (e2) => {
          e2.exports = (e3, t2) => (t2 = t2 || (() => {
          }), e3.then((e4) => new Promise((e5) => {
            e5(t2());
          }).then(() => e4), (e4) => new Promise((e5) => {
            e5(t2());
          }).then(() => {
            throw e4;
          })));
        }, 574: (e2, t2) => {
          Object.defineProperty(t2, "__esModule", { value: true }), t2.default = function(e3, t3, r2) {
            let n2 = 0, i2 = e3.length;
            for (; i2 > 0; ) {
              let a = i2 / 2 | 0, o = n2 + a;
              0 >= r2(e3[o], t3) ? (n2 = ++o, i2 -= a + 1) : i2 = a;
            }
            return n2;
          };
        }, 821: (e2, t2, r2) => {
          Object.defineProperty(t2, "__esModule", { value: true });
          let n2 = r2(574);
          class i2 {
            constructor() {
              this._queue = [];
            }
            enqueue(e3, t3) {
              let r3 = { priority: (t3 = Object.assign({ priority: 0 }, t3)).priority, run: e3 };
              if (this.size && this._queue[this.size - 1].priority >= t3.priority) return void this._queue.push(r3);
              let i3 = n2.default(this._queue, r3, (e4, t4) => t4.priority - e4.priority);
              this._queue.splice(i3, 0, r3);
            }
            dequeue() {
              let e3 = this._queue.shift();
              return null == e3 ? void 0 : e3.run;
            }
            filter(e3) {
              return this._queue.filter((t3) => t3.priority === e3.priority).map((e4) => e4.run);
            }
            get size() {
              return this._queue.length;
            }
          }
          t2.default = i2;
        }, 816: (e2, t2, r2) => {
          let n2 = r2(213);
          class i2 extends Error {
            constructor(e3) {
              super(e3), this.name = "TimeoutError";
            }
          }
          let a = (e3, t3, r3) => new Promise((a2, o) => {
            if ("number" != typeof t3 || t3 < 0) throw TypeError("Expected `milliseconds` to be a positive number");
            if (t3 === 1 / 0) return void a2(e3);
            let s = setTimeout(() => {
              if ("function" == typeof r3) {
                try {
                  a2(r3());
                } catch (e4) {
                  o(e4);
                }
                return;
              }
              let n3 = "string" == typeof r3 ? r3 : `Promise timed out after ${t3} milliseconds`, s2 = r3 instanceof Error ? r3 : new i2(n3);
              "function" == typeof e3.cancel && e3.cancel(), o(s2);
            }, t3);
            n2(e3.then(a2, o), () => {
              clearTimeout(s);
            });
          });
          e2.exports = a, e2.exports.default = a, e2.exports.TimeoutError = i2;
        } }, r = {};
        function n(e2) {
          var i2 = r[e2];
          if (void 0 !== i2) return i2.exports;
          var a = r[e2] = { exports: {} }, o = true;
          try {
            t[e2](a, a.exports, n), o = false;
          } finally {
            o && delete r[e2];
          }
          return a.exports;
        }
        n.ab = "//";
        var i = {};
        (() => {
          Object.defineProperty(i, "__esModule", { value: true });
          let e2 = n(993), t2 = n(816), r2 = n(821), a = () => {
          }, o = new t2.TimeoutError();
          class s extends e2 {
            constructor(e3) {
              var t3, n2, i2, o2;
              if (super(), this._intervalCount = 0, this._intervalEnd = 0, this._pendingCount = 0, this._resolveEmpty = a, this._resolveIdle = a, !("number" == typeof (e3 = Object.assign({ carryoverConcurrencyCount: false, intervalCap: 1 / 0, interval: 0, concurrency: 1 / 0, autoStart: true, queueClass: r2.default }, e3)).intervalCap && e3.intervalCap >= 1)) throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null != (n2 = null == (t3 = e3.intervalCap) ? void 0 : t3.toString()) ? n2 : ""}\` (${typeof e3.intervalCap})`);
              if (void 0 === e3.interval || !(Number.isFinite(e3.interval) && e3.interval >= 0)) throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null != (o2 = null == (i2 = e3.interval) ? void 0 : i2.toString()) ? o2 : ""}\` (${typeof e3.interval})`);
              this._carryoverConcurrencyCount = e3.carryoverConcurrencyCount, this._isIntervalIgnored = e3.intervalCap === 1 / 0 || 0 === e3.interval, this._intervalCap = e3.intervalCap, this._interval = e3.interval, this._queue = new e3.queueClass(), this._queueClass = e3.queueClass, this.concurrency = e3.concurrency, this._timeout = e3.timeout, this._throwOnTimeout = true === e3.throwOnTimeout, this._isPaused = false === e3.autoStart;
            }
            get _doesIntervalAllowAnother() {
              return this._isIntervalIgnored || this._intervalCount < this._intervalCap;
            }
            get _doesConcurrentAllowAnother() {
              return this._pendingCount < this._concurrency;
            }
            _next() {
              this._pendingCount--, this._tryToStartAnother(), this.emit("next");
            }
            _resolvePromises() {
              this._resolveEmpty(), this._resolveEmpty = a, 0 === this._pendingCount && (this._resolveIdle(), this._resolveIdle = a, this.emit("idle"));
            }
            _onResumeInterval() {
              this._onInterval(), this._initializeIntervalIfNeeded(), this._timeoutId = void 0;
            }
            _isIntervalPaused() {
              let e3 = Date.now();
              if (void 0 === this._intervalId) {
                let t3 = this._intervalEnd - e3;
                if (!(t3 < 0)) return void 0 === this._timeoutId && (this._timeoutId = setTimeout(() => {
                  this._onResumeInterval();
                }, t3)), true;
                this._intervalCount = this._carryoverConcurrencyCount ? this._pendingCount : 0;
              }
              return false;
            }
            _tryToStartAnother() {
              if (0 === this._queue.size) return this._intervalId && clearInterval(this._intervalId), this._intervalId = void 0, this._resolvePromises(), false;
              if (!this._isPaused) {
                let e3 = !this._isIntervalPaused();
                if (this._doesIntervalAllowAnother && this._doesConcurrentAllowAnother) {
                  let t3 = this._queue.dequeue();
                  return !!t3 && (this.emit("active"), t3(), e3 && this._initializeIntervalIfNeeded(), true);
                }
              }
              return false;
            }
            _initializeIntervalIfNeeded() {
              this._isIntervalIgnored || void 0 !== this._intervalId || (this._intervalId = setInterval(() => {
                this._onInterval();
              }, this._interval), this._intervalEnd = Date.now() + this._interval);
            }
            _onInterval() {
              0 === this._intervalCount && 0 === this._pendingCount && this._intervalId && (clearInterval(this._intervalId), this._intervalId = void 0), this._intervalCount = this._carryoverConcurrencyCount ? this._pendingCount : 0, this._processQueue();
            }
            _processQueue() {
              for (; this._tryToStartAnother(); ) ;
            }
            get concurrency() {
              return this._concurrency;
            }
            set concurrency(e3) {
              if (!("number" == typeof e3 && e3 >= 1)) throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e3}\` (${typeof e3})`);
              this._concurrency = e3, this._processQueue();
            }
            async add(e3, r3 = {}) {
              return new Promise((n2, i2) => {
                let a2 = async () => {
                  this._pendingCount++, this._intervalCount++;
                  try {
                    let a3 = void 0 === this._timeout && void 0 === r3.timeout ? e3() : t2.default(Promise.resolve(e3()), void 0 === r3.timeout ? this._timeout : r3.timeout, () => {
                      (void 0 === r3.throwOnTimeout ? this._throwOnTimeout : r3.throwOnTimeout) && i2(o);
                    });
                    n2(await a3);
                  } catch (e4) {
                    i2(e4);
                  }
                  this._next();
                };
                this._queue.enqueue(a2, r3), this._tryToStartAnother(), this.emit("add");
              });
            }
            async addAll(e3, t3) {
              return Promise.all(e3.map(async (e4) => this.add(e4, t3)));
            }
            start() {
              return this._isPaused && (this._isPaused = false, this._processQueue()), this;
            }
            pause() {
              this._isPaused = true;
            }
            clear() {
              this._queue = new this._queueClass();
            }
            async onEmpty() {
              if (0 !== this._queue.size) return new Promise((e3) => {
                let t3 = this._resolveEmpty;
                this._resolveEmpty = () => {
                  t3(), e3();
                };
              });
            }
            async onIdle() {
              if (0 !== this._pendingCount || 0 !== this._queue.size) return new Promise((e3) => {
                let t3 = this._resolveIdle;
                this._resolveIdle = () => {
                  t3(), e3();
                };
              });
            }
            get size() {
              return this._queue.size;
            }
            sizeBy(e3) {
              return this._queue.filter(e3).length;
            }
            get pending() {
              return this._pendingCount;
            }
            get isPaused() {
              return this._isPaused;
            }
            get timeout() {
              return this._timeout;
            }
            set timeout(e3) {
              this._timeout = e3;
            }
          }
          i.default = s;
        })(), e.exports = i;
      })();
    }, 441: (e, t, r) => {
      "use strict";
      r.d(t, { RM: () => a, s8: () => i });
      let n = new Set(Object.values({ NOT_FOUND: 404, FORBIDDEN: 403, UNAUTHORIZED: 401 })), i = "NEXT_HTTP_ERROR_FALLBACK";
      function a(e2) {
        if ("object" != typeof e2 || null === e2 || !("digest" in e2) || "string" != typeof e2.digest) return false;
        let [t2, r2] = e2.digest.split(";");
        return t2 === i && n.has(Number(r2));
      }
    }, 446: (e) => {
      "use strict";
      var t = Object.defineProperty, r = Object.getOwnPropertyDescriptor, n = Object.getOwnPropertyNames, i = Object.prototype.hasOwnProperty, a = {};
      function o(e2) {
        var t2;
        let r2 = ["path" in e2 && e2.path && `Path=${e2.path}`, "expires" in e2 && (e2.expires || 0 === e2.expires) && `Expires=${("number" == typeof e2.expires ? new Date(e2.expires) : e2.expires).toUTCString()}`, "maxAge" in e2 && "number" == typeof e2.maxAge && `Max-Age=${e2.maxAge}`, "domain" in e2 && e2.domain && `Domain=${e2.domain}`, "secure" in e2 && e2.secure && "Secure", "httpOnly" in e2 && e2.httpOnly && "HttpOnly", "sameSite" in e2 && e2.sameSite && `SameSite=${e2.sameSite}`, "partitioned" in e2 && e2.partitioned && "Partitioned", "priority" in e2 && e2.priority && `Priority=${e2.priority}`].filter(Boolean), n2 = `${e2.name}=${encodeURIComponent(null != (t2 = e2.value) ? t2 : "")}`;
        return 0 === r2.length ? n2 : `${n2}; ${r2.join("; ")}`;
      }
      function s(e2) {
        let t2 = /* @__PURE__ */ new Map();
        for (let r2 of e2.split(/; */)) {
          if (!r2) continue;
          let e3 = r2.indexOf("=");
          if (-1 === e3) {
            t2.set(r2, "true");
            continue;
          }
          let [n2, i2] = [r2.slice(0, e3), r2.slice(e3 + 1)];
          try {
            t2.set(n2, decodeURIComponent(null != i2 ? i2 : "true"));
          } catch {
          }
        }
        return t2;
      }
      function u(e2) {
        if (!e2) return;
        let [[t2, r2], ...n2] = s(e2), { domain: i2, expires: a2, httponly: o2, maxage: u2, path: d2, samesite: f2, secure: p, partitioned: h, priority: _ } = Object.fromEntries(n2.map(([e3, t3]) => [e3.toLowerCase().replace(/-/g, ""), t3]));
        {
          var y, g, m = { name: t2, value: decodeURIComponent(r2), domain: i2, ...a2 && { expires: new Date(a2) }, ...o2 && { httpOnly: true }, ..."string" == typeof u2 && { maxAge: Number(u2) }, path: d2, ...f2 && { sameSite: l.includes(y = (y = f2).toLowerCase()) ? y : void 0 }, ...p && { secure: true }, ..._ && { priority: c.includes(g = (g = _).toLowerCase()) ? g : void 0 }, ...h && { partitioned: true } };
          let e3 = {};
          for (let t3 in m) m[t3] && (e3[t3] = m[t3]);
          return e3;
        }
      }
      ((e2, r2) => {
        for (var n2 in r2) t(e2, n2, { get: r2[n2], enumerable: true });
      })(a, { RequestCookies: () => d, ResponseCookies: () => f, parseCookie: () => s, parseSetCookie: () => u, stringifyCookie: () => o }), e.exports = ((e2, a2, o2, s2) => {
        if (a2 && "object" == typeof a2 || "function" == typeof a2) for (let u2 of n(a2)) i.call(e2, u2) || u2 === o2 || t(e2, u2, { get: () => a2[u2], enumerable: !(s2 = r(a2, u2)) || s2.enumerable });
        return e2;
      })(t({}, "__esModule", { value: true }), a);
      var l = ["strict", "lax", "none"], c = ["low", "medium", "high"], d = class {
        constructor(e2) {
          this._parsed = /* @__PURE__ */ new Map(), this._headers = e2;
          let t2 = e2.get("cookie");
          if (t2) for (let [e3, r2] of s(t2)) this._parsed.set(e3, { name: e3, value: r2 });
        }
        [Symbol.iterator]() {
          return this._parsed[Symbol.iterator]();
        }
        get size() {
          return this._parsed.size;
        }
        get(...e2) {
          let t2 = "string" == typeof e2[0] ? e2[0] : e2[0].name;
          return this._parsed.get(t2);
        }
        getAll(...e2) {
          var t2;
          let r2 = Array.from(this._parsed);
          if (!e2.length) return r2.map(([e3, t3]) => t3);
          let n2 = "string" == typeof e2[0] ? e2[0] : null == (t2 = e2[0]) ? void 0 : t2.name;
          return r2.filter(([e3]) => e3 === n2).map(([e3, t3]) => t3);
        }
        has(e2) {
          return this._parsed.has(e2);
        }
        set(...e2) {
          let [t2, r2] = 1 === e2.length ? [e2[0].name, e2[0].value] : e2, n2 = this._parsed;
          return n2.set(t2, { name: t2, value: r2 }), this._headers.set("cookie", Array.from(n2).map(([e3, t3]) => o(t3)).join("; ")), this;
        }
        delete(e2) {
          let t2 = this._parsed, r2 = Array.isArray(e2) ? e2.map((e3) => t2.delete(e3)) : t2.delete(e2);
          return this._headers.set("cookie", Array.from(t2).map(([e3, t3]) => o(t3)).join("; ")), r2;
        }
        clear() {
          return this.delete(Array.from(this._parsed.keys())), this;
        }
        [Symbol.for("edge-runtime.inspect.custom")]() {
          return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;
        }
        toString() {
          return [...this._parsed.values()].map((e2) => `${e2.name}=${encodeURIComponent(e2.value)}`).join("; ");
        }
      }, f = class {
        constructor(e2) {
          var t2, r2, n2;
          this._parsed = /* @__PURE__ */ new Map(), this._headers = e2;
          let i2 = null != (n2 = null != (r2 = null == (t2 = e2.getSetCookie) ? void 0 : t2.call(e2)) ? r2 : e2.get("set-cookie")) ? n2 : [];
          for (let e3 of Array.isArray(i2) ? i2 : function(e4) {
            if (!e4) return [];
            var t3, r3, n3, i3, a2, o2 = [], s2 = 0;
            function u2() {
              for (; s2 < e4.length && /\s/.test(e4.charAt(s2)); ) s2 += 1;
              return s2 < e4.length;
            }
            for (; s2 < e4.length; ) {
              for (t3 = s2, a2 = false; u2(); ) if ("," === (r3 = e4.charAt(s2))) {
                for (n3 = s2, s2 += 1, u2(), i3 = s2; s2 < e4.length && "=" !== (r3 = e4.charAt(s2)) && ";" !== r3 && "," !== r3; ) s2 += 1;
                s2 < e4.length && "=" === e4.charAt(s2) ? (a2 = true, s2 = i3, o2.push(e4.substring(t3, n3)), t3 = s2) : s2 = n3 + 1;
              } else s2 += 1;
              (!a2 || s2 >= e4.length) && o2.push(e4.substring(t3, e4.length));
            }
            return o2;
          }(i2)) {
            let t3 = u(e3);
            t3 && this._parsed.set(t3.name, t3);
          }
        }
        get(...e2) {
          let t2 = "string" == typeof e2[0] ? e2[0] : e2[0].name;
          return this._parsed.get(t2);
        }
        getAll(...e2) {
          var t2;
          let r2 = Array.from(this._parsed.values());
          if (!e2.length) return r2;
          let n2 = "string" == typeof e2[0] ? e2[0] : null == (t2 = e2[0]) ? void 0 : t2.name;
          return r2.filter((e3) => e3.name === n2);
        }
        has(e2) {
          return this._parsed.has(e2);
        }
        set(...e2) {
          let [t2, r2, n2] = 1 === e2.length ? [e2[0].name, e2[0].value, e2[0]] : e2, i2 = this._parsed;
          return i2.set(t2, function(e3 = { name: "", value: "" }) {
            return "number" == typeof e3.expires && (e3.expires = new Date(e3.expires)), e3.maxAge && (e3.expires = new Date(Date.now() + 1e3 * e3.maxAge)), (null === e3.path || void 0 === e3.path) && (e3.path = "/"), e3;
          }({ name: t2, value: r2, ...n2 })), function(e3, t3) {
            for (let [, r3] of (t3.delete("set-cookie"), e3)) {
              let e4 = o(r3);
              t3.append("set-cookie", e4);
            }
          }(i2, this._headers), this;
        }
        delete(...e2) {
          let [t2, r2] = "string" == typeof e2[0] ? [e2[0]] : [e2[0].name, e2[0]];
          return this.set({ ...r2, name: t2, value: "", expires: /* @__PURE__ */ new Date(0) });
        }
        [Symbol.for("edge-runtime.inspect.custom")]() {
          return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;
        }
        toString() {
          return [...this._parsed.values()].map(o).join("; ");
        }
      };
    }, 450: (e, t, r) => {
      "use strict";
      var n;
      (n = r(661)).renderToReadableStream, n.decodeReply, n.decodeReplyFromAsyncIterable, n.decodeAction, n.decodeFormState, n.registerServerReference, t.YR = n.registerClientReference, n.createClientModuleProxy, n.createTemporaryReferenceSet;
    }, 457: (e, t, r) => {
      "use strict";
      e.exports = r(753);
    }, 496: (e, t, r) => {
      "use strict";
      r.d(t, { s: () => n });
      let n = (0, r(342).xl)();
    }, 499: (e, t, r) => {
      "use strict";
      r.d(t, { Q: () => n });
      var n = function(e2) {
        return e2[e2.SeeOther = 303] = "SeeOther", e2[e2.TemporaryRedirect = 307] = "TemporaryRedirect", e2[e2.PermanentRedirect = 308] = "PermanentRedirect", e2;
      }({});
    }, 521: (e) => {
      "use strict";
      e.exports = (init_node_async_hooks(), __toCommonJS(node_async_hooks_exports));
    }, 580: (e) => {
      "use strict";
      e.exports = JSON.parse('{"workspace":{"title":"AI\u6A21\u578B\u5DE5\u4F5C\u53F0","subtitle":"\u5F3A\u5927\u7684AI\u751F\u6210\u5DE5\u5177\uFF0C\u652F\u6301\u6587\u672C\u3001\u56FE\u50CF\u548C\u89C6\u9891\u521B\u4F5C","fullscreen":"\u5168\u5C4F\u6A21\u5F0F","start_create":"\u5F00\u59CB\u521B\u4F5C","choose_model":"\u9009\u62E9\u6A21\u578B\u5E76\u5F00\u59CB\u60A8\u7684\u521B\u4F5C"},"generator":{"start":"\u5F00\u59CB\u751F\u6210","generating":"\u751F\u6210\u4E2D...","model_selector":"\u9009\u62E9\u6A21\u578B","prompt_input":"\u8F93\u5165\u63D0\u793A\u8BCD","prompt_placeholder":"\u63CF\u8FF0\u60A8\u60F3\u8981\u521B\u5EFA\u7684\u5185\u5BB9...","options_config":"\u914D\u7F6E\u9009\u9879"},"cost":{"estimated":"\u9884\u4F30\u6210\u672C","credits":"\u79EF\u5206","consumed":"\u672C\u6B21\u751F\u6210\u6D88\u8017 {amount} \u79EF\u5206","not_enough":"\u79EF\u5206\u4E0D\u8DB3\uFF0C\u8FD8\u9700\u8981 {shortfall} \u79EF\u5206","can_afford":"\u60A8\u7684\u79EF\u5206\u5145\u8DB3"},"status":{"success":"\u751F\u6210\u5B8C\u6210","failed":"\u751F\u6210\u5931\u8D25","pending":"\u5904\u7406\u4E2D...","running":"\u751F\u6210\u4E2D...","progress":"\u8FDB\u5EA6\uFF1A{percent}%"},"actions":{"view":"\u67E5\u770B","download":"\u4E0B\u8F7D","retry":"\u91CD\u8BD5","cancel":"\u53D6\u6D88","close":"\u5173\u95ED"},"models":{"loading":"\u52A0\u8F7D\u4E2D...","error":"\u52A0\u8F7D\u5931\u8D25","no_models":"\u6682\u65E0\u53EF\u7528\u6A21\u578B","select_model":"\u9009\u62E9\u6A21\u578B","model_selector":"AI\u6A21\u578B","model_info":"\u6A21\u578B\u4FE1\u606F"},"results":{"text_result":"\u751F\u6210\u7684\u6587\u672C","image_result":"\u751F\u6210\u7684\u56FE\u7247 {index}","video_result":"\u751F\u6210\u7684\u89C6\u9891","no_result":"\u6682\u65E0\u7ED3\u679C","result_ready":"\u751F\u6210\u7ED3\u679C","text_description":"\u6587\u672C\u751F\u6210\u7684\u7ED3\u679C\u5C06\u5728\u6B64\u5904\u663E\u793A\uFF0C\u652F\u6301\u590D\u5236\u548C\u5BFC\u51FA","image_description":"\u56FE\u50CF\u751F\u6210\u7684\u7ED3\u679C\u5C06\u5728\u6B64\u5904\u663E\u793A\uFF0C\u652F\u6301\u9884\u89C8\u548C\u4E0B\u8F7D","video_description":"\u89C6\u9891\u751F\u6210\u7684\u7ED3\u679C\u5C06\u5728\u6B64\u5904\u663E\u793A\uFF0C\u652F\u6301\u64AD\u653E\u548C\u4E0B\u8F7D"},"errors":{"generation_failed":"\u751F\u6210\u5931\u8D25\uFF1A{detail}","network_error":"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5","invalid_input":"\u8F93\u5165\u65E0\u6548\uFF0C\u8BF7\u68C0\u67E5\u60A8\u7684\u63D0\u793A\u8BCD","model_unavailable":"\u6240\u9009\u6A21\u578B\u5F53\u524D\u4E0D\u53EF\u7528","insufficient_credits":"\u79EF\u5206\u4E0D\u8DB3\uFF0C\u8BF7\u5145\u503C\u540E\u518D\u8BD5"},"tabs":{"text":"\u6587\u672C\u6A21\u578B","image":"\u56FE\u50CF\u751F\u6210","video":"\u89C6\u9891\u751F\u6210","audio":"\u97F3\u9891\u751F\u6210"},"toolbar":{"minimize":"\u6700\u5C0F\u5316","maximize":"\u6700\u5927\u5316","exit_fullscreen":"\u9000\u51FA\u5168\u5C4F","settings":"\u8BBE\u7F6E"},"credits":{"current_balance":"\u5F53\u524D\u4F59\u989D","insufficient":"\u79EF\u5206\u4E0D\u8DB3","recharge":"\u5145\u503C","usage_info":"\u4F7F\u7528\u4FE1\u606F"},"options":{"image_upload":"\u4E0A\u4F20\u56FE\u7247","reference_image":"\u53C2\u8003\u56FE\u7247","first_frame":"\u9996\u5E27\u56FE\u7247","uploading":"\u6B63\u5728\u4E0A\u4F20\u56FE\u7247...","drag_drop":"\u70B9\u51FB\u9009\u62E9\u6216\u62D6\u62FD\u56FE\u7247\u5230\u6B64\u5904","drop_to_upload":"\u677E\u5F00\u9F20\u6807\u5B8C\u6210\u4E0A\u4F20","file_detected":"\u68C0\u6D4B\u5230\u56FE\u7247\u6587\u4EF6\uFF0C\u677E\u5F00\u5373\u53EF\u4E0A\u4F20","supported_formats":"\u652F\u6301 JPG\u3001PNG\u3001GIF\u3001WebP \u7B49\u683C\u5F0F\uFF0C\u6700\u5927 10MB","max_tokens":"\u6700\u5927\u8F93\u51FA\u957F\u5EA6","temperature":"\u521B\u9020\u6027 (0-1)","variants":"\u751F\u6210\u6570\u91CF","image_size":"\u56FE\u50CF\u5C3A\u5BF8","square":"\u6B63\u65B9\u5F62","landscape":"\u6A2A\u5C4F","portrait":"\u7AD6\u5C4F","1_image":"1\u5F20","2_images":"2\u5F20","generate_1":"\u751F\u62101\u5F20\u56FE\u7247","generate_2":"\u751F\u62102\u5F20\u56FE\u7247","square_ratio":"\u6B63\u65B9\u5F62 (1:1)","landscape_ratio":"\u6A2A\u5C4F (16:9)","portrait_ratio":"\u7AD6\u5C4F (9:16)"}}');
    }, 654: (e, t, r) => {
      "use strict";
      r.d(t, { F: () => i, h: () => a });
      let n = "DYNAMIC_SERVER_USAGE";
      class i extends Error {
        constructor(e2) {
          super("Dynamic server usage: " + e2), this.description = e2, this.digest = n;
        }
      }
      function a(e2) {
        return "object" == typeof e2 && null !== e2 && "digest" in e2 && "string" == typeof e2.digest && e2.digest === n;
      }
    }, 661: (e, t, r) => {
      "use strict";
      var n = r(245), i = r(457), a = Symbol.for("react.element"), o = Symbol.for("react.transitional.element"), s = Symbol.for("react.fragment"), u = Symbol.for("react.context"), l = Symbol.for("react.forward_ref"), c = Symbol.for("react.suspense"), d = Symbol.for("react.suspense_list"), f = Symbol.for("react.memo"), p = Symbol.for("react.lazy"), h = Symbol.for("react.memo_cache_sentinel");
      Symbol.for("react.postpone");
      var _ = Symbol.iterator;
      function y(e10) {
        return null === e10 || "object" != typeof e10 ? null : "function" == typeof (e10 = _ && e10[_] || e10["@@iterator"]) ? e10 : null;
      }
      var g = Symbol.asyncIterator;
      function m(e10) {
        tw(function() {
          throw e10;
        });
      }
      var v = Promise, b = "function" == typeof queueMicrotask ? queueMicrotask : function(e10) {
        v.resolve(null).then(e10).catch(m);
      }, w = null, S = 0;
      function E(e10, t2) {
        if (0 !== t2.byteLength) if (2048 < t2.byteLength) 0 < S && (e10.enqueue(new Uint8Array(w.buffer, 0, S)), w = new Uint8Array(2048), S = 0), e10.enqueue(t2);
        else {
          var r2 = w.length - S;
          r2 < t2.byteLength && (0 === r2 ? e10.enqueue(w) : (w.set(t2.subarray(0, r2), S), e10.enqueue(w), t2 = t2.subarray(r2)), w = new Uint8Array(2048), S = 0), w.set(t2, S), S += t2.byteLength;
        }
        return true;
      }
      var C = new TextEncoder();
      function R(e10) {
        return C.encode(e10);
      }
      function x(e10) {
        return e10.byteLength;
      }
      function T(e10, t2) {
        "function" == typeof e10.error ? e10.error(t2) : e10.close();
      }
      var k = Symbol.for("react.client.reference"), O = Symbol.for("react.server.reference");
      function P(e10, t2, r2) {
        return Object.defineProperties(e10, { $$typeof: { value: k }, $$id: { value: t2 }, $$async: { value: r2 } });
      }
      var A = Function.prototype.bind, N = Array.prototype.slice;
      function L() {
        var e10 = A.apply(this, arguments);
        if (this.$$typeof === O) {
          var t2 = N.call(arguments, 1);
          return Object.defineProperties(e10, { $$typeof: { value: O }, $$id: { value: this.$$id }, $$bound: t2 = { value: this.$$bound ? this.$$bound.concat(t2) : t2 }, bind: { value: L, configurable: true } });
        }
        return e10;
      }
      var I = Promise.prototype, M = { get: function(e10, t2) {
        switch (t2) {
          case "$$typeof":
            return e10.$$typeof;
          case "$$id":
            return e10.$$id;
          case "$$async":
            return e10.$$async;
          case "name":
            return e10.name;
          case "displayName":
          case "defaultProps":
          case "toJSON":
            return;
          case Symbol.toPrimitive:
            return Object.prototype[Symbol.toPrimitive];
          case Symbol.toStringTag:
            return Object.prototype[Symbol.toStringTag];
          case "Provider":
            throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
          case "then":
            throw Error("Cannot await or return from a thenable. You cannot await a client module from a server component.");
        }
        throw Error("Cannot access " + String(e10.name) + "." + String(t2) + " on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");
      }, set: function() {
        throw Error("Cannot assign to a client module from a server module.");
      } };
      function j(e10, t2) {
        switch (t2) {
          case "$$typeof":
            return e10.$$typeof;
          case "$$id":
            return e10.$$id;
          case "$$async":
            return e10.$$async;
          case "name":
            return e10.name;
          case "defaultProps":
          case "toJSON":
            return;
          case Symbol.toPrimitive:
            return Object.prototype[Symbol.toPrimitive];
          case Symbol.toStringTag:
            return Object.prototype[Symbol.toStringTag];
          case "__esModule":
            var r2 = e10.$$id;
            return e10.default = P(function() {
              throw Error("Attempted to call the default export of " + r2 + " from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
            }, e10.$$id + "#", e10.$$async), true;
          case "then":
            if (e10.then) return e10.then;
            if (e10.$$async) return;
            var n2 = P({}, e10.$$id, true), i2 = new Proxy(n2, $);
            return e10.status = "fulfilled", e10.value = i2, e10.then = P(function(e11) {
              return Promise.resolve(e11(i2));
            }, e10.$$id + "#then", false);
        }
        if ("symbol" == typeof t2) throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");
        return (n2 = e10[t2]) || (Object.defineProperty(n2 = P(function() {
          throw Error("Attempted to call " + String(t2) + "() from the server but " + String(t2) + " is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
        }, e10.$$id + "#" + t2, e10.$$async), "name", { value: t2 }), n2 = e10[t2] = new Proxy(n2, M)), n2;
      }
      var $ = { get: function(e10, t2) {
        return j(e10, t2);
      }, getOwnPropertyDescriptor: function(e10, t2) {
        var r2 = Object.getOwnPropertyDescriptor(e10, t2);
        return r2 || (r2 = { value: j(e10, t2), writable: false, configurable: false, enumerable: false }, Object.defineProperty(e10, t2, r2)), r2;
      }, getPrototypeOf: function() {
        return I;
      }, set: function() {
        throw Error("Cannot assign to a client module from a server module.");
      } }, D = n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, q = D.d;
      function U(e10) {
        if (null == e10) return null;
        var t2, r2 = false, n2 = {};
        for (t2 in e10) null != e10[t2] && (r2 = true, n2[t2] = e10[t2]);
        return r2 ? n2 : null;
      }
      D.d = { f: q.f, r: q.r, D: function(e10) {
        if ("string" == typeof e10 && e10) {
          var t2 = em();
          if (t2) {
            var r2 = t2.hints, n2 = "D|" + e10;
            r2.has(n2) || (r2.add(n2), eb(t2, "D", e10));
          } else q.D(e10);
        }
      }, C: function(e10, t2) {
        if ("string" == typeof e10) {
          var r2 = em();
          if (r2) {
            var n2 = r2.hints, i2 = "C|" + (null == t2 ? "null" : t2) + "|" + e10;
            n2.has(i2) || (n2.add(i2), "string" == typeof t2 ? eb(r2, "C", [e10, t2]) : eb(r2, "C", e10));
          } else q.C(e10, t2);
        }
      }, L: function(e10, t2, r2) {
        if ("string" == typeof e10) {
          var n2 = em();
          if (n2) {
            var i2 = n2.hints, a2 = "L";
            if ("image" === t2 && r2) {
              var o2 = r2.imageSrcSet, s2 = r2.imageSizes, u2 = "";
              "string" == typeof o2 && "" !== o2 ? (u2 += "[" + o2 + "]", "string" == typeof s2 && (u2 += "[" + s2 + "]")) : u2 += "[][]" + e10, a2 += "[image]" + u2;
            } else a2 += "[" + t2 + "]" + e10;
            i2.has(a2) || (i2.add(a2), (r2 = U(r2)) ? eb(n2, "L", [e10, t2, r2]) : eb(n2, "L", [e10, t2]));
          } else q.L(e10, t2, r2);
        }
      }, m: function(e10, t2) {
        if ("string" == typeof e10) {
          var r2 = em();
          if (r2) {
            var n2 = r2.hints, i2 = "m|" + e10;
            if (n2.has(i2)) return;
            return n2.add(i2), (t2 = U(t2)) ? eb(r2, "m", [e10, t2]) : eb(r2, "m", e10);
          }
          q.m(e10, t2);
        }
      }, X: function(e10, t2) {
        if ("string" == typeof e10) {
          var r2 = em();
          if (r2) {
            var n2 = r2.hints, i2 = "X|" + e10;
            if (n2.has(i2)) return;
            return n2.add(i2), (t2 = U(t2)) ? eb(r2, "X", [e10, t2]) : eb(r2, "X", e10);
          }
          q.X(e10, t2);
        }
      }, S: function(e10, t2, r2) {
        if ("string" == typeof e10) {
          var n2 = em();
          if (n2) {
            var i2 = n2.hints, a2 = "S|" + e10;
            if (i2.has(a2)) return;
            return i2.add(a2), (r2 = U(r2)) ? eb(n2, "S", [e10, "string" == typeof t2 ? t2 : 0, r2]) : "string" == typeof t2 ? eb(n2, "S", [e10, t2]) : eb(n2, "S", e10);
          }
          q.S(e10, t2, r2);
        }
      }, M: function(e10, t2) {
        if ("string" == typeof e10) {
          var r2 = em();
          if (r2) {
            var n2 = r2.hints, i2 = "M|" + e10;
            if (n2.has(i2)) return;
            return n2.add(i2), (t2 = U(t2)) ? eb(r2, "M", [e10, t2]) : eb(r2, "M", e10);
          }
          q.M(e10, t2);
        }
      } };
      var G = "function" == typeof AsyncLocalStorage, B = G ? new AsyncLocalStorage() : null;
      "object" == typeof async_hooks && async_hooks.createHook, "object" == typeof async_hooks && async_hooks.executionAsyncId;
      var H = Symbol.for("react.temporary.reference"), z = { get: function(e10, t2) {
        switch (t2) {
          case "$$typeof":
            return e10.$$typeof;
          case "name":
          case "displayName":
          case "defaultProps":
          case "toJSON":
            return;
          case Symbol.toPrimitive:
            return Object.prototype[Symbol.toPrimitive];
          case Symbol.toStringTag:
            return Object.prototype[Symbol.toStringTag];
          case "Provider":
            throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
        }
        throw Error("Cannot access " + String(t2) + " on the server. You cannot dot into a temporary client reference from a server component. You can only pass the value through to the client.");
      }, set: function() {
        throw Error("Cannot assign to a temporary client reference from a server module.");
      } }, F = Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`.");
      function W() {
      }
      var V = null;
      function K() {
        if (null === V) throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");
        var e10 = V;
        return V = null, e10;
      }
      var X = null, Y = 0, J = null;
      function Z() {
        var e10 = J || [];
        return J = null, e10;
      }
      var Q = { readContext: er, use: function(e10) {
        if (null !== e10 && "object" == typeof e10 || "function" == typeof e10) {
          if ("function" == typeof e10.then) {
            var t2 = Y;
            Y += 1, null === J && (J = []);
            var r2 = J, n2 = e10, i2 = t2;
            switch (void 0 === (i2 = r2[i2]) ? r2.push(n2) : i2 !== n2 && (n2.then(W, W), n2 = i2), n2.status) {
              case "fulfilled":
                return n2.value;
              case "rejected":
                throw n2.reason;
              default:
                switch ("string" == typeof n2.status ? n2.then(W, W) : ((r2 = n2).status = "pending", r2.then(function(e11) {
                  if ("pending" === n2.status) {
                    var t3 = n2;
                    t3.status = "fulfilled", t3.value = e11;
                  }
                }, function(e11) {
                  if ("pending" === n2.status) {
                    var t3 = n2;
                    t3.status = "rejected", t3.reason = e11;
                  }
                })), n2.status) {
                  case "fulfilled":
                    return n2.value;
                  case "rejected":
                    throw n2.reason;
                }
                throw V = n2, F;
            }
          }
          e10.$$typeof === u && er();
        }
        if (e10.$$typeof === k) {
          if (null != e10.value && e10.value.$$typeof === u) throw Error("Cannot read a Client Context from a Server Component.");
          throw Error("Cannot use() an already resolved Client Reference.");
        }
        throw Error("An unsupported type was passed to use(): " + String(e10));
      }, useCallback: function(e10) {
        return e10;
      }, useContext: er, useEffect: ee, useImperativeHandle: ee, useLayoutEffect: ee, useInsertionEffect: ee, useMemo: function(e10) {
        return e10();
      }, useReducer: ee, useRef: ee, useState: ee, useDebugValue: function() {
      }, useDeferredValue: ee, useTransition: ee, useSyncExternalStore: ee, useId: function() {
        if (null === X) throw Error("useId can only be used while React is rendering");
        var e10 = X.identifierCount++;
        return ":" + X.identifierPrefix + "S" + e10.toString(32) + ":";
      }, useHostTransitionStatus: ee, useFormState: ee, useActionState: ee, useOptimistic: ee, useMemoCache: function(e10) {
        for (var t2 = Array(e10), r2 = 0; r2 < e10; r2++) t2[r2] = h;
        return t2;
      }, useCacheRefresh: function() {
        return et;
      } };
      function ee() {
        throw Error("This Hook is not supported in Server Components.");
      }
      function et() {
        throw Error("Refreshing the cache is not supported in Server Components.");
      }
      function er() {
        throw Error("Cannot read a Client Context from a Server Component.");
      }
      var en = { getCacheForType: function(e10) {
        var t2 = (t2 = em()) ? t2.cache : /* @__PURE__ */ new Map(), r2 = t2.get(e10);
        return void 0 === r2 && (r2 = e10(), t2.set(e10, r2)), r2;
      } }, ei = i.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;
      if (!ei) throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
      var ea = Array.isArray, eo = Object.getPrototypeOf;
      function es(e10) {
        return Object.prototype.toString.call(e10).replace(/^\[object (.*)\]$/, function(e11, t2) {
          return t2;
        });
      }
      function eu(e10) {
        switch (typeof e10) {
          case "string":
            return JSON.stringify(10 >= e10.length ? e10 : e10.slice(0, 10) + "...");
          case "object":
            if (ea(e10)) return "[...]";
            if (null !== e10 && e10.$$typeof === el) return "client";
            return "Object" === (e10 = es(e10)) ? "{...}" : e10;
          case "function":
            return e10.$$typeof === el ? "client" : (e10 = e10.displayName || e10.name) ? "function " + e10 : "function";
          default:
            return String(e10);
        }
      }
      var el = Symbol.for("react.client.reference");
      function ec(e10, t2) {
        var r2 = es(e10);
        if ("Object" !== r2 && "Array" !== r2) return r2;
        r2 = -1;
        var n2 = 0;
        if (ea(e10)) {
          for (var i2 = "[", a2 = 0; a2 < e10.length; a2++) {
            0 < a2 && (i2 += ", ");
            var s2 = e10[a2];
            s2 = "object" == typeof s2 && null !== s2 ? ec(s2) : eu(s2), "" + a2 === t2 ? (r2 = i2.length, n2 = s2.length, i2 += s2) : i2 = 10 > s2.length && 40 > i2.length + s2.length ? i2 + s2 : i2 + "...";
          }
          i2 += "]";
        } else if (e10.$$typeof === o) i2 = "<" + function e11(t3) {
          if ("string" == typeof t3) return t3;
          switch (t3) {
            case c:
              return "Suspense";
            case d:
              return "SuspenseList";
          }
          if ("object" == typeof t3) switch (t3.$$typeof) {
            case l:
              return e11(t3.render);
            case f:
              return e11(t3.type);
            case p:
              var r3 = t3._payload;
              t3 = t3._init;
              try {
                return e11(t3(r3));
              } catch (e12) {
              }
          }
          return "";
        }(e10.type) + "/>";
        else {
          if (e10.$$typeof === el) return "client";
          for (s2 = 0, i2 = "{", a2 = Object.keys(e10); s2 < a2.length; s2++) {
            0 < s2 && (i2 += ", ");
            var u2 = a2[s2], h2 = JSON.stringify(u2);
            i2 += ('"' + u2 + '"' === h2 ? u2 : h2) + ": ", h2 = "object" == typeof (h2 = e10[u2]) && null !== h2 ? ec(h2) : eu(h2), u2 === t2 ? (r2 = i2.length, n2 = h2.length, i2 += h2) : i2 = 10 > h2.length && 40 > i2.length + h2.length ? i2 + h2 : i2 + "...";
          }
          i2 += "}";
        }
        return void 0 === t2 ? i2 : -1 < r2 && 0 < n2 ? "\n  " + i2 + "\n  " + (e10 = " ".repeat(r2) + "^".repeat(n2)) : "\n  " + i2;
      }
      var ed = Object.prototype, ef = JSON.stringify;
      function ep(e10) {
        console.error(e10);
      }
      function eh() {
      }
      function e_(e10, t2, r2, n2, i2, a2, o2, s2, u2, l2, c2) {
        if (null !== ei.A && ei.A !== en) throw Error("Currently React only supports one RSC renderer at a time.");
        ei.A = en, u2 = /* @__PURE__ */ new Set(), s2 = [];
        var d2 = /* @__PURE__ */ new Set();
        this.type = e10, this.status = 10, this.flushScheduled = false, this.destination = this.fatalError = null, this.bundlerConfig = r2, this.cache = /* @__PURE__ */ new Map(), this.pendingChunks = this.nextChunkId = 0, this.hints = d2, this.abortListeners = /* @__PURE__ */ new Set(), this.abortableTasks = u2, this.pingedTasks = s2, this.completedImportChunks = [], this.completedHintChunks = [], this.completedRegularChunks = [], this.completedErrorChunks = [], this.writtenSymbols = /* @__PURE__ */ new Map(), this.writtenClientReferences = /* @__PURE__ */ new Map(), this.writtenServerReferences = /* @__PURE__ */ new Map(), this.writtenObjects = /* @__PURE__ */ new WeakMap(), this.temporaryReferences = o2, this.identifierPrefix = i2 || "", this.identifierCount = 1, this.taintCleanupQueue = [], this.onError = void 0 === n2 ? ep : n2, this.onPostpone = void 0 === a2 ? eh : a2, this.onAllReady = l2, this.onFatalError = c2, e10 = ex(this, t2, null, false, u2), s2.push(e10);
      }
      function ey() {
      }
      var eg = null;
      function em() {
        if (eg) return eg;
        if (G) {
          var e10 = B.getStore();
          if (e10) return e10;
        }
        return null;
      }
      function ev(e10, t2, r2) {
        var n2 = ex(e10, null, t2.keyPath, t2.implicitSlot, e10.abortableTasks);
        switch (r2.status) {
          case "fulfilled":
            return n2.model = r2.value, eR(e10, n2), n2.id;
          case "rejected":
            return eG(e10, n2, r2.reason), n2.id;
          default:
            if (12 === e10.status) return e10.abortableTasks.delete(n2), n2.status = 3, t2 = ef(eT(e10.fatalError)), e$(e10, n2.id, t2), n2.id;
            "string" != typeof r2.status && (r2.status = "pending", r2.then(function(e11) {
              "pending" === r2.status && (r2.status = "fulfilled", r2.value = e11);
            }, function(e11) {
              "pending" === r2.status && (r2.status = "rejected", r2.reason = e11);
            }));
        }
        return r2.then(function(t3) {
          n2.model = t3, eR(e10, n2);
        }, function(t3) {
          0 === n2.status && (eG(e10, n2, t3), eV(e10));
        }), n2.id;
      }
      function eb(e10, t2, r2) {
        t2 = R(":H" + t2 + (r2 = ef(r2)) + "\n"), e10.completedHintChunks.push(t2), eV(e10);
      }
      function ew(e10) {
        if ("fulfilled" === e10.status) return e10.value;
        if ("rejected" === e10.status) throw e10.reason;
        throw e10;
      }
      function eS() {
      }
      function eE(e10, t2, r2, n2, i2) {
        var a2 = t2.thenableState;
        if (t2.thenableState = null, Y = 0, J = a2, i2 = n2(i2, void 0), 12 === e10.status) throw "object" == typeof i2 && null !== i2 && "function" == typeof i2.then && i2.$$typeof !== k && i2.then(eS, eS), null;
        return i2 = function(e11, t3, r3, n3) {
          if ("object" != typeof n3 || null === n3 || n3.$$typeof === k) return n3;
          if ("function" == typeof n3.then) return "fulfilled" === n3.status ? n3.value : function(e12) {
            switch (e12.status) {
              case "fulfilled":
              case "rejected":
                break;
              default:
                "string" != typeof e12.status && (e12.status = "pending", e12.then(function(t4) {
                  "pending" === e12.status && (e12.status = "fulfilled", e12.value = t4);
                }, function(t4) {
                  "pending" === e12.status && (e12.status = "rejected", e12.reason = t4);
                }));
            }
            return { $$typeof: p, _payload: e12, _init: ew };
          }(n3);
          var i3 = y(n3);
          return i3 ? ((e11 = {})[Symbol.iterator] = function() {
            return i3.call(n3);
          }, e11) : "function" != typeof n3[g] || "function" == typeof ReadableStream && n3 instanceof ReadableStream ? n3 : ((e11 = {})[g] = function() {
            return n3[g]();
          }, e11);
        }(e10, 0, 0, i2), n2 = t2.keyPath, a2 = t2.implicitSlot, null !== r2 ? t2.keyPath = null === n2 ? r2 : n2 + "," + r2 : null === n2 && (t2.implicitSlot = true), e10 = eL(e10, t2, eB, "", i2), t2.keyPath = n2, t2.implicitSlot = a2, e10;
      }
      function eC(e10, t2, r2) {
        return null !== t2.keyPath ? (e10 = [o, s, t2.keyPath, { children: r2 }], t2.implicitSlot ? [e10] : e10) : r2;
      }
      function eR(e10, t2) {
        var r2 = e10.pingedTasks;
        r2.push(t2), 1 === r2.length && (e10.flushScheduled = null !== e10.destination, 21 === e10.type || 10 === e10.status ? b(function() {
          return ez(e10);
        }) : tw(function() {
          return ez(e10);
        }, 0));
      }
      function ex(e10, t2, r2, n2, i2) {
        e10.pendingChunks++;
        var a2 = e10.nextChunkId++;
        "object" != typeof t2 || null === t2 || null !== r2 || n2 || e10.writtenObjects.set(t2, eT(a2));
        var s2 = { id: a2, status: 0, model: t2, keyPath: r2, implicitSlot: n2, ping: function() {
          return eR(e10, s2);
        }, toJSON: function(t3, r3) {
          var n3 = s2.keyPath, i3 = s2.implicitSlot;
          try {
            var a3 = eL(e10, s2, this, t3, r3);
          } catch (l2) {
            if (t3 = "object" == typeof (t3 = s2.model) && null !== t3 && (t3.$$typeof === o || t3.$$typeof === p), 12 === e10.status) s2.status = 3, n3 = e10.fatalError, a3 = t3 ? "$L" + n3.toString(16) : eT(n3);
            else if ("object" == typeof (r3 = l2 === F ? K() : l2) && null !== r3 && "function" == typeof r3.then) {
              var u2 = (a3 = ex(e10, s2.model, s2.keyPath, s2.implicitSlot, e10.abortableTasks)).ping;
              r3.then(u2, u2), a3.thenableState = Z(), s2.keyPath = n3, s2.implicitSlot = i3, a3 = t3 ? "$L" + a3.id.toString(16) : eT(a3.id);
            } else s2.keyPath = n3, s2.implicitSlot = i3, e10.pendingChunks++, n3 = e10.nextChunkId++, i3 = eI(e10, r3, s2), ej(e10, n3, i3), a3 = t3 ? "$L" + n3.toString(16) : eT(n3);
          }
          return a3;
        }, thenableState: null };
        return i2.add(s2), s2;
      }
      function eT(e10) {
        return "$" + e10.toString(16);
      }
      function ek(e10, t2, r2) {
        return e10 = ef(r2), R(t2 = t2.toString(16) + ":" + e10 + "\n");
      }
      function eO(e10, t2, r2, n2) {
        var i2 = n2.$$async ? n2.$$id + "#async" : n2.$$id, a2 = e10.writtenClientReferences, s2 = a2.get(i2);
        if (void 0 !== s2) return t2[0] === o && "1" === r2 ? "$L" + s2.toString(16) : eT(s2);
        try {
          var u2 = e10.bundlerConfig, l2 = n2.$$id;
          s2 = "";
          var c2 = u2[l2];
          if (c2) s2 = c2.name;
          else {
            var d2 = l2.lastIndexOf("#");
            if (-1 !== d2 && (s2 = l2.slice(d2 + 1), c2 = u2[l2.slice(0, d2)]), !c2) throw Error('Could not find the module "' + l2 + '" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');
          }
          if (true === c2.async && true === n2.$$async) throw Error('The module "' + l2 + '" is marked as an async ESM module but was loaded as a CJS proxy. This is probably a bug in the React Server Components bundler.');
          var f2 = true === c2.async || true === n2.$$async ? [c2.id, c2.chunks, s2, 1] : [c2.id, c2.chunks, s2];
          e10.pendingChunks++;
          var p2 = e10.nextChunkId++, h2 = ef(f2), _2 = p2.toString(16) + ":I" + h2 + "\n", y2 = R(_2);
          return e10.completedImportChunks.push(y2), a2.set(i2, p2), t2[0] === o && "1" === r2 ? "$L" + p2.toString(16) : eT(p2);
        } catch (n3) {
          return e10.pendingChunks++, t2 = e10.nextChunkId++, r2 = eI(e10, n3, null), ej(e10, t2, r2), eT(t2);
        }
      }
      function eP(e10, t2) {
        return t2 = ex(e10, t2, null, false, e10.abortableTasks), eH(e10, t2), t2.id;
      }
      function eA(e10, t2, r2) {
        e10.pendingChunks++;
        var n2 = e10.nextChunkId++;
        return eD(e10, n2, t2, r2), eT(n2);
      }
      var eN = false;
      function eL(e10, t2, r2, n2, i2) {
        if (t2.model = i2, i2 === o) return "$";
        if (null === i2) return null;
        if ("object" == typeof i2) {
          switch (i2.$$typeof) {
            case o:
              var u2 = null, c2 = e10.writtenObjects;
              if (null === t2.keyPath && !t2.implicitSlot) {
                var d2 = c2.get(i2);
                if (void 0 !== d2) if (eN !== i2) return d2;
                else eN = null;
                else -1 === n2.indexOf(":") && void 0 !== (r2 = c2.get(r2)) && (u2 = r2 + ":" + n2, c2.set(i2, u2));
              }
              return r2 = (n2 = i2.props).ref, "object" == typeof (e10 = function e11(t3, r3, n3, i3, a2, u3) {
                if (null != a2) throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");
                if ("function" == typeof n3 && n3.$$typeof !== k && n3.$$typeof !== H) return eE(t3, r3, i3, n3, u3);
                if (n3 === s && null === i3) return n3 = r3.implicitSlot, null === r3.keyPath && (r3.implicitSlot = true), u3 = eL(t3, r3, eB, "", u3.children), r3.implicitSlot = n3, u3;
                if (null != n3 && "object" == typeof n3 && n3.$$typeof !== k) switch (n3.$$typeof) {
                  case p:
                    if (n3 = (0, n3._init)(n3._payload), 12 === t3.status) throw null;
                    return e11(t3, r3, n3, i3, a2, u3);
                  case l:
                    return eE(t3, r3, i3, n3.render, u3);
                  case f:
                    return e11(t3, r3, n3.type, i3, a2, u3);
                }
                return t3 = i3, i3 = r3.keyPath, null === t3 ? t3 = i3 : null !== i3 && (t3 = i3 + "," + t3), u3 = [o, n3, t3, u3], r3 = r3.implicitSlot && null !== t3 ? [u3] : u3;
              }(e10, t2, i2.type, i2.key, void 0 !== r2 ? r2 : null, n2)) && null !== e10 && null !== u2 && (c2.has(e10) || c2.set(e10, u2)), e10;
            case p:
              if (t2.thenableState = null, i2 = (n2 = i2._init)(i2._payload), 12 === e10.status) throw null;
              return eL(e10, t2, eB, "", i2);
            case a:
              throw Error('A React Element from an older version of React was rendered. This is not supported. It can happen if:\n- Multiple copies of the "react" package is used.\n- A library pre-bundled an old copy of "react" or "react/jsx-runtime".\n- A compiler tries to "inline" JSX instead of using the runtime.');
          }
          if (i2.$$typeof === k) return eO(e10, r2, n2, i2);
          if (void 0 !== e10.temporaryReferences && void 0 !== (u2 = e10.temporaryReferences.get(i2))) return "$T" + u2;
          if (c2 = (u2 = e10.writtenObjects).get(i2), "function" == typeof i2.then) {
            if (void 0 !== c2) {
              if (null !== t2.keyPath || t2.implicitSlot) return "$@" + ev(e10, t2, i2).toString(16);
              if (eN !== i2) return c2;
              eN = null;
            }
            return e10 = "$@" + ev(e10, t2, i2).toString(16), u2.set(i2, e10), e10;
          }
          if (void 0 !== c2) if (eN !== i2) return c2;
          else eN = null;
          else if (-1 === n2.indexOf(":") && void 0 !== (c2 = u2.get(r2))) {
            if (d2 = n2, ea(r2) && r2[0] === o) switch (n2) {
              case "1":
                d2 = "type";
                break;
              case "2":
                d2 = "key";
                break;
              case "3":
                d2 = "props";
                break;
              case "4":
                d2 = "_owner";
            }
            u2.set(i2, c2 + ":" + d2);
          }
          if (ea(i2)) return eC(e10, t2, i2);
          if (i2 instanceof Map) return "$Q" + eP(e10, i2 = Array.from(i2)).toString(16);
          if (i2 instanceof Set) return "$W" + eP(e10, i2 = Array.from(i2)).toString(16);
          if ("function" == typeof FormData && i2 instanceof FormData) return "$K" + eP(e10, i2 = Array.from(i2.entries())).toString(16);
          if (i2 instanceof Error) return "$Z";
          if (i2 instanceof ArrayBuffer) return eA(e10, "A", new Uint8Array(i2));
          if (i2 instanceof Int8Array) return eA(e10, "O", i2);
          if (i2 instanceof Uint8Array) return eA(e10, "o", i2);
          if (i2 instanceof Uint8ClampedArray) return eA(e10, "U", i2);
          if (i2 instanceof Int16Array) return eA(e10, "S", i2);
          if (i2 instanceof Uint16Array) return eA(e10, "s", i2);
          if (i2 instanceof Int32Array) return eA(e10, "L", i2);
          if (i2 instanceof Uint32Array) return eA(e10, "l", i2);
          if (i2 instanceof Float32Array) return eA(e10, "G", i2);
          if (i2 instanceof Float64Array) return eA(e10, "g", i2);
          if (i2 instanceof BigInt64Array) return eA(e10, "M", i2);
          if (i2 instanceof BigUint64Array) return eA(e10, "m", i2);
          if (i2 instanceof DataView) return eA(e10, "V", i2);
          if ("function" == typeof Blob && i2 instanceof Blob) return function(e11, t3) {
            function r3(t4) {
              s2 || (s2 = true, e11.abortListeners.delete(n3), eG(e11, a2, t4), eV(e11), o2.cancel(t4).then(r3, r3));
            }
            function n3(t4) {
              s2 || (s2 = true, e11.abortListeners.delete(n3), eG(e11, a2, t4), eV(e11), o2.cancel(t4).then(r3, r3));
            }
            var i3 = [t3.type], a2 = ex(e11, i3, null, false, e11.abortableTasks), o2 = t3.stream().getReader(), s2 = false;
            return e11.abortListeners.add(n3), o2.read().then(function t4(u3) {
              if (!s2) if (!u3.done) return i3.push(u3.value), o2.read().then(t4).catch(r3);
              else e11.abortListeners.delete(n3), s2 = true, eR(e11, a2);
            }).catch(r3), "$B" + a2.id.toString(16);
          }(e10, i2);
          if (u2 = y(i2)) return (n2 = u2.call(i2)) === i2 ? "$i" + eP(e10, Array.from(n2)).toString(16) : eC(e10, t2, Array.from(n2));
          if ("function" == typeof ReadableStream && i2 instanceof ReadableStream) return function(e11, t3, r3) {
            function n3(t4) {
              u3 || (u3 = true, e11.abortListeners.delete(i3), eG(e11, s2, t4), eV(e11), o2.cancel(t4).then(n3, n3));
            }
            function i3(t4) {
              u3 || (u3 = true, e11.abortListeners.delete(i3), eG(e11, s2, t4), eV(e11), o2.cancel(t4).then(n3, n3));
            }
            var a2 = r3.supportsBYOB;
            if (void 0 === a2) try {
              r3.getReader({ mode: "byob" }).releaseLock(), a2 = true;
            } catch (e12) {
              a2 = false;
            }
            var o2 = r3.getReader(), s2 = ex(e11, t3.model, t3.keyPath, t3.implicitSlot, e11.abortableTasks);
            e11.abortableTasks.delete(s2), e11.pendingChunks++, t3 = s2.id.toString(16) + ":" + (a2 ? "r" : "R") + "\n", e11.completedRegularChunks.push(R(t3));
            var u3 = false;
            return e11.abortListeners.add(i3), o2.read().then(function t4(r4) {
              if (!u3) if (r4.done) e11.abortListeners.delete(i3), r4 = s2.id.toString(16) + ":C\n", e11.completedRegularChunks.push(R(r4)), eV(e11), u3 = true;
              else try {
                s2.model = r4.value, e11.pendingChunks++, eU(e11, s2, s2.model), eV(e11), o2.read().then(t4, n3);
              } catch (e12) {
                n3(e12);
              }
            }, n3), eT(s2.id);
          }(e10, t2, i2);
          if ("function" == typeof (u2 = i2[g])) return null !== t2.keyPath ? (e10 = [o, s, t2.keyPath, { children: i2 }], e10 = t2.implicitSlot ? [e10] : e10) : (n2 = u2.call(i2), e10 = function(e11, t3, r3, n3) {
            function i3(t4) {
              s2 || (s2 = true, e11.abortListeners.delete(a2), eG(e11, o2, t4), eV(e11), "function" == typeof n3.throw && n3.throw(t4).then(i3, i3));
            }
            function a2(t4) {
              s2 || (s2 = true, e11.abortListeners.delete(a2), eG(e11, o2, t4), eV(e11), "function" == typeof n3.throw && n3.throw(t4).then(i3, i3));
            }
            r3 = r3 === n3;
            var o2 = ex(e11, t3.model, t3.keyPath, t3.implicitSlot, e11.abortableTasks);
            e11.abortableTasks.delete(o2), e11.pendingChunks++, t3 = o2.id.toString(16) + ":" + (r3 ? "x" : "X") + "\n", e11.completedRegularChunks.push(R(t3));
            var s2 = false;
            return e11.abortListeners.add(a2), n3.next().then(function t4(r4) {
              if (!s2) if (r4.done) {
                if (e11.abortListeners.delete(a2), void 0 === r4.value) var u3 = o2.id.toString(16) + ":C\n";
                else try {
                  var l2 = eP(e11, r4.value);
                  u3 = o2.id.toString(16) + ":C" + ef(eT(l2)) + "\n";
                } catch (e12) {
                  i3(e12);
                  return;
                }
                e11.completedRegularChunks.push(R(u3)), eV(e11), s2 = true;
              } else try {
                o2.model = r4.value, e11.pendingChunks++, eU(e11, o2, o2.model), eV(e11), n3.next().then(t4, i3);
              } catch (e12) {
                i3(e12);
              }
            }, i3), eT(o2.id);
          }(e10, t2, i2, n2)), e10;
          if (i2 instanceof Date) return "$D" + i2.toJSON();
          if ((e10 = eo(i2)) !== ed && (null === e10 || null !== eo(e10))) throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported." + ec(r2, n2));
          return i2;
        }
        if ("string" == typeof i2) return "Z" === i2[i2.length - 1] && r2[n2] instanceof Date ? "$D" + i2 : 1024 <= i2.length && null !== x ? (e10.pendingChunks++, t2 = e10.nextChunkId++, eq(e10, t2, i2), eT(t2)) : e10 = "$" === i2[0] ? "$" + i2 : i2;
        if ("boolean" == typeof i2) return i2;
        if ("number" == typeof i2) return Number.isFinite(i2) ? 0 === i2 && -1 / 0 == 1 / i2 ? "$-0" : i2 : 1 / 0 === i2 ? "$Infinity" : -1 / 0 === i2 ? "$-Infinity" : "$NaN";
        if (void 0 === i2) return "$undefined";
        if ("function" == typeof i2) {
          if (i2.$$typeof === k) return eO(e10, r2, n2, i2);
          if (i2.$$typeof === O) return void 0 !== (n2 = (t2 = e10.writtenServerReferences).get(i2)) ? e10 = "$F" + n2.toString(16) : (n2 = null === (n2 = i2.$$bound) ? null : Promise.resolve(n2), e10 = eP(e10, { id: i2.$$id, bound: n2 }), t2.set(i2, e10), e10 = "$F" + e10.toString(16)), e10;
          if (void 0 !== e10.temporaryReferences && void 0 !== (e10 = e10.temporaryReferences.get(i2))) return "$T" + e10;
          if (i2.$$typeof === H) throw Error("Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.");
          if (/^on[A-Z]/.test(n2)) throw Error("Event handlers cannot be passed to Client Component props." + ec(r2, n2) + "\nIf you need interactivity, consider converting part of this to a Client Component.");
          throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.' + ec(r2, n2));
        }
        if ("symbol" == typeof i2) {
          if (void 0 !== (u2 = (t2 = e10.writtenSymbols).get(i2))) return eT(u2);
          if (Symbol.for(u2 = i2.description) !== i2) throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for(" + i2.description + ") cannot be found among global symbols." + ec(r2, n2));
          return e10.pendingChunks++, n2 = e10.nextChunkId++, r2 = ek(e10, n2, "$S" + u2), e10.completedImportChunks.push(r2), t2.set(i2, n2), eT(n2);
        }
        if ("bigint" == typeof i2) return "$n" + i2.toString(10);
        throw Error("Type " + typeof i2 + " is not supported in Client Component props." + ec(r2, n2));
      }
      function eI(e10, t2) {
        var r2 = eg;
        eg = null;
        try {
          var n2 = e10.onError, i2 = G ? B.run(void 0, n2, t2) : n2(t2);
        } finally {
          eg = r2;
        }
        if (null != i2 && "string" != typeof i2) throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "' + typeof i2 + '" instead');
        return i2 || "";
      }
      function eM(e10, t2) {
        (0, e10.onFatalError)(t2), null !== e10.destination ? (e10.status = 14, T(e10.destination, t2)) : (e10.status = 13, e10.fatalError = t2);
      }
      function ej(e10, t2, r2) {
        r2 = { digest: r2 }, t2 = R(t2 = t2.toString(16) + ":E" + ef(r2) + "\n"), e10.completedErrorChunks.push(t2);
      }
      function e$(e10, t2, r2) {
        t2 = R(t2 = t2.toString(16) + ":" + r2 + "\n"), e10.completedRegularChunks.push(t2);
      }
      function eD(e10, t2, r2, n2) {
        e10.pendingChunks++;
        var i2 = new Uint8Array(n2.buffer, n2.byteOffset, n2.byteLength);
        i2 = (n2 = 2048 < n2.byteLength ? i2.slice() : i2).byteLength, t2 = R(t2 = t2.toString(16) + ":" + r2 + i2.toString(16) + ","), e10.completedRegularChunks.push(t2, n2);
      }
      function eq(e10, t2, r2) {
        if (null === x) throw Error("Existence of byteLengthOfChunk should have already been checked. This is a bug in React.");
        e10.pendingChunks++;
        var n2 = (r2 = R(r2)).byteLength;
        t2 = R(t2 = t2.toString(16) + ":T" + n2.toString(16) + ","), e10.completedRegularChunks.push(t2, r2);
      }
      function eU(e10, t2, r2) {
        var n2 = t2.id;
        "string" == typeof r2 && null !== x ? eq(e10, n2, r2) : r2 instanceof ArrayBuffer ? eD(e10, n2, "A", new Uint8Array(r2)) : r2 instanceof Int8Array ? eD(e10, n2, "O", r2) : r2 instanceof Uint8Array ? eD(e10, n2, "o", r2) : r2 instanceof Uint8ClampedArray ? eD(e10, n2, "U", r2) : r2 instanceof Int16Array ? eD(e10, n2, "S", r2) : r2 instanceof Uint16Array ? eD(e10, n2, "s", r2) : r2 instanceof Int32Array ? eD(e10, n2, "L", r2) : r2 instanceof Uint32Array ? eD(e10, n2, "l", r2) : r2 instanceof Float32Array ? eD(e10, n2, "G", r2) : r2 instanceof Float64Array ? eD(e10, n2, "g", r2) : r2 instanceof BigInt64Array ? eD(e10, n2, "M", r2) : r2 instanceof BigUint64Array ? eD(e10, n2, "m", r2) : r2 instanceof DataView ? eD(e10, n2, "V", r2) : (r2 = ef(r2, t2.toJSON), e$(e10, t2.id, r2));
      }
      function eG(e10, t2, r2) {
        e10.abortableTasks.delete(t2), t2.status = 4, r2 = eI(e10, r2, t2), ej(e10, t2.id, r2);
      }
      var eB = {};
      function eH(e10, t2) {
        if (0 === t2.status) {
          t2.status = 5;
          try {
            eN = t2.model;
            var r2 = eL(e10, t2, eB, "", t2.model);
            if (eN = r2, t2.keyPath = null, t2.implicitSlot = false, "object" == typeof r2 && null !== r2) e10.writtenObjects.set(r2, eT(t2.id)), eU(e10, t2, r2);
            else {
              var n2 = ef(r2);
              e$(e10, t2.id, n2);
            }
            e10.abortableTasks.delete(t2), t2.status = 1;
          } catch (r3) {
            if (12 === e10.status) {
              e10.abortableTasks.delete(t2), t2.status = 3;
              var i2 = ef(eT(e10.fatalError));
              e$(e10, t2.id, i2);
            } else {
              var a2 = r3 === F ? K() : r3;
              if ("object" == typeof a2 && null !== a2 && "function" == typeof a2.then) {
                t2.status = 0, t2.thenableState = Z();
                var o2 = t2.ping;
                a2.then(o2, o2);
              } else eG(e10, t2, a2);
            }
          } finally {
          }
        }
      }
      function ez(e10) {
        var t2 = ei.H;
        ei.H = Q;
        var r2 = eg;
        X = eg = e10;
        var n2 = 0 < e10.abortableTasks.size;
        try {
          var i2 = e10.pingedTasks;
          e10.pingedTasks = [];
          for (var a2 = 0; a2 < i2.length; a2++) eH(e10, i2[a2]);
          null !== e10.destination && eF(e10, e10.destination), n2 && 0 === e10.abortableTasks.size && (0, e10.onAllReady)();
        } catch (t3) {
          eI(e10, t3, null), eM(e10, t3);
        } finally {
          ei.H = t2, X = null, eg = r2;
        }
      }
      function eF(e10, t2) {
        w = new Uint8Array(2048), S = 0;
        try {
          for (var r2 = e10.completedImportChunks, n2 = 0; n2 < r2.length; n2++) e10.pendingChunks--, E(t2, r2[n2]);
          r2.splice(0, n2);
          var i2 = e10.completedHintChunks;
          for (n2 = 0; n2 < i2.length; n2++) E(t2, i2[n2]);
          i2.splice(0, n2);
          var a2 = e10.completedRegularChunks;
          for (n2 = 0; n2 < a2.length; n2++) e10.pendingChunks--, E(t2, a2[n2]);
          a2.splice(0, n2);
          var o2 = e10.completedErrorChunks;
          for (n2 = 0; n2 < o2.length; n2++) e10.pendingChunks--, E(t2, o2[n2]);
          o2.splice(0, n2);
        } finally {
          e10.flushScheduled = false, w && 0 < S && (t2.enqueue(new Uint8Array(w.buffer, 0, S)), w = null, S = 0);
        }
        0 === e10.pendingChunks && (e10.status = 14, t2.close(), e10.destination = null);
      }
      function eW(e10) {
        e10.flushScheduled = null !== e10.destination, G ? b(function() {
          B.run(e10, ez, e10);
        }) : b(function() {
          return ez(e10);
        }), tw(function() {
          10 === e10.status && (e10.status = 11);
        }, 0);
      }
      function eV(e10) {
        false === e10.flushScheduled && 0 === e10.pingedTasks.length && null !== e10.destination && (e10.flushScheduled = true, tw(function() {
          e10.flushScheduled = false;
          var t2 = e10.destination;
          t2 && eF(e10, t2);
        }, 0));
      }
      function eK(e10, t2) {
        if (13 === e10.status) e10.status = 14, T(t2, e10.fatalError);
        else if (14 !== e10.status && null === e10.destination) {
          e10.destination = t2;
          try {
            eF(e10, t2);
          } catch (t3) {
            eI(e10, t3, null), eM(e10, t3);
          }
        }
      }
      function eX(e10, t2) {
        try {
          11 >= e10.status && (e10.status = 12);
          var r2 = e10.abortableTasks;
          if (0 < r2.size) {
            var n2 = void 0 === t2 ? Error("The render was aborted by the server without a reason.") : "object" == typeof t2 && null !== t2 && "function" == typeof t2.then ? Error("The render was aborted by the server with a promise.") : t2, i2 = eI(e10, n2, null), a2 = e10.nextChunkId++;
            e10.fatalError = a2, e10.pendingChunks++, ej(e10, a2, i2, n2), r2.forEach(function(t3) {
              if (5 !== t3.status) {
                t3.status = 3;
                var r3 = eT(a2);
                t3 = ek(e10, t3.id, r3), e10.completedErrorChunks.push(t3);
              }
            }), r2.clear(), (0, e10.onAllReady)();
          }
          var o2 = e10.abortListeners;
          if (0 < o2.size) {
            var s2 = void 0 === t2 ? Error("The render was aborted by the server without a reason.") : "object" == typeof t2 && null !== t2 && "function" == typeof t2.then ? Error("The render was aborted by the server with a promise.") : t2;
            o2.forEach(function(e11) {
              return e11(s2);
            }), o2.clear();
          }
          null !== e10.destination && eF(e10, e10.destination);
        } catch (t3) {
          eI(e10, t3, null), eM(e10, t3);
        }
      }
      function eY(e10, t2) {
        var r2 = "", n2 = e10[t2];
        if (n2) r2 = n2.name;
        else {
          var i2 = t2.lastIndexOf("#");
          if (-1 !== i2 && (r2 = t2.slice(i2 + 1), n2 = e10[t2.slice(0, i2)]), !n2) throw Error('Could not find the module "' + t2 + '" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');
        }
        return n2.async ? [n2.id, n2.chunks, r2, 1] : [n2.id, n2.chunks, r2];
      }
      var eJ = /* @__PURE__ */ new Map();
      function eZ(e10) {
        var t2 = globalThis.__next_require__(e10);
        return "function" != typeof t2.then || "fulfilled" === t2.status ? null : (t2.then(function(e11) {
          t2.status = "fulfilled", t2.value = e11;
        }, function(e11) {
          t2.status = "rejected", t2.reason = e11;
        }), t2);
      }
      function eQ() {
      }
      function e0(e10) {
        for (var t2 = e10[1], n2 = [], i2 = 0; i2 < t2.length; ) {
          var a2 = t2[i2++];
          t2[i2++];
          var o2 = eJ.get(a2);
          if (void 0 === o2) {
            o2 = r.e(a2), n2.push(o2);
            var s2 = eJ.set.bind(eJ, a2, null);
            o2.then(s2, eQ), eJ.set(a2, o2);
          } else null !== o2 && n2.push(o2);
        }
        return 4 === e10.length ? 0 === n2.length ? eZ(e10[0]) : Promise.all(n2).then(function() {
          return eZ(e10[0]);
        }) : 0 < n2.length ? Promise.all(n2) : null;
      }
      function e1(e10) {
        var t2 = globalThis.__next_require__(e10[0]);
        if (4 === e10.length && "function" == typeof t2.then) if ("fulfilled" === t2.status) t2 = t2.value;
        else throw t2.reason;
        return "*" === e10[2] ? t2 : "" === e10[2] ? t2.__esModule ? t2.default : t2 : t2[e10[2]];
      }
      var e3 = Object.prototype.hasOwnProperty;
      function e2(e10, t2, r2, n2) {
        this.status = e10, this.value = t2, this.reason = r2, this._response = n2;
      }
      function e4(e10) {
        return new e2("pending", null, null, e10);
      }
      function e5(e10, t2) {
        for (var r2 = 0; r2 < e10.length; r2++) (0, e10[r2])(t2);
      }
      function e6(e10, t2) {
        if ("pending" !== e10.status && "blocked" !== e10.status) e10.reason.error(t2);
        else {
          var r2 = e10.reason;
          e10.status = "rejected", e10.reason = t2, null !== r2 && e5(r2, t2);
        }
      }
      function e9(e10, t2, r2) {
        if ("pending" !== e10.status) e10 = e10.reason, "C" === t2[0] ? e10.close("C" === t2 ? '"$undefined"' : t2.slice(1)) : e10.enqueueModel(t2);
        else {
          var n2 = e10.value, i2 = e10.reason;
          if (e10.status = "resolved_model", e10.value = t2, e10.reason = r2, null !== n2) switch (tr(e10), e10.status) {
            case "fulfilled":
              e5(n2, e10.value);
              break;
            case "pending":
            case "blocked":
            case "cyclic":
              if (e10.value) for (t2 = 0; t2 < n2.length; t2++) e10.value.push(n2[t2]);
              else e10.value = n2;
              if (e10.reason) {
                if (i2) for (t2 = 0; t2 < i2.length; t2++) e10.reason.push(i2[t2]);
              } else e10.reason = i2;
              break;
            case "rejected":
              i2 && e5(i2, e10.reason);
          }
        }
      }
      function e8(e10, t2, r2) {
        return new e2("resolved_model", (r2 ? '{"done":true,"value":' : '{"done":false,"value":') + t2 + "}", -1, e10);
      }
      function e7(e10, t2, r2) {
        e9(e10, (r2 ? '{"done":true,"value":' : '{"done":false,"value":') + t2 + "}", -1);
      }
      e2.prototype = Object.create(Promise.prototype), e2.prototype.then = function(e10, t2) {
        switch ("resolved_model" === this.status && tr(this), this.status) {
          case "fulfilled":
            e10(this.value);
            break;
          case "pending":
          case "blocked":
          case "cyclic":
            e10 && (null === this.value && (this.value = []), this.value.push(e10)), t2 && (null === this.reason && (this.reason = []), this.reason.push(t2));
            break;
          default:
            t2(this.reason);
        }
      };
      var te = null, tt = null;
      function tr(e10) {
        var t2 = te, r2 = tt;
        te = e10, tt = null;
        var n2 = -1 === e10.reason ? void 0 : e10.reason.toString(16), i2 = e10.value;
        e10.status = "cyclic", e10.value = null, e10.reason = null;
        try {
          var a2 = JSON.parse(i2), o2 = function e11(t3, r3, n3, i3, a3) {
            if ("string" == typeof i3) return function(e12, t4, r4, n4, i4) {
              if ("$" === n4[0]) {
                switch (n4[1]) {
                  case "$":
                    return n4.slice(1);
                  case "@":
                    return ti(e12, t4 = parseInt(n4.slice(2), 16));
                  case "F":
                    return n4 = ts(e12, n4 = n4.slice(2), t4, r4, td), function(e13, t5, r5, n5, i5, a5) {
                      var o5 = eY(e13._bundlerConfig, t5);
                      if (t5 = e0(o5), r5) r5 = Promise.all([r5, t5]).then(function(e14) {
                        e14 = e14[0];
                        var t6 = e1(o5);
                        return t6.bind.apply(t6, [null].concat(e14));
                      });
                      else {
                        if (!t5) return e1(o5);
                        r5 = Promise.resolve(t5).then(function() {
                          return e1(o5);
                        });
                      }
                      return r5.then(ta(n5, i5, a5, false, e13, td, []), to(n5)), null;
                    }(e12, n4.id, n4.bound, te, t4, r4);
                  case "T":
                    var a4, o4;
                    if (void 0 === i4 || void 0 === e12._temporaryReferences) throw Error("Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.");
                    return a4 = e12._temporaryReferences, o4 = new Proxy(o4 = Object.defineProperties(function() {
                      throw Error("Attempted to call a temporary Client Reference from the server but it is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
                    }, { $$typeof: { value: H } }), z), a4.set(o4, i4), o4;
                  case "Q":
                    return ts(e12, n4 = n4.slice(2), t4, r4, tu);
                  case "W":
                    return ts(e12, n4 = n4.slice(2), t4, r4, tl);
                  case "K":
                    t4 = n4.slice(2);
                    var s3 = e12._prefix + t4 + "_", u2 = new FormData();
                    return e12._formData.forEach(function(e13, t5) {
                      t5.startsWith(s3) && u2.append(t5.slice(s3.length), e13);
                    }), u2;
                  case "i":
                    return ts(e12, n4 = n4.slice(2), t4, r4, tc);
                  case "I":
                    return 1 / 0;
                  case "-":
                    return "$-0" === n4 ? -0 : -1 / 0;
                  case "N":
                    return NaN;
                  case "u":
                    return;
                  case "D":
                    return new Date(Date.parse(n4.slice(2)));
                  case "n":
                    return BigInt(n4.slice(2));
                }
                switch (n4[1]) {
                  case "A":
                    return tf(e12, n4, ArrayBuffer, 1, t4, r4);
                  case "O":
                    return tf(e12, n4, Int8Array, 1, t4, r4);
                  case "o":
                    return tf(e12, n4, Uint8Array, 1, t4, r4);
                  case "U":
                    return tf(e12, n4, Uint8ClampedArray, 1, t4, r4);
                  case "S":
                    return tf(e12, n4, Int16Array, 2, t4, r4);
                  case "s":
                    return tf(e12, n4, Uint16Array, 2, t4, r4);
                  case "L":
                    return tf(e12, n4, Int32Array, 4, t4, r4);
                  case "l":
                    return tf(e12, n4, Uint32Array, 4, t4, r4);
                  case "G":
                    return tf(e12, n4, Float32Array, 4, t4, r4);
                  case "g":
                    return tf(e12, n4, Float64Array, 8, t4, r4);
                  case "M":
                    return tf(e12, n4, BigInt64Array, 8, t4, r4);
                  case "m":
                    return tf(e12, n4, BigUint64Array, 8, t4, r4);
                  case "V":
                    return tf(e12, n4, DataView, 1, t4, r4);
                  case "B":
                    return t4 = parseInt(n4.slice(2), 16), e12._formData.get(e12._prefix + t4);
                }
                switch (n4[1]) {
                  case "R":
                    return th(e12, n4, void 0);
                  case "r":
                    return th(e12, n4, "bytes");
                  case "X":
                    return ty(e12, n4, false);
                  case "x":
                    return ty(e12, n4, true);
                }
                return ts(e12, n4 = n4.slice(1), t4, r4, td);
              }
              return n4;
            }(t3, r3, n3, i3, a3);
            if ("object" == typeof i3 && null !== i3) if (void 0 !== a3 && void 0 !== t3._temporaryReferences && t3._temporaryReferences.set(i3, a3), Array.isArray(i3)) for (var o3 = 0; o3 < i3.length; o3++) i3[o3] = e11(t3, i3, "" + o3, i3[o3], void 0 !== a3 ? a3 + ":" + o3 : void 0);
            else for (o3 in i3) e3.call(i3, o3) && (r3 = void 0 !== a3 && -1 === o3.indexOf(":") ? a3 + ":" + o3 : void 0, void 0 !== (r3 = e11(t3, i3, o3, i3[o3], r3)) ? i3[o3] = r3 : delete i3[o3]);
            return i3;
          }(e10._response, { "": a2 }, "", a2, n2);
          if (null !== tt && 0 < tt.deps) tt.value = o2, e10.status = "blocked";
          else {
            var s2 = e10.value;
            e10.status = "fulfilled", e10.value = o2, null !== s2 && e5(s2, o2);
          }
        } catch (t3) {
          e10.status = "rejected", e10.reason = t3;
        } finally {
          te = t2, tt = r2;
        }
      }
      function tn(e10, t2) {
        e10._closed = true, e10._closedReason = t2, e10._chunks.forEach(function(e11) {
          "pending" === e11.status && e6(e11, t2);
        });
      }
      function ti(e10, t2) {
        var r2 = e10._chunks, n2 = r2.get(t2);
        return n2 || (n2 = null != (n2 = e10._formData.get(e10._prefix + t2)) ? new e2("resolved_model", n2, t2, e10) : e10._closed ? new e2("rejected", null, e10._closedReason, e10) : e4(e10), r2.set(t2, n2)), n2;
      }
      function ta(e10, t2, r2, n2, i2, a2, o2) {
        if (tt) {
          var s2 = tt;
          n2 || s2.deps++;
        } else s2 = tt = { deps: +!n2, value: null };
        return function(n3) {
          for (var u2 = 1; u2 < o2.length; u2++) n3 = n3[o2[u2]];
          t2[r2] = a2(i2, n3), "" === r2 && null === s2.value && (s2.value = t2[r2]), s2.deps--, 0 === s2.deps && "blocked" === e10.status && (n3 = e10.value, e10.status = "fulfilled", e10.value = s2.value, null !== n3 && e5(n3, s2.value));
        };
      }
      function to(e10) {
        return function(t2) {
          return e6(e10, t2);
        };
      }
      function ts(e10, t2, r2, n2, i2) {
        var a2 = parseInt((t2 = t2.split(":"))[0], 16);
        switch ("resolved_model" === (a2 = ti(e10, a2)).status && tr(a2), a2.status) {
          case "fulfilled":
            for (n2 = 1, r2 = a2.value; n2 < t2.length; n2++) r2 = r2[t2[n2]];
            return i2(e10, r2);
          case "pending":
          case "blocked":
          case "cyclic":
            var o2 = te;
            return a2.then(ta(o2, r2, n2, "cyclic" === a2.status, e10, i2, t2), to(o2)), null;
          default:
            throw a2.reason;
        }
      }
      function tu(e10, t2) {
        return new Map(t2);
      }
      function tl(e10, t2) {
        return new Set(t2);
      }
      function tc(e10, t2) {
        return t2[Symbol.iterator]();
      }
      function td(e10, t2) {
        return t2;
      }
      function tf(e10, t2, r2, n2, i2, a2) {
        return t2 = parseInt(t2.slice(2), 16), t2 = e10._formData.get(e10._prefix + t2), t2 = r2 === ArrayBuffer ? t2.arrayBuffer() : t2.arrayBuffer().then(function(e11) {
          return new r2(e11);
        }), n2 = te, t2.then(ta(n2, i2, a2, false, e10, td, []), to(n2)), null;
      }
      function tp(e10, t2, r2, n2) {
        var i2 = e10._chunks;
        for (r2 = new e2("fulfilled", r2, n2, e10), i2.set(t2, r2), e10 = e10._formData.getAll(e10._prefix + t2), t2 = 0; t2 < e10.length; t2++) "C" === (i2 = e10[t2])[0] ? n2.close("C" === i2 ? '"$undefined"' : i2.slice(1)) : n2.enqueueModel(i2);
      }
      function th(e10, t2, r2) {
        t2 = parseInt(t2.slice(2), 16);
        var n2 = null;
        r2 = new ReadableStream({ type: r2, start: function(e11) {
          n2 = e11;
        } });
        var i2 = null;
        return tp(e10, t2, r2, { enqueueModel: function(t3) {
          if (null === i2) {
            var r3 = new e2("resolved_model", t3, -1, e10);
            tr(r3), "fulfilled" === r3.status ? n2.enqueue(r3.value) : (r3.then(function(e11) {
              return n2.enqueue(e11);
            }, function(e11) {
              return n2.error(e11);
            }), i2 = r3);
          } else {
            r3 = i2;
            var a2 = e4(e10);
            a2.then(function(e11) {
              return n2.enqueue(e11);
            }, function(e11) {
              return n2.error(e11);
            }), i2 = a2, r3.then(function() {
              i2 === a2 && (i2 = null), e9(a2, t3, -1);
            });
          }
        }, close: function() {
          if (null === i2) n2.close();
          else {
            var e11 = i2;
            i2 = null, e11.then(function() {
              return n2.close();
            });
          }
        }, error: function(e11) {
          if (null === i2) n2.error(e11);
          else {
            var t3 = i2;
            i2 = null, t3.then(function() {
              return n2.error(e11);
            });
          }
        } }), r2;
      }
      function t_() {
        return this;
      }
      function ty(e10, t2, r2) {
        t2 = parseInt(t2.slice(2), 16);
        var n2 = [], i2 = false, a2 = 0, o2 = {};
        return o2[g] = function() {
          var t3, r3 = 0;
          return (t3 = { next: t3 = function(t4) {
            if (void 0 !== t4) throw Error("Values cannot be passed to next() of AsyncIterables passed to Client Components.");
            if (r3 === n2.length) {
              if (i2) return new e2("fulfilled", { done: true, value: void 0 }, null, e10);
              n2[r3] = e4(e10);
            }
            return n2[r3++];
          } })[g] = t_, t3;
        }, tp(e10, t2, r2 = r2 ? o2[g]() : o2, { enqueueModel: function(t3) {
          a2 === n2.length ? n2[a2] = e8(e10, t3, false) : e7(n2[a2], t3, false), a2++;
        }, close: function(t3) {
          for (i2 = true, a2 === n2.length ? n2[a2] = e8(e10, t3, true) : e7(n2[a2], t3, true), a2++; a2 < n2.length; ) e7(n2[a2++], '"$undefined"', true);
        }, error: function(t3) {
          for (i2 = true, a2 === n2.length && (n2[a2] = e4(e10)); a2 < n2.length; ) e6(n2[a2++], t3);
        } }), r2;
      }
      function tg(e10, t2, r2) {
        var n2 = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : new FormData();
        return { _bundlerConfig: e10, _prefix: t2, _formData: n2, _chunks: /* @__PURE__ */ new Map(), _closed: false, _closedReason: null, _temporaryReferences: r2 };
      }
      function tm(e10) {
        tn(e10, Error("Connection closed."));
      }
      function tv(e10, t2, r2) {
        var n2 = eY(e10, t2);
        return e10 = e0(n2), r2 ? Promise.all([r2, e10]).then(function(e11) {
          e11 = e11[0];
          var t3 = e1(n2);
          return t3.bind.apply(t3, [null].concat(e11));
        }) : e10 ? Promise.resolve(e10).then(function() {
          return e1(n2);
        }) : Promise.resolve(e1(n2));
      }
      function tb(e10, t2, r2) {
        if (tm(e10 = tg(t2, r2, void 0, e10)), (e10 = ti(e10, 0)).then(function() {
        }), "fulfilled" !== e10.status) throw e10.reason;
        return e10.value;
      }
      t.createClientModuleProxy = function(e10) {
        return new Proxy(e10 = P({}, e10, false), $);
      }, t.createTemporaryReferenceSet = function() {
        return /* @__PURE__ */ new WeakMap();
      }, t.decodeAction = function(e10, t2) {
        var r2 = new FormData(), n2 = null;
        return e10.forEach(function(i2, a2) {
          a2.startsWith("$ACTION_") ? a2.startsWith("$ACTION_REF_") ? (i2 = tb(e10, t2, i2 = "$ACTION_" + a2.slice(12) + ":"), n2 = tv(t2, i2.id, i2.bound)) : a2.startsWith("$ACTION_ID_") && (n2 = tv(t2, i2 = a2.slice(11), null)) : r2.append(a2, i2);
        }), null === n2 ? null : n2.then(function(e11) {
          return e11.bind(null, r2);
        });
      }, t.decodeFormState = function(e10, t2, r2) {
        var n2 = t2.get("$ACTION_KEY");
        if ("string" != typeof n2) return Promise.resolve(null);
        var i2 = null;
        if (t2.forEach(function(e11, n3) {
          n3.startsWith("$ACTION_REF_") && (i2 = tb(t2, r2, "$ACTION_" + n3.slice(12) + ":"));
        }), null === i2) return Promise.resolve(null);
        var a2 = i2.id;
        return Promise.resolve(i2.bound).then(function(t3) {
          return null === t3 ? null : [e10, n2, a2, t3.length - 1];
        });
      }, t.decodeReply = function(e10, t2, r2) {
        if ("string" == typeof e10) {
          var n2 = new FormData();
          n2.append("0", e10), e10 = n2;
        }
        return t2 = ti(e10 = tg(t2, "", r2 ? r2.temporaryReferences : void 0, e10), 0), tm(e10), t2;
      }, t.decodeReplyFromAsyncIterable = function(e10, t2, r2) {
        function n2(e11) {
          tn(a2, e11), "function" == typeof i2.throw && i2.throw(e11).then(n2, n2);
        }
        var i2 = e10[g](), a2 = tg(t2, "", r2 ? r2.temporaryReferences : void 0);
        return i2.next().then(function e11(t3) {
          if (t3.done) tm(a2);
          else {
            var r3 = (t3 = t3.value)[0];
            if ("string" == typeof (t3 = t3[1])) {
              a2._formData.append(r3, t3);
              var o2 = a2._prefix;
              if (r3.startsWith(o2)) {
                var s2 = a2._chunks;
                r3 = +r3.slice(o2.length), (s2 = s2.get(r3)) && e9(s2, t3, r3);
              }
            } else a2._formData.append(r3, t3);
            i2.next().then(e11, n2);
          }
        }, n2), ti(a2, 0);
      }, t.registerClientReference = function(e10, t2, r2) {
        return P(e10, t2 + "#" + r2, false);
      }, t.registerServerReference = function(e10, t2, r2) {
        return Object.defineProperties(e10, { $$typeof: { value: O }, $$id: { value: null === r2 ? t2 : t2 + "#" + r2, configurable: true }, $$bound: { value: null, configurable: true }, bind: { value: L, configurable: true } });
      };
      let tw = "function" == typeof globalThis.setImmediate && globalThis.propertyIsEnumerable("setImmediate") ? globalThis.setImmediate : setTimeout;
      t.renderToReadableStream = function(e10, t2, r2) {
        var n2 = new e_(20, e10, t2, r2 ? r2.onError : void 0, r2 ? r2.identifierPrefix : void 0, r2 ? r2.onPostpone : void 0, r2 ? r2.temporaryReferences : void 0, void 0, void 0, ey, ey);
        if (r2 && r2.signal) {
          var i2 = r2.signal;
          if (i2.aborted) eX(n2, i2.reason);
          else {
            var a2 = function() {
              eX(n2, i2.reason), i2.removeEventListener("abort", a2);
            };
            i2.addEventListener("abort", a2);
          }
        }
        return new ReadableStream({ type: "bytes", start: function() {
          eW(n2);
        }, pull: function(e11) {
          eK(n2, e11);
        }, cancel: function(e11) {
          n2.destination = null, eX(n2, e11);
        } }, { highWaterMark: 0 });
      }, t.unstable_prerender = function(e10, t2, r2) {
        return new Promise(function(n2, i2) {
          var a2 = new e_(21, e10, t2, r2 ? r2.onError : void 0, r2 ? r2.identifierPrefix : void 0, r2 ? r2.onPostpone : void 0, r2 ? r2.temporaryReferences : void 0, void 0, void 0, function() {
            n2({ prelude: new ReadableStream({ type: "bytes", start: function() {
              eW(a2);
            }, pull: function(e11) {
              eK(a2, e11);
            }, cancel: function(e11) {
              a2.destination = null, eX(a2, e11);
            } }, { highWaterMark: 0 }) });
          }, i2);
          if (r2 && r2.signal) {
            var o2 = r2.signal;
            if (o2.aborted) eX(a2, o2.reason);
            else {
              var s2 = function() {
                eX(a2, o2.reason), o2.removeEventListener("abort", s2);
              };
              o2.addEventListener("abort", s2);
            }
          }
          eW(a2);
        });
      };
    }, 673: (e) => {
      "use strict";
      e.exports = JSON.parse(`{"user":{"sign_in":"Sign In","sign_out":"Sign Out","credits":"Credits","api_keys":"API Keys","my_orders":"My Orders","user_center":"User Center","admin_system":"Admin System"},"language_switch":{"title":"Switch Language?","description":"We detected that you might prefer to use {suggestedLanguage}. Would you like to switch to {suggestedLanguage}?","switch_button_en":"Switch to {suggestedLanguage}","switch_button_zh":"\u5207\u6362\u5230{suggestedLanguage}","cancel_button_en":"Keep {currentLanguage}","cancel_button_zh":"\u4FDD\u6301{currentLanguage}"},"sign_modal":{"sign_in_title":"Sign In","sign_in_description":"Sign in to your account","sign_up_title":"Sign Up","sign_up_description":"Create an account","email_title":"Email","email_placeholder":"Input your email here","password_title":"Password","password_placeholder":"Input your password here","forgot_password":"Forgot password?","or":"Or","continue":"Continue","no_account":"Don't have an account?","email_sign_in":"Sign in with Email","google_sign_in":"Sign in with Google","google_signing_in":"Redirecting to Google...","github_sign_in":"Sign in with GitHub","github_signing_in":"Redirecting to GitHub...","close_title":"Close","cancel_title":"Cancel"},"my_orders":{"title":"My Orders","description":"orders paid","no_orders":"No orders found","tip":"","activate_order":"Activate Order","actived":"Activated","join_discord":"Join Discord","read_docs":"Read Docs","table":{"order_no":"Order No","email":"Email","product_name":"Product Name","amount":"Amount","paid_at":"Paid At","github_username":"GitHub Username","status":"Status"}},"my_credits":{"title":"My Credits","left_tip":"left credits: {left_credits}","no_credits":"No credits records","recharge":"Recharge","table":{"trans_no":"Trans No","trans_type":"Trans Type","credits":"Credits","updated_at":"Updated At","status":"Status"}},"api_keys":{"title":"API Keys","tip":"Please keep your apikey safe to avoid leaks","no_api_keys":"No API Keys","create_api_key":"Create API Key","table":{"name":"Name","key":"Key","created_at":"Created At"},"form":{"name":"Name","name_placeholder":"API Key Name","submit":"Submit"}},"blog":{"title":"Blog","description":"News, resources, and updates about us","read_more_text":"Read More"},"my_invites":{"title":"My Invites","description":"View your invite records","no_invites":"No invite records found","my_invite_link":"My Invite Link","edit_invite_link":"Edit Invite Link","copy_invite_link":"Copy Invite Link","invite_code":"Invite Code","invite_tip":"Invite 1 friend to order, reward $50.","invite_balance":"Invite Reward Balance","total_invite_count":"Total Invite Count","total_paid_count":"Total Paid Count","total_award_amount":"Total Award Amount","update_invite_code":"Set Invite Code","update_invite_code_tip":"Input your custom invite code","update_invite_button":"Save","no_orders":"You can't invite others before you order","no_affiliates":"You're not allowed to invite others, please contact us to apply for permission.","table":{"invite_time":"Invite Time","invite_user":"Invite User","status":"Status","reward_percent":"Reward Percent","reward_amount":"Reward Amount","pending":"Pending","completed":"Completed"}},"feedback":{"title":"Feedback","description":"We'd love to hear what went well or how we can improve the product experience.","submit":"Submit","loading":"Submitting...","contact_tip":"Other ways to contact us","rating_tip":"How do you feel about our product?","placeholder":"Leave your words here..."}}`);
    }, 678: (e, t, r) => {
      "use strict";
      r.d(t, { X: () => function e2(t2) {
        if ((0, a.p)(t2) || "object" == typeof t2 && null !== t2 && "digest" in t2 && "BAILOUT_TO_CLIENT_SIDE_RENDERING" === t2.digest || (0, s.h)(t2) || (0, o.I3)(t2) || "object" == typeof t2 && null !== t2 && t2.$$typeof === i || (0, n.T)(t2)) throw t2;
        t2 instanceof Error && "cause" in t2 && e2(t2.cause);
      } });
      var n = r(911);
      let i = Symbol.for("react.postpone");
      var a = r(5), o = r(223), s = r(654);
    }, 684: (e, t, r) => {
      var n = { "./en.json": 673, "./zh.json": 938 };
      function i(e2) {
        return Promise.resolve().then(() => {
          if (!r.o(n, e2)) {
            var t2 = Error("Cannot find module '" + e2 + "'");
            throw t2.code = "MODULE_NOT_FOUND", t2;
          }
          var i2 = n[e2];
          return r.t(i2, 19);
        });
      }
      i.keys = () => Object.keys(n), i.id = 684, e.exports = i;
    }, 753: (e, t) => {
      "use strict";
      var r = { H: null, A: null };
      function n(e2) {
        var t2 = "https://react.dev/errors/" + e2;
        if (1 < arguments.length) {
          t2 += "?args[]=" + encodeURIComponent(arguments[1]);
          for (var r2 = 2; r2 < arguments.length; r2++) t2 += "&args[]=" + encodeURIComponent(arguments[r2]);
        }
        return "Minified React error #" + e2 + "; visit " + t2 + " for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";
      }
      var i = Array.isArray, a = Symbol.for("react.transitional.element"), o = Symbol.for("react.portal"), s = Symbol.for("react.fragment"), u = Symbol.for("react.strict_mode"), l = Symbol.for("react.profiler"), c = Symbol.for("react.forward_ref"), d = Symbol.for("react.suspense"), f = Symbol.for("react.memo"), p = Symbol.for("react.lazy"), h = Symbol.iterator, _ = Object.prototype.hasOwnProperty, y = Object.assign;
      function g(e2, t2, r2, n2, i2, o2) {
        return { $$typeof: a, type: e2, key: t2, ref: void 0 !== (r2 = o2.ref) ? r2 : null, props: o2 };
      }
      function m(e2) {
        return "object" == typeof e2 && null !== e2 && e2.$$typeof === a;
      }
      var v = /\/+/g;
      function b(e2, t2) {
        var r2, n2;
        return "object" == typeof e2 && null !== e2 && null != e2.key ? (r2 = "" + e2.key, n2 = { "=": "=0", ":": "=2" }, "$" + r2.replace(/[=:]/g, function(e3) {
          return n2[e3];
        })) : t2.toString(36);
      }
      function w() {
      }
      function S(e2, t2, r2) {
        if (null == e2) return e2;
        var s2 = [], u2 = 0;
        return !function e3(t3, r3, s3, u3, l2) {
          var c2, d2, f2, _2 = typeof t3;
          ("undefined" === _2 || "boolean" === _2) && (t3 = null);
          var y2 = false;
          if (null === t3) y2 = true;
          else switch (_2) {
            case "bigint":
            case "string":
            case "number":
              y2 = true;
              break;
            case "object":
              switch (t3.$$typeof) {
                case a:
                case o:
                  y2 = true;
                  break;
                case p:
                  return e3((y2 = t3._init)(t3._payload), r3, s3, u3, l2);
              }
          }
          if (y2) return l2 = l2(t3), y2 = "" === u3 ? "." + b(t3, 0) : u3, i(l2) ? (s3 = "", null != y2 && (s3 = y2.replace(v, "$&/") + "/"), e3(l2, r3, s3, "", function(e4) {
            return e4;
          })) : null != l2 && (m(l2) && (c2 = l2, d2 = s3 + (null == l2.key || t3 && t3.key === l2.key ? "" : ("" + l2.key).replace(v, "$&/") + "/") + y2, l2 = g(c2.type, d2, void 0, void 0, void 0, c2.props)), r3.push(l2)), 1;
          y2 = 0;
          var S2 = "" === u3 ? "." : u3 + ":";
          if (i(t3)) for (var E2 = 0; E2 < t3.length; E2++) _2 = S2 + b(u3 = t3[E2], E2), y2 += e3(u3, r3, s3, _2, l2);
          else if ("function" == typeof (E2 = null === (f2 = t3) || "object" != typeof f2 ? null : "function" == typeof (f2 = h && f2[h] || f2["@@iterator"]) ? f2 : null)) for (t3 = E2.call(t3), E2 = 0; !(u3 = t3.next()).done; ) _2 = S2 + b(u3 = u3.value, E2++), y2 += e3(u3, r3, s3, _2, l2);
          else if ("object" === _2) {
            if ("function" == typeof t3.then) return e3(function(e4) {
              switch (e4.status) {
                case "fulfilled":
                  return e4.value;
                case "rejected":
                  throw e4.reason;
                default:
                  switch ("string" == typeof e4.status ? e4.then(w, w) : (e4.status = "pending", e4.then(function(t4) {
                    "pending" === e4.status && (e4.status = "fulfilled", e4.value = t4);
                  }, function(t4) {
                    "pending" === e4.status && (e4.status = "rejected", e4.reason = t4);
                  })), e4.status) {
                    case "fulfilled":
                      return e4.value;
                    case "rejected":
                      throw e4.reason;
                  }
              }
              throw e4;
            }(t3), r3, s3, u3, l2);
            throw Error(n(31, "[object Object]" === (r3 = String(t3)) ? "object with keys {" + Object.keys(t3).join(", ") + "}" : r3));
          }
          return y2;
        }(e2, s2, "", "", function(e3) {
          return t2.call(r2, e3, u2++);
        }), s2;
      }
      function E(e2) {
        if (-1 === e2._status) {
          var t2 = e2._result;
          (t2 = t2()).then(function(t3) {
            (0 === e2._status || -1 === e2._status) && (e2._status = 1, e2._result = t3);
          }, function(t3) {
            (0 === e2._status || -1 === e2._status) && (e2._status = 2, e2._result = t3);
          }), -1 === e2._status && (e2._status = 0, e2._result = t2);
        }
        if (1 === e2._status) return e2._result.default;
        throw e2._result;
      }
      function C() {
        return /* @__PURE__ */ new WeakMap();
      }
      function R() {
        return { s: 0, v: void 0, o: null, p: null };
      }
      t.Children = { map: S, forEach: function(e2, t2, r2) {
        S(e2, function() {
          t2.apply(this, arguments);
        }, r2);
      }, count: function(e2) {
        var t2 = 0;
        return S(e2, function() {
          t2++;
        }), t2;
      }, toArray: function(e2) {
        return S(e2, function(e3) {
          return e3;
        }) || [];
      }, only: function(e2) {
        if (!m(e2)) throw Error(n(143));
        return e2;
      } }, t.Fragment = s, t.Profiler = l, t.StrictMode = u, t.Suspense = d, t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = r, t.cache = function(e2) {
        return function() {
          var t2 = r.A;
          if (!t2) return e2.apply(null, arguments);
          var n2 = t2.getCacheForType(C);
          void 0 === (t2 = n2.get(e2)) && (t2 = R(), n2.set(e2, t2)), n2 = 0;
          for (var i2 = arguments.length; n2 < i2; n2++) {
            var a2 = arguments[n2];
            if ("function" == typeof a2 || "object" == typeof a2 && null !== a2) {
              var o2 = t2.o;
              null === o2 && (t2.o = o2 = /* @__PURE__ */ new WeakMap()), void 0 === (t2 = o2.get(a2)) && (t2 = R(), o2.set(a2, t2));
            } else null === (o2 = t2.p) && (t2.p = o2 = /* @__PURE__ */ new Map()), void 0 === (t2 = o2.get(a2)) && (t2 = R(), o2.set(a2, t2));
          }
          if (1 === t2.s) return t2.v;
          if (2 === t2.s) throw t2.v;
          try {
            var s2 = e2.apply(null, arguments);
            return (n2 = t2).s = 1, n2.v = s2;
          } catch (e3) {
            throw (s2 = t2).s = 2, s2.v = e3, e3;
          }
        };
      }, t.captureOwnerStack = function() {
        return null;
      }, t.cloneElement = function(e2, t2, r2) {
        if (null == e2) throw Error(n(267, e2));
        var i2 = y({}, e2.props), a2 = e2.key, o2 = void 0;
        if (null != t2) for (s2 in void 0 !== t2.ref && (o2 = void 0), void 0 !== t2.key && (a2 = "" + t2.key), t2) _.call(t2, s2) && "key" !== s2 && "__self" !== s2 && "__source" !== s2 && ("ref" !== s2 || void 0 !== t2.ref) && (i2[s2] = t2[s2]);
        var s2 = arguments.length - 2;
        if (1 === s2) i2.children = r2;
        else if (1 < s2) {
          for (var u2 = Array(s2), l2 = 0; l2 < s2; l2++) u2[l2] = arguments[l2 + 2];
          i2.children = u2;
        }
        return g(e2.type, a2, void 0, void 0, o2, i2);
      }, t.createElement = function(e2, t2, r2) {
        var n2, i2 = {}, a2 = null;
        if (null != t2) for (n2 in void 0 !== t2.key && (a2 = "" + t2.key), t2) _.call(t2, n2) && "key" !== n2 && "__self" !== n2 && "__source" !== n2 && (i2[n2] = t2[n2]);
        var o2 = arguments.length - 2;
        if (1 === o2) i2.children = r2;
        else if (1 < o2) {
          for (var s2 = Array(o2), u2 = 0; u2 < o2; u2++) s2[u2] = arguments[u2 + 2];
          i2.children = s2;
        }
        if (e2 && e2.defaultProps) for (n2 in o2 = e2.defaultProps) void 0 === i2[n2] && (i2[n2] = o2[n2]);
        return g(e2, a2, void 0, void 0, null, i2);
      }, t.createRef = function() {
        return { current: null };
      }, t.forwardRef = function(e2) {
        return { $$typeof: c, render: e2 };
      }, t.isValidElement = m, t.lazy = function(e2) {
        return { $$typeof: p, _payload: { _status: -1, _result: e2 }, _init: E };
      }, t.memo = function(e2, t2) {
        return { $$typeof: f, type: e2, compare: void 0 === t2 ? null : t2 };
      }, t.use = function(e2) {
        return r.H.use(e2);
      }, t.useCallback = function(e2, t2) {
        return r.H.useCallback(e2, t2);
      }, t.useDebugValue = function() {
      }, t.useId = function() {
        return r.H.useId();
      }, t.useMemo = function(e2, t2) {
        return r.H.useMemo(e2, t2);
      }, t.version = "19.2.0-canary-3fbfb9ba-20250409";
    }, 764: (e) => {
      (() => {
        "use strict";
        "undefined" != typeof __nccwpck_require__ && (__nccwpck_require__.ab = "//");
        var t = {};
        (() => {
          t.parse = function(t2, r2) {
            if ("string" != typeof t2) throw TypeError("argument str must be a string");
            for (var i2 = {}, a = t2.split(n), o = (r2 || {}).decode || e2, s = 0; s < a.length; s++) {
              var u = a[s], l = u.indexOf("=");
              if (!(l < 0)) {
                var c = u.substr(0, l).trim(), d = u.substr(++l, u.length).trim();
                '"' == d[0] && (d = d.slice(1, -1)), void 0 == i2[c] && (i2[c] = function(e3, t3) {
                  try {
                    return t3(e3);
                  } catch (t4) {
                    return e3;
                  }
                }(d, o));
              }
            }
            return i2;
          }, t.serialize = function(e3, t2, n2) {
            var a = n2 || {}, o = a.encode || r;
            if ("function" != typeof o) throw TypeError("option encode is invalid");
            if (!i.test(e3)) throw TypeError("argument name is invalid");
            var s = o(t2);
            if (s && !i.test(s)) throw TypeError("argument val is invalid");
            var u = e3 + "=" + s;
            if (null != a.maxAge) {
              var l = a.maxAge - 0;
              if (isNaN(l) || !isFinite(l)) throw TypeError("option maxAge is invalid");
              u += "; Max-Age=" + Math.floor(l);
            }
            if (a.domain) {
              if (!i.test(a.domain)) throw TypeError("option domain is invalid");
              u += "; Domain=" + a.domain;
            }
            if (a.path) {
              if (!i.test(a.path)) throw TypeError("option path is invalid");
              u += "; Path=" + a.path;
            }
            if (a.expires) {
              if ("function" != typeof a.expires.toUTCString) throw TypeError("option expires is invalid");
              u += "; Expires=" + a.expires.toUTCString();
            }
            if (a.httpOnly && (u += "; HttpOnly"), a.secure && (u += "; Secure"), a.sameSite) switch ("string" == typeof a.sameSite ? a.sameSite.toLowerCase() : a.sameSite) {
              case true:
              case "strict":
                u += "; SameSite=Strict";
                break;
              case "lax":
                u += "; SameSite=Lax";
                break;
              case "none":
                u += "; SameSite=None";
                break;
              default:
                throw TypeError("option sameSite is invalid");
            }
            return u;
          };
          var e2 = decodeURIComponent, r = encodeURIComponent, n = /; */, i = /^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;
        })(), e.exports = t;
      })();
    }, 794: (e, t, r) => {
      "use strict";
      e.exports = r(230);
    }, 813: (e) => {
      "use strict";
      e.exports = n, e.exports.preferredEncodings = n;
      var t = /^\s*([^\s;]+)\s*(?:;(.*))?$/;
      function r(e2, t2, r2) {
        var n2 = 0;
        if (t2.encoding.toLowerCase() === e2.toLowerCase()) n2 |= 1;
        else if ("*" !== t2.encoding) return null;
        return { encoding: e2, i: r2, o: t2.i, q: t2.q, s: n2 };
      }
      function n(e2, n2, s) {
        var u = function(e3) {
          for (var n3 = e3.split(","), i2 = false, a2 = 1, o2 = 0, s2 = 0; o2 < n3.length; o2++) {
            var u2 = function(e4, r2) {
              var n4 = t.exec(e4);
              if (!n4) return null;
              var i3 = n4[1], a3 = 1;
              if (n4[2]) for (var o3 = n4[2].split(";"), s3 = 0; s3 < o3.length; s3++) {
                var u3 = o3[s3].trim().split("=");
                if ("q" === u3[0]) {
                  a3 = parseFloat(u3[1]);
                  break;
                }
              }
              return { encoding: i3, q: a3, i: r2 };
            }(n3[o2].trim(), o2);
            u2 && (n3[s2++] = u2, i2 = i2 || r("identity", u2), a2 = Math.min(a2, u2.q || 1));
          }
          return i2 || (n3[s2++] = { encoding: "identity", q: a2, i: o2 }), n3.length = s2, n3;
        }(e2 || ""), l = s ? function(e3, t2) {
          if (e3.q !== t2.q) return t2.q - e3.q;
          var r2 = s.indexOf(e3.encoding), n3 = s.indexOf(t2.encoding);
          return -1 === r2 && -1 === n3 ? t2.s - e3.s || e3.o - t2.o || e3.i - t2.i : -1 !== r2 && -1 !== n3 ? r2 - n3 : -1 === r2 ? 1 : -1;
        } : i;
        if (!n2) return u.filter(o).sort(l).map(a);
        var c = n2.map(function(e3, t2) {
          for (var n3 = { encoding: e3, o: -1, q: 0, s: 0 }, i2 = 0; i2 < u.length; i2++) {
            var a2 = r(e3, u[i2], t2);
            a2 && 0 > (n3.s - a2.s || n3.q - a2.q || n3.o - a2.o) && (n3 = a2);
          }
          return n3;
        });
        return c.filter(o).sort(l).map(function(e3) {
          return n2[c.indexOf(e3)];
        });
      }
      function i(e2, t2) {
        return t2.q - e2.q || t2.s - e2.s || e2.o - t2.o || e2.i - t2.i;
      }
      function a(e2) {
        return e2.encoding;
      }
      function o(e2) {
        return e2.q > 0;
      }
    }, 911: (e, t, r) => {
      "use strict";
      function n(e2) {
        return "object" == typeof e2 && null !== e2 && "digest" in e2 && e2.digest === i;
      }
      r.d(t, { T: () => n, W: () => s });
      let i = "HANGING_PROMISE_REJECTION";
      class a extends Error {
        constructor(e2) {
          super(`During prerendering, ${e2} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e2} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`), this.expression = e2, this.digest = i;
        }
      }
      let o = /* @__PURE__ */ new WeakMap();
      function s(e2, t2) {
        if (e2.aborted) return Promise.reject(new a(t2));
        {
          let r2 = new Promise((r3, n2) => {
            let i2 = n2.bind(null, new a(t2)), s2 = o.get(e2);
            if (s2) s2.push(i2);
            else {
              let t3 = [i2];
              o.set(e2, t3), e2.addEventListener("abort", () => {
                for (let e3 = 0; e3 < t3.length; e3++) t3[e3]();
              }, { once: true });
            }
          });
          return r2.catch(u), r2;
        }
      }
      function u() {
      }
    }, 938: (e) => {
      "use strict";
      e.exports = JSON.parse('{"metadata":{"title":"AI\u4E00\u7AD9\u5F0F\u5DE5\u5177\u5E73\u53F0 | AI\u5DE5\u5177\u7BB1 - \u5BF9\u8BDD\u3001\u56FE\u7247\u3001\u89C6\u9891\u3001\u8BED\u97F3AI\u5DE5\u5177","description":"\u96C6\u6210\u591A\u79CD\u9876\u7EA7AI\u6A21\u578B\u7684\u4E00\u7AD9\u5F0F\u5E73\u53F0\uFF0C\u63D0\u4F9BAI\u5BF9\u8BDD\u3001\u56FE\u7247\u751F\u6210\u3001\u89C6\u9891\u521B\u4F5C\u3001\u8BED\u97F3\u5408\u6210\u7B49\u529F\u80FD\uFF0C\u6EE1\u8DB3\u6240\u6709\u521B\u4F5C\u9700\u6C42\u3002","keywords":"AI\u5DE5\u5177, AI\u5BF9\u8BDD, \u56FE\u7247\u751F\u6210, \u89C6\u9891\u521B\u4F5C, \u8BED\u97F3\u5408\u6210, AI\u5E73\u53F0"},"user":{"sign_in":"\u767B\u5F55","sign_out":"\u9000\u51FA\u767B\u5F55","credits":"\u989D\u5EA6","api_keys":"API \u5BC6\u94A5","my_orders":"\u6211\u7684\u8BA2\u5355","user_center":"\u7528\u6237\u4E2D\u5FC3","admin_system":"\u7BA1\u7406\u540E\u53F0"},"language_switch":{"title":"\u5207\u6362\u8BED\u8A00\uFF1F","description":"\u6211\u4EEC\u68C0\u6D4B\u5230\u60A8\u53EF\u80FD\u5E0C\u671B\u4F7F\u7528{suggestedLanguage}\u8BED\u8A00\u3002\u60A8\u662F\u5426\u8981\u5207\u6362\u5230{suggestedLanguage}\uFF1F","switch_button_en":"Switch to {suggestedLanguage}","switch_button_zh":"\u5207\u6362\u5230{suggestedLanguage}","cancel_button_en":"Keep {currentLanguage}","cancel_button_zh":"\u4FDD\u6301{currentLanguage}"},"sign_modal":{"sign_in_title":"\u767B\u5F55","sign_in_description":"\u767B\u5F55\u60A8\u7684\u8D26\u6237","sign_up_title":"\u6CE8\u518C","sign_up_description":"\u521B\u5EFA\u65B0\u8D26\u6237","email_title":"\u90AE\u7BB1","email_placeholder":"\u8BF7\u8F93\u5165\u60A8\u7684\u90AE\u7BB1","password_title":"\u5BC6\u7801","password_placeholder":"\u8BF7\u8F93\u5165\u60A8\u7684\u5BC6\u7801","forgot_password":"\u5FD8\u8BB0\u5BC6\u7801\uFF1F","or":"\u6216","continue":"\u7EE7\u7EED","no_account":"\u8FD8\u6CA1\u6709\u8D26\u6237\uFF1F","email_sign_in":"\u4F7F\u7528\u90AE\u7BB1\u767B\u5F55","google_sign_in":"\u4F7F\u7528 Google \u767B\u5F55","google_signing_in":"\u6B63\u5728\u8DF3\u8F6C\u5230 Google...","github_sign_in":"\u4F7F\u7528 GitHub \u767B\u5F55","github_signing_in":"\u6B63\u5728\u8DF3\u8F6C\u5230 GitHub...","close_title":"\u5173\u95ED","cancel_title":"\u53D6\u6D88"},"my_orders":{"title":"\u6211\u7684\u8BA2\u5355","description":"\u5DF2\u8D2D\u4E70\u7684\u8BA2\u5355\u3002","no_orders":"\u672A\u627E\u5230\u8BA2\u5355","tip":"","activate_order":"\u6FC0\u6D3B\u8BA2\u5355","actived":"\u5DF2\u6FC0\u6D3B","join_discord":"\u52A0\u5165 Discord","read_docs":"\u9605\u8BFB\u6587\u6863","table":{"order_no":"\u8BA2\u5355\u53F7","email":"\u90AE\u7BB1","product_name":"\u4EA7\u54C1\u540D\u79F0","amount":"\u91D1\u989D","paid_at":"\u652F\u4ED8\u65F6\u95F4","github_username":"GitHub \u7528\u6237\u540D","status":"\u72B6\u6001"}},"my_credits":{"title":"\u6211\u7684\u79EF\u5206","left_tip":"\u5269\u4F59\u79EF\u5206: {left_credits}","no_credits":"\u6CA1\u6709\u79EF\u5206\u8BB0\u5F55","recharge":"\u5145\u503C","table":{"trans_no":"\u4EA4\u6613\u53F7","trans_type":"\u4EA4\u6613\u7C7B\u578B","credits":"\u79EF\u5206","updated_at":"\u66F4\u65B0\u65F6\u95F4","status":"\u72B6\u6001"}},"api_keys":{"title":"API \u5BC6\u94A5","tip":"\u8BF7\u59A5\u5584\u4FDD\u7BA1\u60A8\u7684 API \u5BC6\u94A5\uFF0C\u907F\u514D\u6CC4\u9732","no_api_keys":"\u6CA1\u6709 API \u5BC6\u94A5","create_api_key":"\u521B\u5EFA API \u5BC6\u94A5","table":{"name":"\u540D\u79F0","key":"\u5BC6\u94A5","created_at":"\u521B\u5EFA\u65F6\u95F4"},"form":{"name":"\u540D\u79F0","name_placeholder":"API \u5BC6\u94A5\u540D\u79F0","submit":"\u63D0\u4EA4"}},"blog":{"title":"\u535A\u5BA2","description":"\u65B0\u95FB\u3001\u8D44\u6E90\u548C\u66F4\u65B0","read_more_text":"\u9605\u8BFB\u66F4\u591A"},"my_invites":{"title":"\u6211\u7684\u9080\u8BF7","description":"\u67E5\u770B\u60A8\u7684\u9080\u8BF7\u8BB0\u5F55","no_invites":"\u672A\u627E\u5230\u9080\u8BF7\u8BB0\u5F55","my_invite_link":"\u6211\u7684\u9080\u8BF7\u94FE\u63A5","edit_invite_link":"\u7F16\u8F91\u9080\u8BF7\u94FE\u63A5","copy_invite_link":"\u590D\u5236\u9080\u8BF7\u94FE\u63A5","invite_code":"\u9080\u8BF7\u7801","invite_tip":"\u6BCF\u9080\u8BF7 1 \u4F4D\u670B\u53CB\u8D2D\u4E70\uFF0C\u5956\u52B1 $50\u3002","invite_balance":"\u9080\u8BF7\u5956\u52B1\u4F59\u989D","total_invite_count":"\u603B\u9080\u8BF7\u4EBA\u6570","total_paid_count":"\u5DF2\u5145\u503C\u4EBA\u6570","total_award_amount":"\u603B\u5956\u52B1\u91D1\u989D","update_invite_code":"\u8BBE\u7F6E\u9080\u8BF7\u7801","update_invite_code_tip":"\u8F93\u5165\u4F60\u7684\u81EA\u5B9A\u4E49\u9080\u8BF7\u7801","update_invite_button":"\u4FDD\u5B58","no_orders":"\u4F60\u9700\u8981\u5148\u4E0B\u5355\u624D\u80FD\u9080\u8BF7\u670B\u53CB","no_affiliates":"\u4F60\u6682\u65E0\u9080\u8BF7\u670B\u53CB\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u6211\u4EEC\u7533\u8BF7\u5F00\u901A\u3002","table":{"invite_time":"\u9080\u8BF7\u65F6\u95F4","invite_user":"\u9080\u8BF7\u7528\u6237","status":"\u72B6\u6001","reward_percent":"\u5956\u52B1\u6BD4\u4F8B","reward_amount":"\u5956\u52B1\u91D1\u989D","pending":"\u5DF2\u6CE8\u518C\uFF0C\u672A\u652F\u4ED8","completed":"\u5DF2\u652F\u4ED8"}},"feedback":{"title":"\u53CD\u9988","description":"\u6211\u4EEC\u5F88\u4E50\u610F\u542C\u53D6\u60A8\u5BF9\u4EA7\u54C1\u7684\u770B\u6CD5\u6216\u5982\u4F55\u6539\u8FDB\u4EA7\u54C1\u4F53\u9A8C\u3002","submit":"\u63D0\u4EA4","loading":"\u63D0\u4EA4\u4E2D...","contact_tip":"\u5176\u4ED6\u8054\u7CFB\u65B9\u5F0F","rating_tip":"\u60A8\u5BF9\u6211\u4EEC\u7684\u770B\u6CD5\u5982\u4F55\uFF1F","placeholder":"\u5728\u8FD9\u91CC\u7559\u4E0B\u60A8\u7684\u53CD\u9988..."}}');
    }, 962: (e, t, r) => {
      var n = { "./en.json": 51, "./zh.json": 580 };
      function i(e2) {
        return Promise.resolve().then(() => {
          if (!r.o(n, e2)) {
            var t2 = Error("Cannot find module '" + e2 + "'");
            throw t2.code = "MODULE_NOT_FOUND", t2;
          }
          var i2 = n[e2];
          return r.t(i2, 19);
        });
      }
      i.keys = () => Object.keys(n), i.id = 962, e.exports = i;
    }, 988: (e, t, r) => {
      "use strict";
      var n = r(144), i = r(813), a = r(224), o = r(304);
      function s(e2) {
        if (!(this instanceof s)) return new s(e2);
        this.request = e2;
      }
      e.exports = s, e.exports.Negotiator = s, s.prototype.charset = function(e2) {
        var t2 = this.charsets(e2);
        return t2 && t2[0];
      }, s.prototype.charsets = function(e2) {
        return n(this.request.headers["accept-charset"], e2);
      }, s.prototype.encoding = function(e2, t2) {
        var r2 = this.encodings(e2, t2);
        return r2 && r2[0];
      }, s.prototype.encodings = function(e2, t2) {
        return i(this.request.headers["accept-encoding"], e2, (t2 || {}).preferred);
      }, s.prototype.language = function(e2) {
        var t2 = this.languages(e2);
        return t2 && t2[0];
      }, s.prototype.languages = function(e2) {
        return a(this.request.headers["accept-language"], e2);
      }, s.prototype.mediaType = function(e2) {
        var t2 = this.mediaTypes(e2);
        return t2 && t2[0];
      }, s.prototype.mediaTypes = function(e2) {
        return o(this.request.headers.accept, e2);
      }, s.prototype.preferredCharset = s.prototype.charset, s.prototype.preferredCharsets = s.prototype.charsets, s.prototype.preferredEncoding = s.prototype.encoding, s.prototype.preferredEncodings = s.prototype.encodings, s.prototype.preferredLanguage = s.prototype.language, s.prototype.preferredLanguages = s.prototype.languages, s.prototype.preferredMediaType = s.prototype.mediaType, s.prototype.preferredMediaTypes = s.prototype.mediaTypes;
    }, 989: (e, t, r) => {
      "use strict";
      r.d(t, { J: () => n });
      let n = (0, r(342).xl)();
    } }, (e) => {
      var t = e(e.s = 129);
      (_ENTRIES = "undefined" == typeof _ENTRIES ? {} : _ENTRIES).middleware_middleware = t;
    }]);
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/edgeFunctionHandler.js
var edgeFunctionHandler_exports = {};
__export(edgeFunctionHandler_exports, {
  default: () => edgeFunctionHandler
});
async function edgeFunctionHandler(request) {
  const path3 = new URL(request.url).pathname;
  const routes = globalThis._ROUTES;
  const correspondingRoute = routes.find((route) => route.regex.some((r) => new RegExp(r).test(path3)));
  if (!correspondingRoute) {
    throw new Error(`No route found for ${request.url}`);
  }
  const entry = await self._ENTRIES[`middleware_${correspondingRoute.name}`];
  const result = await entry.default({
    page: correspondingRoute.page,
    request: {
      ...request,
      page: {
        name: correspondingRoute.name
      }
    }
  });
  globalThis.__openNextAls.getStore()?.pendingPromiseRunner.add(result.waitUntil);
  const response = result.response;
  return response;
}
var init_edgeFunctionHandler = __esm({
  "node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/edgeFunctionHandler.js"() {
    globalThis._ENTRIES = {};
    globalThis.self = globalThis;
    globalThis._ROUTES = [{ "name": "middleware", "page": "/", "regex": ["^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!privacy-policy|terms-of-service|api\\/|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$"] }];
    require_edge_runtime_webpack();
    require_middleware();
  }
});

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/promise.js
init_logger();
var DetachedPromise = class {
  resolve;
  reject;
  promise;
  constructor() {
    let resolve;
    let reject;
    this.promise = new Promise((res, rej) => {
      resolve = res;
      reject = rej;
    });
    this.resolve = resolve;
    this.reject = reject;
  }
};
var DetachedPromiseRunner = class {
  promises = [];
  withResolvers() {
    const detachedPromise = new DetachedPromise();
    this.promises.push(detachedPromise);
    return detachedPromise;
  }
  add(promise) {
    const detachedPromise = new DetachedPromise();
    this.promises.push(detachedPromise);
    promise.then(detachedPromise.resolve, detachedPromise.reject);
  }
  async await() {
    debug(`Awaiting ${this.promises.length} detached promises`);
    const results = await Promise.allSettled(this.promises.map((p) => p.promise));
    const rejectedPromises = results.filter((r) => r.status === "rejected");
    rejectedPromises.forEach((r) => {
      error(r.reason);
    });
  }
};
async function awaitAllDetachedPromise() {
  const store = globalThis.__openNextAls.getStore();
  const promisesToAwait = store?.pendingPromiseRunner.await() ?? Promise.resolve();
  if (store?.waitUntil) {
    store.waitUntil(promisesToAwait);
    return;
  }
  await promisesToAwait;
}
function provideNextAfterProvider() {
  const NEXT_REQUEST_CONTEXT_SYMBOL = Symbol.for("@next/request-context");
  const VERCEL_REQUEST_CONTEXT_SYMBOL = Symbol.for("@vercel/request-context");
  const store = globalThis.__openNextAls.getStore();
  const waitUntil = store?.waitUntil ?? ((promise) => store?.pendingPromiseRunner.add(promise));
  const nextAfterContext = {
    get: () => ({
      waitUntil
    })
  };
  globalThis[NEXT_REQUEST_CONTEXT_SYMBOL] = nextAfterContext;
  if (process.env.EMULATE_VERCEL_REQUEST_CONTEXT) {
    globalThis[VERCEL_REQUEST_CONTEXT_SYMBOL] = nextAfterContext;
  }
}
function runWithOpenNextRequestContext({ isISRRevalidation, waitUntil, requestId = Math.random().toString(36) }, fn) {
  return globalThis.__openNextAls.run({
    requestId,
    pendingPromiseRunner: new DetachedPromiseRunner(),
    isISRRevalidation,
    waitUntil,
    writtenTags: /* @__PURE__ */ new Set()
  }, async () => {
    provideNextAfterProvider();
    let result;
    try {
      result = await fn();
    } finally {
      await awaitAllDetachedPromise();
    }
    return result;
  });
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/adapters/middleware.js
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/createGenericHandler.js
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/resolve.js
async function resolveConverter(converter2) {
  if (typeof converter2 === "function") {
    return converter2();
  }
  const m_1 = await Promise.resolve().then(() => (init_edge(), edge_exports));
  return m_1.default;
}
async function resolveWrapper(wrapper) {
  if (typeof wrapper === "function") {
    return wrapper();
  }
  const m_1 = await Promise.resolve().then(() => (init_cloudflare_edge(), cloudflare_edge_exports));
  return m_1.default;
}
async function resolveOriginResolver(originResolver) {
  if (typeof originResolver === "function") {
    return originResolver();
  }
  const m_1 = await Promise.resolve().then(() => (init_pattern_env(), pattern_env_exports));
  return m_1.default;
}
async function resolveAssetResolver(assetResolver) {
  if (typeof assetResolver === "function") {
    return assetResolver();
  }
  const m_1 = await Promise.resolve().then(() => (init_dummy(), dummy_exports));
  return m_1.default;
}
async function resolveProxyRequest(proxyRequest) {
  if (typeof proxyRequest === "function") {
    return proxyRequest();
  }
  const m_1 = await Promise.resolve().then(() => (init_fetch(), fetch_exports));
  return m_1.default;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/createGenericHandler.js
async function createGenericHandler(handler3) {
  const config = await import("./open-next.config.mjs").then((m) => m.default);
  globalThis.openNextConfig = config;
  const handlerConfig = config[handler3.type];
  const override = handlerConfig && "override" in handlerConfig ? handlerConfig.override : void 0;
  const converter2 = await resolveConverter(override?.converter);
  const { name, wrapper } = await resolveWrapper(override?.wrapper);
  debug("Using wrapper", name);
  return wrapper(handler3.handler, converter2);
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/util.js
import crypto from "node:crypto";
import { Readable as Readable2 } from "node:stream";

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/adapters/config/index.js
init_logger();
import path from "node:path";
globalThis.__dirname ??= "";
var NEXT_DIR = path.join(__dirname, ".next");
var OPEN_NEXT_DIR = path.join(__dirname, ".open-next");
debug({ NEXT_DIR, OPEN_NEXT_DIR });
var NextConfig = { "env": {}, "eslint": { "ignoreDuringBuilds": true }, "typescript": { "ignoreBuildErrors": true, "tsconfigPath": "tsconfig.json" }, "distDir": ".next", "cleanDistDir": true, "assetPrefix": "", "cacheMaxMemorySize": ********, "configOrigin": "next.config.mjs", "useFileSystemPublicRoutes": true, "generateEtags": true, "pageExtensions": ["ts", "tsx", "js", "jsx", "md", "mdx"], "poweredByHeader": true, "compress": true, "images": { "deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "loaderFile": "", "domains": [], "disableStaticImages": false, "minimumCacheTTL": 60, "formats": ["image/webp"], "dangerouslyAllowSVG": false, "contentSecurityPolicy": "script-src 'none'; frame-src 'none'; sandbox;", "contentDispositionType": "attachment", "remotePatterns": [{ "protocol": "https", "hostname": "*" }], "unoptimized": false }, "devIndicators": { "position": "bottom-left" }, "onDemandEntries": { "maxInactiveAge": 6e4, "pagesBufferLength": 5 }, "amp": { "canonicalBase": "" }, "basePath": "", "sassOptions": {}, "trailingSlash": false, "i18n": null, "productionBrowserSourceMaps": false, "excludeDefaultMomentLocales": true, "serverRuntimeConfig": {}, "publicRuntimeConfig": {}, "reactProductionProfiling": false, "reactStrictMode": false, "reactMaxHeadersLength": 6e3, "httpAgentOptions": { "keepAlive": true }, "logging": {}, "expireTime": 31536e3, "staticPageGenerationTimeout": 60, "output": "standalone", "modularizeImports": { "@mui/icons-material": { "transform": "@mui/icons-material/{{member}}" }, "lodash": { "transform": "lodash/{{member}}" } }, "outputFileTracingRoot": "/Users/<USER>/My-Project/fluxkrea", "experimental": { "nodeMiddleware": false, "cacheLife": { "default": { "stale": 300, "revalidate": 900, "expire": 4294967294 }, "seconds": { "stale": 0, "revalidate": 1, "expire": 60 }, "minutes": { "stale": 300, "revalidate": 60, "expire": 3600 }, "hours": { "stale": 300, "revalidate": 3600, "expire": 86400 }, "days": { "stale": 300, "revalidate": 86400, "expire": 604800 }, "weeks": { "stale": 300, "revalidate": 604800, "expire": 2592e3 }, "max": { "stale": 300, "revalidate": 2592e3, "expire": 4294967294 } }, "cacheHandlers": {}, "cssChunking": true, "multiZoneDraftMode": false, "appNavFailHandling": false, "prerenderEarlyExit": true, "serverMinification": true, "serverSourceMaps": false, "linkNoTouchStart": false, "caseSensitiveRoutes": false, "clientSegmentCache": false, "dynamicOnHover": false, "preloadEntriesOnStart": true, "clientRouterFilter": true, "clientRouterFilterRedirects": false, "fetchCacheKeyPrefix": "", "middlewarePrefetch": "flexible", "optimisticClientCache": true, "manualClientBasePath": false, "cpus": 9, "memoryBasedWorkersCount": false, "imgOptConcurrency": null, "imgOptTimeoutInSeconds": 7, "imgOptMaxInputPixels": 268402689, "imgOptSequentialRead": null, "isrFlushToDisk": true, "workerThreads": false, "optimizeCss": false, "nextScriptWorkers": false, "scrollRestoration": false, "externalDir": false, "disableOptimizedLoading": false, "gzipSize": true, "craCompat": false, "esmExternals": true, "fullySpecified": false, "swcTraceProfiling": false, "forceSwcTransforms": false, "largePageDataBytes": 128e3, "typedRoutes": false, "typedEnv": false, "parallelServerCompiles": false, "parallelServerBuildTraces": false, "ppr": false, "authInterrupts": false, "webpackMemoryOptimizations": false, "optimizeServerReact": true, "useEarlyImport": false, "viewTransition": false, "routerBFCache": false, "staleTimes": { "dynamic": 0, "static": 300 }, "serverComponentsHmrCache": true, "staticGenerationMaxConcurrency": 8, "staticGenerationMinPagesPerWorker": 25, "dynamicIO": false, "inlineCss": false, "useCache": false, "mdxRs": true, "optimizePackageImports": ["lucide-react", "date-fns", "lodash-es", "ramda", "antd", "react-bootstrap", "ahooks", "@ant-design/icons", "@headlessui/react", "@headlessui-float/react", "@heroicons/react/20/solid", "@heroicons/react/24/solid", "@heroicons/react/24/outline", "@visx/visx", "@tremor/react", "rxjs", "@mui/material", "@mui/icons-material", "recharts", "react-use", "effect", "@effect/schema", "@effect/platform", "@effect/platform-node", "@effect/platform-browser", "@effect/platform-bun", "@effect/sql", "@effect/sql-mssql", "@effect/sql-mysql2", "@effect/sql-pg", "@effect/sql-squlite-node", "@effect/sql-squlite-bun", "@effect/sql-squlite-wasm", "@effect/sql-squlite-react-native", "@effect/rpc", "@effect/rpc-http", "@effect/typeclass", "@effect/experimental", "@effect/opentelemetry", "@material-ui/core", "@material-ui/icons", "@tabler/icons-react", "mui-core", "react-icons/ai", "react-icons/bi", "react-icons/bs", "react-icons/cg", "react-icons/ci", "react-icons/di", "react-icons/fa", "react-icons/fa6", "react-icons/fc", "react-icons/fi", "react-icons/gi", "react-icons/go", "react-icons/gr", "react-icons/hi", "react-icons/hi2", "react-icons/im", "react-icons/io", "react-icons/io5", "react-icons/lia", "react-icons/lib", "react-icons/lu", "react-icons/md", "react-icons/pi", "react-icons/ri", "react-icons/rx", "react-icons/si", "react-icons/sl", "react-icons/tb", "react-icons/tfi", "react-icons/ti", "react-icons/vsc", "react-icons/wi"], "trustHostHeader": false, "isExperimentalCompile": false }, "htmlLimitedBots": "Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti", "bundlePagesRouterDependencies": false, "configFileName": "next.config.mjs", "turbopack": { "root": "/Users/<USER>/My-Project/fluxkrea" }, "_originalRedirects": [] };
var BuildId = "LVErFt4q-jqt7l5i7iJpr";
var RoutesManifest = { "basePath": "", "rewrites": { "beforeFiles": [], "afterFiles": [], "fallback": [] }, "redirects": [{ "source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$" }], "routes": { "static": [{ "page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$" }, { "page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$" }, { "page": "/terms-of-service", "regex": "^/terms\\-of\\-service(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-service(?:/)?$" }], "dynamic": [{ "page": "/api/auth/[...nextauth]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": { "nxtPnextauth": "nxtPnextauth" }, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$" }, { "page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$" }, { "page": "/[locale]/admin", "regex": "^/([^/]+?)/admin(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin(?:/)?$" }, { "page": "/[locale]/admin/feedbacks", "regex": "^/([^/]+?)/admin/feedbacks(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/feedbacks(?:/)?$" }, { "page": "/[locale]/admin/orders", "regex": "^/([^/]+?)/admin/orders(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/orders(?:/)?$" }, { "page": "/[locale]/admin/posts", "regex": "^/([^/]+?)/admin/posts(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts(?:/)?$" }, { "page": "/[locale]/admin/posts/add", "regex": "^/([^/]+?)/admin/posts/add(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts/add(?:/)?$" }, { "page": "/[locale]/admin/posts/[uuid]/edit", "regex": "^/([^/]+?)/admin/posts/([^/]+?)/edit(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale", "nxtPuuid": "nxtPuuid" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts/(?<nxtPuuid>[^/]+?)/edit(?:/)?$" }, { "page": "/[locale]/admin/translations", "regex": "^/([^/]+?)/admin/translations(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/translations(?:/)?$" }, { "page": "/[locale]/admin/users", "regex": "^/([^/]+?)/admin/users(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/users(?:/)?$" }, { "page": "/[locale]/ai-dashboard", "regex": "^/([^/]+?)/ai\\-dashboard(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/ai\\-dashboard(?:/)?$" }, { "page": "/[locale]/api-keys", "regex": "^/([^/]+?)/api\\-keys(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/api\\-keys(?:/)?$" }, { "page": "/[locale]/api-keys/create", "regex": "^/([^/]+?)/api\\-keys/create(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/api\\-keys/create(?:/)?$" }, { "page": "/[locale]/auth/signin", "regex": "^/([^/]+?)/auth/signin(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/auth/signin(?:/)?$" }, { "page": "/[locale]/components-demo", "regex": "^/([^/]+?)/components\\-demo(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo(?:/)?$" }, { "page": "/[locale]/components-demo/blog-detail", "regex": "^/([^/]+?)/components\\-demo/blog\\-detail(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/blog\\-detail(?:/)?$" }, { "page": "/[locale]/components-demo/content", "regex": "^/([^/]+?)/components\\-demo/content(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/content(?:/)?$" }, { "page": "/[locale]/components-demo/data", "regex": "^/([^/]+?)/components\\-demo/data(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/data(?:/)?$" }, { "page": "/[locale]/components-demo/editors", "regex": "^/([^/]+?)/components\\-demo/editors(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/editors(?:/)?$" }, { "page": "/[locale]/components-demo/interactive", "regex": "^/([^/]+?)/components\\-demo/interactive(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/interactive(?:/)?$" }, { "page": "/[locale]/components-demo/layout", "regex": "^/([^/]+?)/components\\-demo/layout(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/layout(?:/)?$" }, { "page": "/[locale]/components-demo/tools", "regex": "^/([^/]+?)/components\\-demo/tools(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/tools(?:/)?$" }, { "page": "/[locale]/i/[code]", "regex": "^/([^/]+?)/i/([^/]+?)(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale", "nxtPcode": "nxtPcode" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/i/(?<nxtPcode>[^/]+?)(?:/)?$" }, { "page": "/[locale]/my-credits", "regex": "^/([^/]+?)/my\\-credits(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-credits(?:/)?$" }, { "page": "/[locale]/my-invites", "regex": "^/([^/]+?)/my\\-invites(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-invites(?:/)?$" }, { "page": "/[locale]/my-orders", "regex": "^/([^/]+?)/my\\-orders(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-orders(?:/)?$" }, { "page": "/[locale]/pay-success/[session_id]", "regex": "^/([^/]+?)/pay\\-success/([^/]+?)(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale", "nxtPsession_id": "nxtPsession_id" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/pay\\-success/(?<nxtPsession_id>[^/]+?)(?:/)?$" }, { "page": "/[locale]/posts", "regex": "^/([^/]+?)/posts(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/posts(?:/)?$" }, { "page": "/[locale]/posts/[slug]", "regex": "^/([^/]+?)/posts/([^/]+?)(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/posts/(?<nxtPslug>[^/]+?)(?:/)?$" }, { "page": "/[locale]/pricing", "regex": "^/([^/]+?)/pricing(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/pricing(?:/)?$" }, { "page": "/[locale]/showcase", "regex": "^/([^/]+?)/showcase(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/showcase(?:/)?$" }, { "page": "/[locale]/showcase-demo", "regex": "^/([^/]+?)/showcase\\-demo(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/showcase\\-demo(?:/)?$" }, { "page": "/[locale]/test-i18n", "regex": "^/([^/]+?)/test\\-i18n(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-i18n(?:/)?$" }, { "page": "/[locale]/test-language-detection", "regex": "^/([^/]+?)/test\\-language\\-detection(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-language\\-detection(?:/)?$" }, { "page": "/[locale]/test-replicate", "regex": "^/([^/]+?)/test\\-replicate(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-replicate(?:/)?$" }, { "page": "/[locale]/test-vol", "regex": "^/([^/]+?)/test\\-vol(?:/)?$", "routeKeys": { "nxtPlocale": "nxtPlocale" }, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-vol(?:/)?$" }], "data": { "static": [], "dynamic": [] } }, "locales": [] };
var ConfigHeaders = [];
var PrerenderManifest = { "version": 4, "routes": {}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": { "previewModeId": "d0f4fdec5a555366623dff24ecd24aae", "previewModeSigningKey": "11e8dd91d7009e680783f7681b6290360a01db3cb39e1ee7516767884704ffad", "previewModeEncryptionKey": "429ed0bde5499d15e4426e7c8d6b50f19e6acb5013bd43e75b1659ae36650cdb" } };
var MiddlewareManifest = { "version": 3, "middleware": { "/": { "files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{ "regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/" }, { "regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*" }, { "regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!privacy-policy|terms-of-service|api\\/|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)" }], "wasm": [], "assets": [], "env": { "__NEXT_BUILD_ID": "LVErFt4q-jqt7l5i7iJpr", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dv7dWAk9T2M+/LsPh/t8l/c14E4NhTVpbZj8PiBGmPM=", "__NEXT_PREVIEW_MODE_ID": "d0f4fdec5a555366623dff24ecd24aae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "429ed0bde5499d15e4426e7c8d6b50f19e6acb5013bd43e75b1659ae36650cdb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "11e8dd91d7009e680783f7681b6290360a01db3cb39e1ee7516767884704ffad" } } }, "functions": {}, "sortedMiddleware": ["/"] };
var AppPathRoutesManifest = { "/_not-found/page": "/_not-found", "/api/add-feedback/route": "/api/add-feedback", "/api/ai/transfer-file/route": "/api/ai/transfer-file", "/api/ai/models/route": "/api/ai/models", "/api/admin/models/translations/route": "/api/admin/models/translations", "/api/ai/estimate-cost/route": "/api/ai/estimate-cost", "/api/ai/usage/route": "/api/ai/usage", "/api/ai/result/route": "/api/ai/result", "/api/checkout/route": "/api/checkout", "/api/ai/upload-image/route": "/api/ai/upload-image", "/api/get-user-credits/route": "/api/get-user-credits", "/api/download-proxy/route": "/api/download-proxy", "/api/get-user-info/route": "/api/get-user-info", "/api/ping/route": "/api/ping", "/api/stripe-notify/route": "/api/stripe-notify", "/api/update-invite-code/route": "/api/update-invite-code", "/api/test/replicate/route": "/api/test/replicate", "/api/update-invite/route": "/api/update-invite", "/api/volcengine/asr/query/route": "/api/volcengine/asr/query", "/api/volcengine/callback/route": "/api/volcengine/callback", "/api/volcengine/asr/submit/route": "/api/volcengine/asr/submit", "/api/volcengine/tts-async/submit/route": "/api/volcengine/tts-async/submit", "/api/volcengine/tts-async/query/route": "/api/volcengine/tts-async/query", "/api/ai/generate/route": "/api/ai/generate", "/api/volcengine/tts/route": "/api/volcengine/tts", "/api/volcengine/voice-clone/status/route": "/api/volcengine/voice-clone/status", "/api/volcengine/voice-clone/upload/route": "/api/volcengine/voice-clone/upload", "/api/auth/[...nextauth]/route": "/api/auth/[...nextauth]", "/(legal)/privacy-policy/page": "/privacy-policy", "/(legal)/terms-of-service/page": "/terms-of-service", "/[locale]/auth/signin/page": "/[locale]/auth/signin", "/[locale]/pay-success/[session_id]/page": "/[locale]/pay-success/[session_id]", "/[locale]/test-i18n/page": "/[locale]/test-i18n", "/[locale]/test-replicate/page": "/[locale]/test-replicate", "/[locale]/test-language-detection/page": "/[locale]/test-language-detection", "/[locale]/test-vol/page": "/[locale]/test-vol", "/[locale]/(admin)/admin/feedbacks/page": "/[locale]/admin/feedbacks", "/[locale]/(admin)/admin/posts/add/page": "/[locale]/admin/posts/add", "/[locale]/(admin)/admin/posts/[uuid]/edit/page": "/[locale]/admin/posts/[uuid]/edit", "/[locale]/(admin)/admin/orders/page": "/[locale]/admin/orders", "/[locale]/(admin)/admin/page": "/[locale]/admin", "/[locale]/(admin)/admin/users/page": "/[locale]/admin/users", "/[locale]/(default)/components-demo/data/page": "/[locale]/components-demo/data", "/[locale]/(default)/ai-dashboard/page": "/[locale]/ai-dashboard", "/[locale]/(default)/components-demo/editors/page": "/[locale]/components-demo/editors", "/[locale]/(default)/components-demo/page": "/[locale]/components-demo", "/[locale]/(default)/components-demo/content/page": "/[locale]/components-demo/content", "/[locale]/(default)/components-demo/interactive/page": "/[locale]/components-demo/interactive", "/[locale]/(default)/components-demo/layout/page": "/[locale]/components-demo/layout", "/[locale]/(default)/components-demo/blog-detail/page": "/[locale]/components-demo/blog-detail", "/[locale]/(default)/page": "/[locale]", "/[locale]/(default)/pricing/page": "/[locale]/pricing", "/[locale]/(default)/posts/page": "/[locale]/posts", "/[locale]/(default)/showcase/page": "/[locale]/showcase", "/[locale]/(default)/components-demo/tools/page": "/[locale]/components-demo/tools", "/[locale]/(default)/posts/[slug]/page": "/[locale]/posts/[slug]", "/[locale]/(default)/i/[code]/page": "/[locale]/i/[code]", "/[locale]/(admin)/admin/posts/page": "/[locale]/admin/posts", "/[locale]/(default)/showcase-demo/page": "/[locale]/showcase-demo", "/[locale]/(default)/admin/translations/page": "/[locale]/admin/translations", "/[locale]/(default)/(console)/api-keys/page": "/[locale]/api-keys", "/[locale]/(default)/(console)/api-keys/create/page": "/[locale]/api-keys/create", "/[locale]/(default)/(console)/my-credits/page": "/[locale]/my-credits", "/[locale]/(default)/(console)/my-orders/page": "/[locale]/my-orders", "/[locale]/(default)/(console)/my-invites/page": "/[locale]/my-invites" };
var FunctionsConfigManifest = { "version": 1, "functions": {} };
var PagesManifest = { "/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/404": "pages/404.html" };
process.env.NEXT_BUILD_ID = BuildId;

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/http/openNextResponse.js
init_logger();
init_util();
import { Transform } from "node:stream";

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/util.js
init_util();
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/i18n/index.js
init_stream();
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/i18n/accept-header.js
function parse(raw, preferences, options) {
  const lowers = /* @__PURE__ */ new Map();
  const header = raw.replace(/[ \t]/g, "");
  if (preferences) {
    let pos = 0;
    for (const preference of preferences) {
      const lower = preference.toLowerCase();
      lowers.set(lower, { orig: preference, pos: pos++ });
      if (options.prefixMatch) {
        const parts2 = lower.split("-");
        while (parts2.pop(), parts2.length > 0) {
          const joined = parts2.join("-");
          if (!lowers.has(joined)) {
            lowers.set(joined, { orig: preference, pos: pos++ });
          }
        }
      }
    }
  }
  const parts = header.split(",");
  const selections = [];
  const map = /* @__PURE__ */ new Set();
  for (let i = 0; i < parts.length; ++i) {
    const part = parts[i];
    if (!part) {
      continue;
    }
    const params = part.split(";");
    if (params.length > 2) {
      throw new Error(`Invalid ${options.type} header`);
    }
    const token = params[0].toLowerCase();
    if (!token) {
      throw new Error(`Invalid ${options.type} header`);
    }
    const selection = { token, pos: i, q: 1 };
    if (preferences && lowers.has(token)) {
      selection.pref = lowers.get(token).pos;
    }
    map.add(selection.token);
    if (params.length === 2) {
      const q = params[1];
      const [key, value] = q.split("=");
      if (!value || key !== "q" && key !== "Q") {
        throw new Error(`Invalid ${options.type} header`);
      }
      const score = Number.parseFloat(value);
      if (score === 0) {
        continue;
      }
      if (Number.isFinite(score) && score <= 1 && score >= 1e-3) {
        selection.q = score;
      }
    }
    selections.push(selection);
  }
  selections.sort((a, b) => {
    if (b.q !== a.q) {
      return b.q - a.q;
    }
    if (b.pref !== a.pref) {
      if (a.pref === void 0) {
        return 1;
      }
      if (b.pref === void 0) {
        return -1;
      }
      return a.pref - b.pref;
    }
    return a.pos - b.pos;
  });
  const values = selections.map((selection) => selection.token);
  if (!preferences || !preferences.length) {
    return values;
  }
  const preferred = [];
  for (const selection of values) {
    if (selection === "*") {
      for (const [preference, value] of lowers) {
        if (!map.has(preference)) {
          preferred.push(value.orig);
        }
      }
    } else {
      const lower = selection.toLowerCase();
      if (lowers.has(lower)) {
        preferred.push(lowers.get(lower).orig);
      }
    }
  }
  return preferred;
}
function acceptLanguage(header = "", preferences) {
  return parse(header, preferences, {
    type: "accept-language",
    prefixMatch: true
  })[0] || void 0;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/i18n/index.js
function isLocalizedPath(path3) {
  return NextConfig.i18n?.locales.includes(path3.split("/")[1].toLowerCase()) ?? false;
}
function getLocaleFromCookie(cookies) {
  const i18n = NextConfig.i18n;
  const nextLocale = cookies.NEXT_LOCALE?.toLowerCase();
  return nextLocale ? i18n?.locales.find((locale) => nextLocale === locale.toLowerCase()) : void 0;
}
function detectDomainLocale({ hostname, detectedLocale }) {
  const i18n = NextConfig.i18n;
  const domains = i18n?.domains;
  if (!domains) {
    return;
  }
  const lowercasedLocale = detectedLocale?.toLowerCase();
  for (const domain of domains) {
    const domainHostname = domain.domain.split(":", 1)[0].toLowerCase();
    if (hostname === domainHostname || lowercasedLocale === domain.defaultLocale.toLowerCase() || domain.locales?.some((locale) => lowercasedLocale === locale.toLowerCase())) {
      return domain;
    }
  }
}
function detectLocale(internalEvent, i18n) {
  const domainLocale = detectDomainLocale({
    hostname: internalEvent.headers.host
  });
  if (i18n.localeDetection === false) {
    return domainLocale?.defaultLocale ?? i18n.defaultLocale;
  }
  const cookiesLocale = getLocaleFromCookie(internalEvent.cookies);
  const preferredLocale = acceptLanguage(internalEvent.headers["accept-language"], i18n?.locales);
  debug({
    cookiesLocale,
    preferredLocale,
    defaultLocale: i18n.defaultLocale,
    domainLocale
  });
  return domainLocale?.defaultLocale ?? cookiesLocale ?? preferredLocale ?? i18n.defaultLocale;
}
function localizePath(internalEvent) {
  const i18n = NextConfig.i18n;
  if (!i18n) {
    return internalEvent.rawPath;
  }
  if (isLocalizedPath(internalEvent.rawPath)) {
    return internalEvent.rawPath;
  }
  const detectedLocale = detectLocale(internalEvent, i18n);
  return `/${detectedLocale}${internalEvent.rawPath}`;
}
function handleLocaleRedirect(internalEvent) {
  const i18n = NextConfig.i18n;
  if (!i18n || i18n.localeDetection === false || internalEvent.rawPath !== "/") {
    return false;
  }
  const preferredLocale = acceptLanguage(internalEvent.headers["accept-language"], i18n?.locales);
  const detectedLocale = detectLocale(internalEvent, i18n);
  const domainLocale = detectDomainLocale({
    hostname: internalEvent.headers.host
  });
  const preferredDomain = detectDomainLocale({
    detectedLocale: preferredLocale
  });
  if (domainLocale && preferredDomain) {
    const isPDomain = preferredDomain.domain === domainLocale.domain;
    const isPLocale = preferredDomain.defaultLocale === preferredLocale;
    if (!isPDomain || !isPLocale) {
      const scheme = `http${preferredDomain.http ? "" : "s"}`;
      const rlocale = isPLocale ? "" : preferredLocale;
      return {
        type: "core",
        statusCode: 307,
        headers: {
          Location: `${scheme}://${preferredDomain.domain}/${rlocale}`
        },
        body: emptyReadableStream(),
        isBase64Encoded: false
      };
    }
  }
  const defaultLocale = domainLocale?.defaultLocale ?? i18n.defaultLocale;
  if (detectedLocale.toLowerCase() !== defaultLocale.toLowerCase()) {
    return {
      type: "core",
      statusCode: 307,
      headers: {
        Location: constructNextUrl(internalEvent.url, `/${detectedLocale}`)
      },
      body: emptyReadableStream(),
      isBase64Encoded: false
    };
  }
  return false;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/queue.js
function generateShardId(rawPath, maxConcurrency, prefix) {
  let a = cyrb128(rawPath);
  let t = a += **********;
  t = Math.imul(t ^ t >>> 15, t | 1);
  t ^= t + Math.imul(t ^ t >>> 7, t | 61);
  const randomFloat = ((t ^ t >>> 14) >>> 0) / **********;
  const randomInt = Math.floor(randomFloat * maxConcurrency);
  return `${prefix}-${randomInt}`;
}
function generateMessageGroupId(rawPath) {
  const maxConcurrency = Number.parseInt(process.env.MAX_REVALIDATE_CONCURRENCY ?? "10");
  return generateShardId(rawPath, maxConcurrency, "revalidate");
}
function cyrb128(str) {
  let h1 = **********;
  let h2 = **********;
  let h3 = **********;
  let h4 = **********;
  for (let i = 0, k; i < str.length; i++) {
    k = str.charCodeAt(i);
    h1 = h2 ^ Math.imul(h1 ^ k, *********);
    h2 = h3 ^ Math.imul(h2 ^ k, **********);
    h3 = h4 ^ Math.imul(h3 ^ k, *********);
    h4 = h1 ^ Math.imul(h4 ^ k, **********);
  }
  h1 = Math.imul(h3 ^ h1 >>> 18, *********);
  h2 = Math.imul(h4 ^ h2 >>> 22, **********);
  h3 = Math.imul(h1 ^ h3 >>> 17, *********);
  h4 = Math.imul(h2 ^ h4 >>> 19, **********);
  h1 ^= h2 ^ h3 ^ h4, h2 ^= h1, h3 ^= h1, h4 ^= h1;
  return h1 >>> 0;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/util.js
function isExternal(url, host) {
  if (!url)
    return false;
  const pattern = /^https?:\/\//;
  if (host) {
    return pattern.test(url) && !url.includes(host);
  }
  return pattern.test(url);
}
function convertFromQueryString(query) {
  if (query === "")
    return {};
  const queryParts = query.split("&");
  return getQueryFromIterator(queryParts.map((p) => {
    const [key, value] = p.split("=");
    return [key, value];
  }));
}
function getUrlParts(url, isExternal2) {
  if (!isExternal2) {
    const regex2 = /\/([^?]*)\??(.*)/;
    const match3 = url.match(regex2);
    return {
      hostname: "",
      pathname: match3?.[1] ? `/${match3[1]}` : url,
      protocol: "",
      queryString: match3?.[2] ?? ""
    };
  }
  const regex = /^(https?:)\/\/?([^\/\s]+)(\/[^?]*)?(\?.*)?/;
  const match2 = url.match(regex);
  if (!match2) {
    throw new Error(`Invalid external URL: ${url}`);
  }
  return {
    protocol: match2[1] ?? "https:",
    hostname: match2[2],
    pathname: match2[3] ?? "",
    queryString: match2[4]?.slice(1) ?? ""
  };
}
function constructNextUrl(baseUrl, path3) {
  const nextBasePath = NextConfig.basePath ?? "";
  const url = new URL(`${nextBasePath}${path3}`, baseUrl);
  return url.href;
}
function convertToQueryString(query) {
  const queryStrings = [];
  Object.entries(query).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((entry) => queryStrings.push(`${key}=${entry}`));
    } else {
      queryStrings.push(`${key}=${value}`);
    }
  });
  return queryStrings.length > 0 ? `?${queryStrings.join("&")}` : "";
}
function getMiddlewareMatch(middlewareManifest2, functionsManifest) {
  if (functionsManifest?.functions?.["/_middleware"]) {
    return functionsManifest.functions["/_middleware"].matchers?.map(({ regexp }) => new RegExp(regexp)) ?? [/.*/];
  }
  const rootMiddleware = middlewareManifest2.middleware["/"];
  if (!rootMiddleware?.matchers)
    return [];
  return rootMiddleware.matchers.map(({ regexp }) => new RegExp(regexp));
}
function escapeRegex(str, { isPath } = {}) {
  const result = str.replaceAll("(.)", "_\xB51_").replaceAll("(..)", "_\xB52_").replaceAll("(...)", "_\xB53_");
  return isPath ? result : result.replaceAll("+", "_\xB54_");
}
function unescapeRegex(str) {
  return str.replaceAll("_\xB51_", "(.)").replaceAll("_\xB52_", "(..)").replaceAll("_\xB53_", "(...)").replaceAll("_\xB54_", "+");
}
function convertBodyToReadableStream(method, body) {
  if (method === "GET" || method === "HEAD")
    return void 0;
  if (!body)
    return void 0;
  const readable = new ReadableStream({
    start(controller) {
      controller.enqueue(body);
      controller.close();
    }
  });
  return readable;
}
var CommonHeaders;
(function(CommonHeaders2) {
  CommonHeaders2["CACHE_CONTROL"] = "cache-control";
  CommonHeaders2["NEXT_CACHE"] = "x-nextjs-cache";
})(CommonHeaders || (CommonHeaders = {}));

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routingHandler.js
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/cacheInterceptor.js
import { createHash } from "node:crypto";
init_stream();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/cache.js
init_logger();
async function hasBeenRevalidated(key, tags, cacheEntry) {
  if (globalThis.openNextConfig.dangerous?.disableTagCache) {
    return false;
  }
  const value = cacheEntry.value;
  if (!value) {
    return true;
  }
  if ("type" in cacheEntry && cacheEntry.type === "page") {
    return false;
  }
  const lastModified = cacheEntry.lastModified ?? Date.now();
  if (globalThis.tagCache.mode === "nextMode") {
    return await globalThis.tagCache.hasBeenRevalidated(tags, lastModified);
  }
  const _lastModified = await globalThis.tagCache.getLastModified(key, lastModified);
  return _lastModified === -1;
}
function getTagsFromValue(value) {
  if (!value) {
    return [];
  }
  try {
    return value.meta?.headers?.["x-next-cache-tags"]?.split(",") ?? [];
  } catch (e) {
    return [];
  }
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/cacheInterceptor.js
init_logger();
var CACHE_ONE_YEAR = 60 * 60 * 24 * 365;
var CACHE_ONE_MONTH = 60 * 60 * 24 * 30;
var VARY_HEADER = "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Next-Url";
async function computeCacheControl(path3, body, host, revalidate, lastModified) {
  let finalRevalidate = CACHE_ONE_YEAR;
  const existingRoute = Object.entries(PrerenderManifest.routes).find((p) => p[0] === path3)?.[1];
  if (revalidate === void 0 && existingRoute) {
    finalRevalidate = existingRoute.initialRevalidateSeconds === false ? CACHE_ONE_YEAR : existingRoute.initialRevalidateSeconds;
  } else if (revalidate !== void 0) {
    finalRevalidate = revalidate === false ? CACHE_ONE_YEAR : revalidate;
  }
  const age = Math.round((Date.now() - (lastModified ?? 0)) / 1e3);
  const hash = (str) => createHash("md5").update(str).digest("hex");
  const etag = hash(body);
  if (revalidate === 0) {
    return {
      "cache-control": "private, no-cache, no-store, max-age=0, must-revalidate",
      "x-opennext-cache": "ERROR",
      etag
    };
  }
  if (finalRevalidate !== CACHE_ONE_YEAR) {
    const sMaxAge = Math.max(finalRevalidate - age, 1);
    debug("sMaxAge", {
      finalRevalidate,
      age,
      lastModified,
      revalidate
    });
    const isStale = sMaxAge === 1;
    if (isStale) {
      let url = NextConfig.trailingSlash ? `${path3}/` : path3;
      if (NextConfig.basePath) {
        url = `${NextConfig.basePath}${url}`;
      }
      await globalThis.queue.send({
        MessageBody: {
          host,
          url,
          eTag: etag,
          lastModified: lastModified ?? Date.now()
        },
        MessageDeduplicationId: hash(`${path3}-${lastModified}-${etag}`),
        MessageGroupId: generateMessageGroupId(path3)
      });
    }
    return {
      "cache-control": `s-maxage=${sMaxAge}, stale-while-revalidate=${CACHE_ONE_MONTH}`,
      "x-opennext-cache": isStale ? "STALE" : "HIT",
      etag
    };
  }
  return {
    "cache-control": `s-maxage=${CACHE_ONE_YEAR}, stale-while-revalidate=${CACHE_ONE_MONTH}`,
    "x-opennext-cache": "HIT",
    etag
  };
}
async function generateResult(event, localizedPath, cachedValue, lastModified) {
  debug("Returning result from experimental cache");
  let body = "";
  let type = "application/octet-stream";
  let isDataRequest = false;
  switch (cachedValue.type) {
    case "app":
      isDataRequest = Boolean(event.headers.rsc);
      body = isDataRequest ? cachedValue.rsc : cachedValue.html;
      type = isDataRequest ? "text/x-component" : "text/html; charset=utf-8";
      break;
    case "page":
      isDataRequest = Boolean(event.query.__nextDataReq);
      body = isDataRequest ? JSON.stringify(cachedValue.json) : cachedValue.html;
      type = isDataRequest ? "application/json" : "text/html; charset=utf-8";
      break;
  }
  const cacheControl = await computeCacheControl(localizedPath, body, event.headers.host, cachedValue.revalidate, lastModified);
  return {
    type: "core",
    // sometimes other status codes can be cached, like 404. For these cases, we should return the correct status code
    statusCode: cachedValue.meta?.status ?? 200,
    body: toReadableStream(body, false),
    isBase64Encoded: false,
    headers: {
      ...cacheControl,
      "content-type": type,
      ...cachedValue.meta?.headers,
      vary: VARY_HEADER
    }
  };
}
function escapePathDelimiters(segment, escapeEncoded) {
  return segment.replace(new RegExp(`([/#?]${escapeEncoded ? "|%(2f|23|3f|5c)" : ""})`, "gi"), (char) => encodeURIComponent(char));
}
function decodePathParams(pathname) {
  return pathname.split("/").map((segment) => {
    try {
      return escapePathDelimiters(decodeURIComponent(segment), true);
    } catch (e) {
      return segment;
    }
  }).join("/");
}
async function cacheInterceptor(event) {
  if (Boolean(event.headers["next-action"]) || Boolean(event.headers["x-prerender-revalidate"]))
    return event;
  const cookies = event.headers.cookie || "";
  const hasPreviewData = cookies.includes("__prerender_bypass") || cookies.includes("__next_preview_data");
  if (hasPreviewData) {
    debug("Preview mode detected, passing through to handler");
    return event;
  }
  let localizedPath = localizePath(event);
  if (NextConfig.basePath) {
    localizedPath = localizedPath.replace(NextConfig.basePath, "");
  }
  localizedPath = localizedPath.replace(/\/$/, "");
  localizedPath = decodePathParams(localizedPath);
  debug("Checking cache for", localizedPath, PrerenderManifest);
  const isISR = Object.keys(PrerenderManifest.routes).includes(localizedPath ?? "/") || Object.values(PrerenderManifest.dynamicRoutes).some((dr) => new RegExp(dr.routeRegex).test(localizedPath));
  debug("isISR", isISR);
  if (isISR) {
    try {
      const cachedData = await globalThis.incrementalCache.get(localizedPath ?? "/index");
      debug("cached data in interceptor", cachedData);
      if (!cachedData?.value) {
        return event;
      }
      if (cachedData.value?.type === "app") {
        const tags = getTagsFromValue(cachedData.value);
        const _hasBeenRevalidated = await hasBeenRevalidated(localizedPath, tags, cachedData);
        if (_hasBeenRevalidated) {
          return event;
        }
      }
      const host = event.headers.host;
      switch (cachedData?.value?.type) {
        case "app":
        case "page":
          return generateResult(event, localizedPath, cachedData.value, cachedData.lastModified);
        case "redirect": {
          const cacheControl = await computeCacheControl(localizedPath, "", host, cachedData.value.revalidate, cachedData.lastModified);
          return {
            type: "core",
            statusCode: cachedData.value.meta?.status ?? 307,
            body: emptyReadableStream(),
            headers: {
              ...cachedData.value.meta?.headers ?? {},
              ...cacheControl
            },
            isBase64Encoded: false
          };
        }
        default:
          return event;
      }
    } catch (e) {
      debug("Error while fetching cache", e);
      return event;
    }
  }
  return event;
}

// node_modules/.pnpm/path-to-regexp@6.3.0/node_modules/path-to-regexp/dist.es2015/index.js
function lexer(str) {
  var tokens = [];
  var i = 0;
  while (i < str.length) {
    var char = str[i];
    if (char === "*" || char === "+" || char === "?") {
      tokens.push({ type: "MODIFIER", index: i, value: str[i++] });
      continue;
    }
    if (char === "\\") {
      tokens.push({ type: "ESCAPED_CHAR", index: i++, value: str[i++] });
      continue;
    }
    if (char === "{") {
      tokens.push({ type: "OPEN", index: i, value: str[i++] });
      continue;
    }
    if (char === "}") {
      tokens.push({ type: "CLOSE", index: i, value: str[i++] });
      continue;
    }
    if (char === ":") {
      var name = "";
      var j = i + 1;
      while (j < str.length) {
        var code = str.charCodeAt(j);
        if (
          // `0-9`
          code >= 48 && code <= 57 || // `A-Z`
          code >= 65 && code <= 90 || // `a-z`
          code >= 97 && code <= 122 || // `_`
          code === 95
        ) {
          name += str[j++];
          continue;
        }
        break;
      }
      if (!name)
        throw new TypeError("Missing parameter name at ".concat(i));
      tokens.push({ type: "NAME", index: i, value: name });
      i = j;
      continue;
    }
    if (char === "(") {
      var count = 1;
      var pattern = "";
      var j = i + 1;
      if (str[j] === "?") {
        throw new TypeError('Pattern cannot start with "?" at '.concat(j));
      }
      while (j < str.length) {
        if (str[j] === "\\") {
          pattern += str[j++] + str[j++];
          continue;
        }
        if (str[j] === ")") {
          count--;
          if (count === 0) {
            j++;
            break;
          }
        } else if (str[j] === "(") {
          count++;
          if (str[j + 1] !== "?") {
            throw new TypeError("Capturing groups are not allowed at ".concat(j));
          }
        }
        pattern += str[j++];
      }
      if (count)
        throw new TypeError("Unbalanced pattern at ".concat(i));
      if (!pattern)
        throw new TypeError("Missing pattern at ".concat(i));
      tokens.push({ type: "PATTERN", index: i, value: pattern });
      i = j;
      continue;
    }
    tokens.push({ type: "CHAR", index: i, value: str[i++] });
  }
  tokens.push({ type: "END", index: i, value: "" });
  return tokens;
}
function parse2(str, options) {
  if (options === void 0) {
    options = {};
  }
  var tokens = lexer(str);
  var _a = options.prefixes, prefixes = _a === void 0 ? "./" : _a, _b = options.delimiter, delimiter = _b === void 0 ? "/#?" : _b;
  var result = [];
  var key = 0;
  var i = 0;
  var path3 = "";
  var tryConsume = function(type) {
    if (i < tokens.length && tokens[i].type === type)
      return tokens[i++].value;
  };
  var mustConsume = function(type) {
    var value2 = tryConsume(type);
    if (value2 !== void 0)
      return value2;
    var _a2 = tokens[i], nextType = _a2.type, index = _a2.index;
    throw new TypeError("Unexpected ".concat(nextType, " at ").concat(index, ", expected ").concat(type));
  };
  var consumeText = function() {
    var result2 = "";
    var value2;
    while (value2 = tryConsume("CHAR") || tryConsume("ESCAPED_CHAR")) {
      result2 += value2;
    }
    return result2;
  };
  var isSafe = function(value2) {
    for (var _i = 0, delimiter_1 = delimiter; _i < delimiter_1.length; _i++) {
      var char2 = delimiter_1[_i];
      if (value2.indexOf(char2) > -1)
        return true;
    }
    return false;
  };
  var safePattern = function(prefix2) {
    var prev = result[result.length - 1];
    var prevText = prefix2 || (prev && typeof prev === "string" ? prev : "");
    if (prev && !prevText) {
      throw new TypeError('Must have text between two parameters, missing text after "'.concat(prev.name, '"'));
    }
    if (!prevText || isSafe(prevText))
      return "[^".concat(escapeString(delimiter), "]+?");
    return "(?:(?!".concat(escapeString(prevText), ")[^").concat(escapeString(delimiter), "])+?");
  };
  while (i < tokens.length) {
    var char = tryConsume("CHAR");
    var name = tryConsume("NAME");
    var pattern = tryConsume("PATTERN");
    if (name || pattern) {
      var prefix = char || "";
      if (prefixes.indexOf(prefix) === -1) {
        path3 += prefix;
        prefix = "";
      }
      if (path3) {
        result.push(path3);
        path3 = "";
      }
      result.push({
        name: name || key++,
        prefix,
        suffix: "",
        pattern: pattern || safePattern(prefix),
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    var value = char || tryConsume("ESCAPED_CHAR");
    if (value) {
      path3 += value;
      continue;
    }
    if (path3) {
      result.push(path3);
      path3 = "";
    }
    var open = tryConsume("OPEN");
    if (open) {
      var prefix = consumeText();
      var name_1 = tryConsume("NAME") || "";
      var pattern_1 = tryConsume("PATTERN") || "";
      var suffix = consumeText();
      mustConsume("CLOSE");
      result.push({
        name: name_1 || (pattern_1 ? key++ : ""),
        pattern: name_1 && !pattern_1 ? safePattern(prefix) : pattern_1,
        prefix,
        suffix,
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    mustConsume("END");
  }
  return result;
}
function compile(str, options) {
  return tokensToFunction(parse2(str, options), options);
}
function tokensToFunction(tokens, options) {
  if (options === void 0) {
    options = {};
  }
  var reFlags = flags(options);
  var _a = options.encode, encode = _a === void 0 ? function(x) {
    return x;
  } : _a, _b = options.validate, validate = _b === void 0 ? true : _b;
  var matches = tokens.map(function(token) {
    if (typeof token === "object") {
      return new RegExp("^(?:".concat(token.pattern, ")$"), reFlags);
    }
  });
  return function(data) {
    var path3 = "";
    for (var i = 0; i < tokens.length; i++) {
      var token = tokens[i];
      if (typeof token === "string") {
        path3 += token;
        continue;
      }
      var value = data ? data[token.name] : void 0;
      var optional = token.modifier === "?" || token.modifier === "*";
      var repeat = token.modifier === "*" || token.modifier === "+";
      if (Array.isArray(value)) {
        if (!repeat) {
          throw new TypeError('Expected "'.concat(token.name, '" to not repeat, but got an array'));
        }
        if (value.length === 0) {
          if (optional)
            continue;
          throw new TypeError('Expected "'.concat(token.name, '" to not be empty'));
        }
        for (var j = 0; j < value.length; j++) {
          var segment = encode(value[j], token);
          if (validate && !matches[i].test(segment)) {
            throw new TypeError('Expected all "'.concat(token.name, '" to match "').concat(token.pattern, '", but got "').concat(segment, '"'));
          }
          path3 += token.prefix + segment + token.suffix;
        }
        continue;
      }
      if (typeof value === "string" || typeof value === "number") {
        var segment = encode(String(value), token);
        if (validate && !matches[i].test(segment)) {
          throw new TypeError('Expected "'.concat(token.name, '" to match "').concat(token.pattern, '", but got "').concat(segment, '"'));
        }
        path3 += token.prefix + segment + token.suffix;
        continue;
      }
      if (optional)
        continue;
      var typeOfMessage = repeat ? "an array" : "a string";
      throw new TypeError('Expected "'.concat(token.name, '" to be ').concat(typeOfMessage));
    }
    return path3;
  };
}
function match(str, options) {
  var keys = [];
  var re = pathToRegexp(str, keys, options);
  return regexpToFunction(re, keys, options);
}
function regexpToFunction(re, keys, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.decode, decode = _a === void 0 ? function(x) {
    return x;
  } : _a;
  return function(pathname) {
    var m = re.exec(pathname);
    if (!m)
      return false;
    var path3 = m[0], index = m.index;
    var params = /* @__PURE__ */ Object.create(null);
    var _loop_1 = function(i2) {
      if (m[i2] === void 0)
        return "continue";
      var key = keys[i2 - 1];
      if (key.modifier === "*" || key.modifier === "+") {
        params[key.name] = m[i2].split(key.prefix + key.suffix).map(function(value) {
          return decode(value, key);
        });
      } else {
        params[key.name] = decode(m[i2], key);
      }
    };
    for (var i = 1; i < m.length; i++) {
      _loop_1(i);
    }
    return { path: path3, index, params };
  };
}
function escapeString(str) {
  return str.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
}
function flags(options) {
  return options && options.sensitive ? "" : "i";
}
function regexpToRegexp(path3, keys) {
  if (!keys)
    return path3;
  var groupsRegex = /\((?:\?<(.*?)>)?(?!\?)/g;
  var index = 0;
  var execResult = groupsRegex.exec(path3.source);
  while (execResult) {
    keys.push({
      // Use parenthesized substring match if available, index otherwise
      name: execResult[1] || index++,
      prefix: "",
      suffix: "",
      modifier: "",
      pattern: ""
    });
    execResult = groupsRegex.exec(path3.source);
  }
  return path3;
}
function arrayToRegexp(paths, keys, options) {
  var parts = paths.map(function(path3) {
    return pathToRegexp(path3, keys, options).source;
  });
  return new RegExp("(?:".concat(parts.join("|"), ")"), flags(options));
}
function stringToRegexp(path3, keys, options) {
  return tokensToRegexp(parse2(path3, options), keys, options);
}
function tokensToRegexp(tokens, keys, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.strict, strict = _a === void 0 ? false : _a, _b = options.start, start = _b === void 0 ? true : _b, _c = options.end, end = _c === void 0 ? true : _c, _d = options.encode, encode = _d === void 0 ? function(x) {
    return x;
  } : _d, _e = options.delimiter, delimiter = _e === void 0 ? "/#?" : _e, _f = options.endsWith, endsWith = _f === void 0 ? "" : _f;
  var endsWithRe = "[".concat(escapeString(endsWith), "]|$");
  var delimiterRe = "[".concat(escapeString(delimiter), "]");
  var route = start ? "^" : "";
  for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {
    var token = tokens_1[_i];
    if (typeof token === "string") {
      route += escapeString(encode(token));
    } else {
      var prefix = escapeString(encode(token.prefix));
      var suffix = escapeString(encode(token.suffix));
      if (token.pattern) {
        if (keys)
          keys.push(token);
        if (prefix || suffix) {
          if (token.modifier === "+" || token.modifier === "*") {
            var mod = token.modifier === "*" ? "?" : "";
            route += "(?:".concat(prefix, "((?:").concat(token.pattern, ")(?:").concat(suffix).concat(prefix, "(?:").concat(token.pattern, "))*)").concat(suffix, ")").concat(mod);
          } else {
            route += "(?:".concat(prefix, "(").concat(token.pattern, ")").concat(suffix, ")").concat(token.modifier);
          }
        } else {
          if (token.modifier === "+" || token.modifier === "*") {
            throw new TypeError('Can not repeat "'.concat(token.name, '" without a prefix and suffix'));
          }
          route += "(".concat(token.pattern, ")").concat(token.modifier);
        }
      } else {
        route += "(?:".concat(prefix).concat(suffix, ")").concat(token.modifier);
      }
    }
  }
  if (end) {
    if (!strict)
      route += "".concat(delimiterRe, "?");
    route += !options.endsWith ? "$" : "(?=".concat(endsWithRe, ")");
  } else {
    var endToken = tokens[tokens.length - 1];
    var isEndDelimited = typeof endToken === "string" ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1 : endToken === void 0;
    if (!strict) {
      route += "(?:".concat(delimiterRe, "(?=").concat(endsWithRe, "))?");
    }
    if (!isEndDelimited) {
      route += "(?=".concat(delimiterRe, "|").concat(endsWithRe, ")");
    }
  }
  return new RegExp(route, flags(options));
}
function pathToRegexp(path3, keys, options) {
  if (path3 instanceof RegExp)
    return regexpToRegexp(path3, keys);
  if (Array.isArray(path3))
    return arrayToRegexp(path3, keys, options);
  return stringToRegexp(path3, keys, options);
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/utils/normalize-path.js
import path2 from "node:path";
function normalizeRepeatedSlashes(url) {
  const urlNoQuery = url.host + url.pathname;
  return `${url.protocol}//${urlNoQuery.replace(/\\/g, "/").replace(/\/\/+/g, "/")}${url.search}`;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/matcher.js
init_stream();
init_logger();

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/routeMatcher.js
var optionalLocalePrefixRegex = `^/(?:${RoutesManifest.locales.map((locale) => `${locale}/?`).join("|")})?`;
var optionalBasepathPrefixRegex = RoutesManifest.basePath ? `^${RoutesManifest.basePath}/?` : "^/";
var optionalPrefix = optionalLocalePrefixRegex.replace("^/", optionalBasepathPrefixRegex);
function routeMatcher(routeDefinitions) {
  const regexp = routeDefinitions.map((route) => ({
    page: route.page,
    regexp: new RegExp(route.regex.replace("^/", optionalPrefix))
  }));
  const appPathsSet = /* @__PURE__ */ new Set();
  const routePathsSet = /* @__PURE__ */ new Set();
  for (const [k, v] of Object.entries(AppPathRoutesManifest)) {
    if (k.endsWith("page")) {
      appPathsSet.add(v);
    } else if (k.endsWith("route")) {
      routePathsSet.add(v);
    }
  }
  return function matchRoute(path3) {
    const foundRoutes = regexp.filter((route) => route.regexp.test(path3));
    return foundRoutes.map((foundRoute) => {
      let routeType = "page";
      if (appPathsSet.has(foundRoute.page)) {
        routeType = "app";
      } else if (routePathsSet.has(foundRoute.page)) {
        routeType = "route";
      }
      return {
        route: foundRoute.page,
        type: routeType
      };
    });
  };
}
var staticRouteMatcher = routeMatcher([
  ...RoutesManifest.routes.static,
  ...getStaticAPIRoutes()
]);
var dynamicRouteMatcher = routeMatcher(RoutesManifest.routes.dynamic);
function getStaticAPIRoutes() {
  const createRouteDefinition = (route) => ({
    page: route,
    regex: `^${route}(?:/)?$`
  });
  const dynamicRoutePages = new Set(RoutesManifest.routes.dynamic.map(({ page }) => page));
  const pagesStaticAPIRoutes = Object.keys(PagesManifest).filter((route) => route.startsWith("/api/") && !dynamicRoutePages.has(route)).map(createRouteDefinition);
  const appPathsStaticAPIRoutes = Object.values(AppPathRoutesManifest).filter((route) => route.startsWith("/api/") || route === "/api" && !dynamicRoutePages.has(route)).map(createRouteDefinition);
  return [...pagesStaticAPIRoutes, ...appPathsStaticAPIRoutes];
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/matcher.js
var routeHasMatcher = (headers, cookies, query) => (redirect) => {
  switch (redirect.type) {
    case "header":
      return !!headers?.[redirect.key.toLowerCase()] && new RegExp(redirect.value ?? "").test(headers[redirect.key.toLowerCase()] ?? "");
    case "cookie":
      return !!cookies?.[redirect.key] && new RegExp(redirect.value ?? "").test(cookies[redirect.key] ?? "");
    case "query":
      return query[redirect.key] && Array.isArray(redirect.value) ? redirect.value.reduce((prev, current) => prev || new RegExp(current).test(query[redirect.key]), false) : new RegExp(redirect.value ?? "").test(query[redirect.key] ?? "");
    case "host":
      return headers?.host !== "" && new RegExp(redirect.value ?? "").test(headers.host);
    default:
      return false;
  }
};
function checkHas(matcher, has, inverted = false) {
  return has ? has.reduce((acc, cur) => {
    if (acc === false)
      return false;
    return inverted ? !matcher(cur) : matcher(cur);
  }, true) : true;
}
var getParamsFromSource = (source) => (value) => {
  debug("value", value);
  const _match = source(value);
  return _match ? _match.params : {};
};
var computeParamHas = (headers, cookies, query) => (has) => {
  if (!has.value)
    return {};
  const matcher = new RegExp(`^${has.value}$`);
  const fromSource = (value) => {
    const matches = value.match(matcher);
    return matches?.groups ?? {};
  };
  switch (has.type) {
    case "header":
      return fromSource(headers[has.key.toLowerCase()] ?? "");
    case "cookie":
      return fromSource(cookies[has.key] ?? "");
    case "query":
      return Array.isArray(query[has.key]) ? fromSource(query[has.key].join(",")) : fromSource(query[has.key] ?? "");
    case "host":
      return fromSource(headers.host ?? "");
  }
};
function convertMatch(match2, toDestination, destination) {
  if (!match2) {
    return destination;
  }
  const { params } = match2;
  const isUsingParams = Object.keys(params).length > 0;
  return isUsingParams ? toDestination(params) : destination;
}
function getNextConfigHeaders(event, configHeaders) {
  if (!configHeaders) {
    return {};
  }
  const matcher = routeHasMatcher(event.headers, event.cookies, event.query);
  const requestHeaders = {};
  const localizedRawPath = localizePath(event);
  for (const { headers, has, missing, regex, source, locale } of configHeaders) {
    const path3 = locale === false ? event.rawPath : localizedRawPath;
    if (new RegExp(regex).test(path3) && checkHas(matcher, has) && checkHas(matcher, missing, true)) {
      const fromSource = match(source);
      const _match = fromSource(path3);
      headers.forEach((h) => {
        try {
          const key = convertMatch(_match, compile(h.key), h.key);
          const value = convertMatch(_match, compile(h.value), h.value);
          requestHeaders[key] = value;
        } catch {
          debug(`Error matching header ${h.key} with value ${h.value}`);
          requestHeaders[h.key] = h.value;
        }
      });
    }
  }
  return requestHeaders;
}
function handleRewrites(event, rewrites) {
  const { rawPath, headers, query, cookies, url } = event;
  const localizedRawPath = localizePath(event);
  const matcher = routeHasMatcher(headers, cookies, query);
  const computeHas = computeParamHas(headers, cookies, query);
  const rewrite = rewrites.find((route) => {
    const path3 = route.locale === false ? rawPath : localizedRawPath;
    return new RegExp(route.regex).test(path3) && checkHas(matcher, route.has) && checkHas(matcher, route.missing, true);
  });
  let finalQuery = query;
  let rewrittenUrl = url;
  const isExternalRewrite = isExternal(rewrite?.destination);
  debug("isExternalRewrite", isExternalRewrite);
  if (rewrite) {
    const { pathname, protocol, hostname, queryString } = getUrlParts(rewrite.destination, isExternalRewrite);
    const pathToUse = rewrite.locale === false ? rawPath : localizedRawPath;
    debug("urlParts", { pathname, protocol, hostname, queryString });
    const toDestinationPath = compile(escapeRegex(pathname, { isPath: true }));
    const toDestinationHost = compile(escapeRegex(hostname));
    const toDestinationQuery = compile(escapeRegex(queryString));
    const params = {
      // params for the source
      ...getParamsFromSource(match(escapeRegex(rewrite.source, { isPath: true })))(pathToUse),
      // params for the has
      ...rewrite.has?.reduce((acc, cur) => {
        return Object.assign(acc, computeHas(cur));
      }, {}),
      // params for the missing
      ...rewrite.missing?.reduce((acc, cur) => {
        return Object.assign(acc, computeHas(cur));
      }, {})
    };
    const isUsingParams = Object.keys(params).length > 0;
    let rewrittenQuery = queryString;
    let rewrittenHost = hostname;
    let rewrittenPath = pathname;
    if (isUsingParams) {
      rewrittenPath = unescapeRegex(toDestinationPath(params));
      rewrittenHost = unescapeRegex(toDestinationHost(params));
      rewrittenQuery = unescapeRegex(toDestinationQuery(params));
    }
    if (NextConfig.i18n && !isExternalRewrite) {
      const strippedPathLocale = rewrittenPath.replace(new RegExp(`^/(${NextConfig.i18n.locales.join("|")})`), "");
      if (strippedPathLocale.startsWith("/api/")) {
        rewrittenPath = strippedPathLocale;
      }
    }
    rewrittenUrl = isExternalRewrite ? `${protocol}//${rewrittenHost}${rewrittenPath}` : new URL(rewrittenPath, event.url).href;
    finalQuery = {
      ...query,
      ...convertFromQueryString(rewrittenQuery)
    };
    rewrittenUrl += convertToQueryString(finalQuery);
    debug("rewrittenUrl", { rewrittenUrl, finalQuery, isUsingParams });
  }
  return {
    internalEvent: {
      ...event,
      query: finalQuery,
      rawPath: new URL(rewrittenUrl).pathname,
      url: rewrittenUrl
    },
    __rewrite: rewrite,
    isExternalRewrite
  };
}
function handleRepeatedSlashRedirect(event) {
  if (event.rawPath.match(/(\\|\/\/)/)) {
    return {
      type: event.type,
      statusCode: 308,
      headers: {
        Location: normalizeRepeatedSlashes(new URL(event.url))
      },
      body: emptyReadableStream(),
      isBase64Encoded: false
    };
  }
  return false;
}
function handleTrailingSlashRedirect(event) {
  const url = new URL(event.rawPath, "http://localhost");
  if (
    // Someone is trying to redirect to a different origin, let's not do that
    url.host !== "localhost" || NextConfig.skipTrailingSlashRedirect || // We should not apply trailing slash redirect to API routes
    event.rawPath.startsWith("/api/")
  ) {
    return false;
  }
  const emptyBody = emptyReadableStream();
  if (NextConfig.trailingSlash && !event.headers["x-nextjs-data"] && !event.rawPath.endsWith("/") && !event.rawPath.match(/[\w-]+\.[\w]+$/g)) {
    const headersLocation = event.url.split("?");
    return {
      type: event.type,
      statusCode: 308,
      headers: {
        Location: `${headersLocation[0]}/${headersLocation[1] ? `?${headersLocation[1]}` : ""}`
      },
      body: emptyBody,
      isBase64Encoded: false
    };
  }
  if (!NextConfig.trailingSlash && event.rawPath.endsWith("/") && event.rawPath !== "/") {
    const headersLocation = event.url.split("?");
    return {
      type: event.type,
      statusCode: 308,
      headers: {
        Location: `${headersLocation[0].replace(/\/$/, "")}${headersLocation[1] ? `?${headersLocation[1]}` : ""}`
      },
      body: emptyBody,
      isBase64Encoded: false
    };
  }
  return false;
}
function handleRedirects(event, redirects) {
  const repeatedSlashRedirect = handleRepeatedSlashRedirect(event);
  if (repeatedSlashRedirect)
    return repeatedSlashRedirect;
  const trailingSlashRedirect = handleTrailingSlashRedirect(event);
  if (trailingSlashRedirect)
    return trailingSlashRedirect;
  const localeRedirect = handleLocaleRedirect(event);
  if (localeRedirect)
    return localeRedirect;
  const { internalEvent, __rewrite } = handleRewrites(event, redirects.filter((r) => !r.internal));
  if (__rewrite && !__rewrite.internal) {
    return {
      type: event.type,
      statusCode: __rewrite.statusCode ?? 308,
      headers: {
        Location: internalEvent.url
      },
      body: emptyReadableStream(),
      isBase64Encoded: false
    };
  }
}
function fixDataPage(internalEvent, buildId) {
  const { rawPath, query } = internalEvent;
  const basePath = NextConfig.basePath ?? "";
  const dataPattern = `${basePath}/_next/data/${buildId}`;
  if (rawPath.startsWith("/_next/data") && !rawPath.startsWith(dataPattern)) {
    return {
      type: internalEvent.type,
      statusCode: 404,
      body: toReadableStream("{}"),
      headers: {
        "Content-Type": "application/json"
      },
      isBase64Encoded: false
    };
  }
  if (rawPath.startsWith(dataPattern) && rawPath.endsWith(".json")) {
    const newPath = `${basePath}${rawPath.slice(dataPattern.length, -".json".length).replace(/^\/index$/, "/")}`;
    query.__nextDataReq = "1";
    return {
      ...internalEvent,
      rawPath: newPath,
      query,
      url: new URL(`${newPath}${convertToQueryString(query)}`, internalEvent.url).href
    };
  }
  return internalEvent;
}
function handleFallbackFalse(internalEvent, prerenderManifest) {
  const { rawPath } = internalEvent;
  const { dynamicRoutes, routes } = prerenderManifest;
  const prerenderedFallbackRoutes = Object.entries(dynamicRoutes).filter(([, { fallback }]) => fallback === false);
  const routeFallback = prerenderedFallbackRoutes.some(([, { routeRegex }]) => {
    const routeRegexExp = new RegExp(routeRegex);
    return routeRegexExp.test(rawPath);
  });
  const locales = NextConfig.i18n?.locales;
  const routesAlreadyHaveLocale = locales?.includes(rawPath.split("/")[1]) || // If we don't use locales, we don't need to add the default locale
  locales === void 0;
  let localizedPath = routesAlreadyHaveLocale ? rawPath : `/${NextConfig.i18n?.defaultLocale}${rawPath}`;
  if (
    // Not if localizedPath is "/" tho, because that would not make it find `isPregenerated` below since it would be try to match an empty string.
    localizedPath !== "/" && NextConfig.trailingSlash && localizedPath.endsWith("/")
  ) {
    localizedPath = localizedPath.slice(0, -1);
  }
  const matchedStaticRoute = staticRouteMatcher(localizedPath);
  const prerenderedFallbackRoutesName = prerenderedFallbackRoutes.map(([name]) => name);
  const matchedDynamicRoute = dynamicRouteMatcher(localizedPath).filter(({ route }) => !prerenderedFallbackRoutesName.includes(route));
  const isPregenerated = Object.keys(routes).includes(localizedPath);
  if (routeFallback && !isPregenerated && matchedStaticRoute.length === 0 && matchedDynamicRoute.length === 0) {
    return {
      event: {
        ...internalEvent,
        rawPath: "/404",
        url: constructNextUrl(internalEvent.url, "/404"),
        headers: {
          ...internalEvent.headers,
          "x-invoke-status": "404"
        }
      },
      isISR: false
    };
  }
  return {
    event: internalEvent,
    isISR: routeFallback || isPregenerated
  };
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routing/middleware.js
init_stream();
init_utils();
var middlewareManifest = MiddlewareManifest;
var functionsConfigManifest = FunctionsConfigManifest;
var middleMatch = getMiddlewareMatch(middlewareManifest, functionsConfigManifest);
function defaultMiddlewareLoader() {
  return Promise.resolve().then(() => (init_edgeFunctionHandler(), edgeFunctionHandler_exports));
}
async function handleMiddleware(internalEvent, initialSearch, middlewareLoader = defaultMiddlewareLoader) {
  const headers = internalEvent.headers;
  if (headers["x-isr"] && headers["x-prerender-revalidate"] === PrerenderManifest.preview.previewModeId)
    return internalEvent;
  const normalizedPath = localizePath(internalEvent);
  const hasMatch = middleMatch.some((r) => r.test(normalizedPath));
  if (!hasMatch)
    return internalEvent;
  const initialUrl = new URL(normalizedPath, internalEvent.url);
  initialUrl.search = initialSearch;
  const url = initialUrl.href;
  const middleware = await middlewareLoader();
  const result = await middleware.default({
    // `geo` is pre Next 15.
    geo: {
      // The city name is percent-encoded.
      // See https://github.com/vercel/vercel/blob/4cb6143/packages/functions/src/headers.ts#L94C19-L94C37
      city: decodeURIComponent(headers["x-open-next-city"]),
      country: headers["x-open-next-country"],
      region: headers["x-open-next-region"],
      latitude: headers["x-open-next-latitude"],
      longitude: headers["x-open-next-longitude"]
    },
    headers,
    method: internalEvent.method || "GET",
    nextConfig: {
      basePath: NextConfig.basePath,
      i18n: NextConfig.i18n,
      trailingSlash: NextConfig.trailingSlash
    },
    url,
    body: convertBodyToReadableStream(internalEvent.method, internalEvent.body)
  });
  const statusCode = result.status;
  const responseHeaders = result.headers;
  const reqHeaders = {};
  const resHeaders = {};
  const filteredHeaders = [
    "x-middleware-override-headers",
    "x-middleware-next",
    "x-middleware-rewrite",
    // We need to drop `content-encoding` because it will be decoded
    "content-encoding"
  ];
  const xMiddlewareKey = "x-middleware-request-";
  responseHeaders.forEach((value, key) => {
    if (key.startsWith(xMiddlewareKey)) {
      const k = key.substring(xMiddlewareKey.length);
      reqHeaders[k] = value;
    } else {
      if (filteredHeaders.includes(key.toLowerCase()))
        return;
      if (key.toLowerCase() === "set-cookie") {
        resHeaders[key] = resHeaders[key] ? [...resHeaders[key], value] : [value];
      } else {
        resHeaders[key] = value;
      }
    }
  });
  const rewriteUrl = responseHeaders.get("x-middleware-rewrite");
  let isExternalRewrite = false;
  let middlewareQuery = internalEvent.query;
  let newUrl = internalEvent.url;
  if (rewriteUrl) {
    newUrl = rewriteUrl;
    if (isExternal(newUrl, internalEvent.headers.host)) {
      isExternalRewrite = true;
    } else {
      const rewriteUrlObject = new URL(rewriteUrl);
      middlewareQuery = getQueryFromSearchParams(rewriteUrlObject.searchParams);
      if ("__nextDataReq" in internalEvent.query) {
        middlewareQuery.__nextDataReq = internalEvent.query.__nextDataReq;
      }
    }
  }
  if (!rewriteUrl && !responseHeaders.get("x-middleware-next")) {
    const body = result.body ?? emptyReadableStream();
    return {
      type: internalEvent.type,
      statusCode,
      headers: resHeaders,
      body,
      isBase64Encoded: false
    };
  }
  return {
    responseHeaders: resHeaders,
    url: newUrl,
    rawPath: new URL(newUrl).pathname,
    type: internalEvent.type,
    headers: { ...internalEvent.headers, ...reqHeaders },
    body: internalEvent.body,
    method: internalEvent.method,
    query: middlewareQuery,
    cookies: internalEvent.cookies,
    remoteAddress: internalEvent.remoteAddress,
    isExternalRewrite
  };
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/core/routingHandler.js
var MIDDLEWARE_HEADER_PREFIX = "x-middleware-response-";
var MIDDLEWARE_HEADER_PREFIX_LEN = MIDDLEWARE_HEADER_PREFIX.length;
var INTERNAL_HEADER_PREFIX = "x-opennext-";
var INTERNAL_HEADER_INITIAL_URL = `${INTERNAL_HEADER_PREFIX}initial-url`;
var INTERNAL_HEADER_LOCALE = `${INTERNAL_HEADER_PREFIX}locale`;
var INTERNAL_HEADER_RESOLVED_ROUTES = `${INTERNAL_HEADER_PREFIX}resolved-routes`;
var INTERNAL_EVENT_REQUEST_ID = `${INTERNAL_HEADER_PREFIX}request-id`;
var geoHeaderToNextHeader = {
  "x-open-next-city": "x-vercel-ip-city",
  "x-open-next-country": "x-vercel-ip-country",
  "x-open-next-region": "x-vercel-ip-country-region",
  "x-open-next-latitude": "x-vercel-ip-latitude",
  "x-open-next-longitude": "x-vercel-ip-longitude"
};
function applyMiddlewareHeaders(eventOrResult, middlewareHeaders) {
  const isResult = isInternalResult(eventOrResult);
  const headers = eventOrResult.headers;
  const keyPrefix = isResult ? "" : MIDDLEWARE_HEADER_PREFIX;
  Object.entries(middlewareHeaders).forEach(([key, value]) => {
    if (value) {
      headers[keyPrefix + key] = Array.isArray(value) ? value.join(",") : value;
    }
  });
}
async function routingHandler(event, { assetResolver }) {
  try {
    for (const [openNextGeoName, nextGeoName] of Object.entries(geoHeaderToNextHeader)) {
      const value = event.headers[openNextGeoName];
      if (value) {
        event.headers[nextGeoName] = value;
      }
    }
    for (const key of Object.keys(event.headers)) {
      if (key.startsWith(INTERNAL_HEADER_PREFIX) || key.startsWith(MIDDLEWARE_HEADER_PREFIX)) {
        delete event.headers[key];
      }
    }
    let headers = getNextConfigHeaders(event, ConfigHeaders);
    let eventOrResult = fixDataPage(event, BuildId);
    if (isInternalResult(eventOrResult)) {
      return eventOrResult;
    }
    const redirect = handleRedirects(eventOrResult, RoutesManifest.redirects);
    if (redirect) {
      redirect.headers.Location = new URL(redirect.headers.Location).href;
      debug("redirect", redirect);
      return redirect;
    }
    const middlewareEventOrResult = await handleMiddleware(
      eventOrResult,
      // We need to pass the initial search without any decoding
      // TODO: we'd need to refactor InternalEvent to include the initial querystring directly
      // Should be done in another PR because it is a breaking change
      new URL(event.url).search
    );
    if (isInternalResult(middlewareEventOrResult)) {
      return middlewareEventOrResult;
    }
    headers = {
      ...middlewareEventOrResult.responseHeaders,
      ...headers
    };
    let isExternalRewrite = middlewareEventOrResult.isExternalRewrite ?? false;
    eventOrResult = middlewareEventOrResult;
    if (!isExternalRewrite) {
      const beforeRewrite = handleRewrites(eventOrResult, RoutesManifest.rewrites.beforeFiles);
      eventOrResult = beforeRewrite.internalEvent;
      isExternalRewrite = beforeRewrite.isExternalRewrite;
      if (!isExternalRewrite) {
        const assetResult = await assetResolver?.maybeGetAssetResult?.(eventOrResult);
        if (assetResult) {
          applyMiddlewareHeaders(assetResult, headers);
          return assetResult;
        }
      }
    }
    const foundStaticRoute = staticRouteMatcher(eventOrResult.rawPath);
    const isStaticRoute = !isExternalRewrite && foundStaticRoute.length > 0;
    if (!(isStaticRoute || isExternalRewrite)) {
      const afterRewrite = handleRewrites(eventOrResult, RoutesManifest.rewrites.afterFiles);
      eventOrResult = afterRewrite.internalEvent;
      isExternalRewrite = afterRewrite.isExternalRewrite;
    }
    let isISR = false;
    if (!isExternalRewrite) {
      const fallbackResult = handleFallbackFalse(eventOrResult, PrerenderManifest);
      eventOrResult = fallbackResult.event;
      isISR = fallbackResult.isISR;
    }
    const foundDynamicRoute = dynamicRouteMatcher(eventOrResult.rawPath);
    const isDynamicRoute = !isExternalRewrite && foundDynamicRoute.length > 0;
    if (!(isDynamicRoute || isStaticRoute || isExternalRewrite)) {
      const fallbackRewrites = handleRewrites(eventOrResult, RoutesManifest.rewrites.fallback);
      eventOrResult = fallbackRewrites.internalEvent;
      isExternalRewrite = fallbackRewrites.isExternalRewrite;
    }
    const isNextImageRoute = eventOrResult.rawPath.startsWith("/_next/image");
    const isRouteFoundBeforeAllRewrites = isStaticRoute || isDynamicRoute || isExternalRewrite;
    if (!(isRouteFoundBeforeAllRewrites || isNextImageRoute || // We need to check again once all rewrites have been applied
    staticRouteMatcher(eventOrResult.rawPath).length > 0 || dynamicRouteMatcher(eventOrResult.rawPath).length > 0)) {
      eventOrResult = {
        ...eventOrResult,
        rawPath: "/404",
        url: constructNextUrl(eventOrResult.url, "/404"),
        headers: {
          ...eventOrResult.headers,
          "x-middleware-response-cache-control": "private, no-cache, no-store, max-age=0, must-revalidate"
        }
      };
    }
    if (globalThis.openNextConfig.dangerous?.enableCacheInterception && !isInternalResult(eventOrResult)) {
      debug("Cache interception enabled");
      eventOrResult = await cacheInterceptor(eventOrResult);
      if (isInternalResult(eventOrResult)) {
        applyMiddlewareHeaders(eventOrResult, headers);
        return eventOrResult;
      }
    }
    applyMiddlewareHeaders(eventOrResult, headers);
    const resolvedRoutes = [
      ...foundStaticRoute,
      ...foundDynamicRoute
    ];
    debug("resolvedRoutes", resolvedRoutes);
    return {
      internalEvent: eventOrResult,
      isExternalRewrite,
      origin: false,
      isISR,
      resolvedRoutes,
      initialURL: event.url,
      locale: NextConfig.i18n ? detectLocale(eventOrResult, NextConfig.i18n) : void 0
    };
  } catch (e) {
    error("Error in routingHandler", e);
    return {
      internalEvent: {
        type: "core",
        method: "GET",
        rawPath: "/500",
        url: constructNextUrl(event.url, "/500"),
        headers: {
          ...event.headers
        },
        query: event.query,
        cookies: event.cookies,
        remoteAddress: event.remoteAddress
      },
      isExternalRewrite: false,
      origin: false,
      isISR: false,
      resolvedRoutes: [],
      initialURL: event.url,
      locale: NextConfig.i18n ? detectLocale(event, NextConfig.i18n) : void 0
    };
  }
}
function isInternalResult(eventOrResult) {
  return eventOrResult != null && "statusCode" in eventOrResult;
}

// node_modules/.pnpm/@opennextjs+aws@3.7.2/node_modules/@opennextjs/aws/dist/adapters/middleware.js
globalThis.internalFetch = fetch;
globalThis.__openNextAls = new AsyncLocalStorage();
var defaultHandler = async (internalEvent, options) => {
  const middlewareConfig = globalThis.openNextConfig.middleware;
  const originResolver = await resolveOriginResolver(middlewareConfig?.originResolver);
  const externalRequestProxy = await resolveProxyRequest(middlewareConfig?.override?.proxyExternalRequest);
  const assetResolver = await resolveAssetResolver(middlewareConfig?.assetResolver);
  const requestId = Math.random().toString(36);
  return runWithOpenNextRequestContext({
    isISRRevalidation: internalEvent.headers["x-isr"] === "1",
    waitUntil: options?.waitUntil,
    requestId
  }, async () => {
    const result = await routingHandler(internalEvent, { assetResolver });
    if ("internalEvent" in result) {
      debug("Middleware intercepted event", internalEvent);
      if (!result.isExternalRewrite) {
        const origin = await originResolver.resolve(result.internalEvent.rawPath);
        return {
          type: "middleware",
          internalEvent: {
            ...result.internalEvent,
            headers: {
              ...result.internalEvent.headers,
              [INTERNAL_HEADER_INITIAL_URL]: internalEvent.url,
              [INTERNAL_HEADER_RESOLVED_ROUTES]: JSON.stringify(result.resolvedRoutes),
              [INTERNAL_EVENT_REQUEST_ID]: requestId
            }
          },
          isExternalRewrite: result.isExternalRewrite,
          origin,
          isISR: result.isISR,
          initialURL: result.initialURL,
          resolvedRoutes: result.resolvedRoutes
        };
      }
      try {
        return externalRequestProxy.proxy(result.internalEvent);
      } catch (e) {
        error("External request failed.", e);
        return {
          type: "middleware",
          internalEvent: {
            ...result.internalEvent,
            headers: {
              ...result.internalEvent.headers,
              [INTERNAL_EVENT_REQUEST_ID]: requestId
            },
            rawPath: "/500",
            url: constructNextUrl(result.internalEvent.url, "/500"),
            method: "GET"
          },
          // On error we need to rewrite to the 500 page which is an internal rewrite
          isExternalRewrite: false,
          origin: false,
          isISR: result.isISR,
          initialURL: result.internalEvent.url,
          resolvedRoutes: [{ route: "/500", type: "page" }]
        };
      }
    }
    result.headers[INTERNAL_EVENT_REQUEST_ID] = requestId;
    debug("Middleware response", result);
    return result;
  });
};
var handler2 = await createGenericHandler({
  handler: defaultHandler,
  type: "middleware"
});
var middleware_default = {
  fetch: handler2
};
export {
  middleware_default as default,
  handler2 as handler
};
