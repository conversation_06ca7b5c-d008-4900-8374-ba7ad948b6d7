CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("LVErFt4q-jqt7l5i7iJpr/_N_T_/layout", "LVErFt4q-jqt7l5i7iJpr/_not-found"), ("LVErFt4q-jqt7l5i7iJpr/_N_T_/_not-found/layout", "LVErFt4q-jqt7l5i7iJpr/_not-found"), ("LVErFt4q-jqt7l5i7iJpr/_N_T_/_not-found/page", "LVErFt4q-jqt7l5i7iJpr/_not-found"), ("LVErFt4q-jqt7l5i7iJpr/_N_T_/_not-found", "LVErFt4q-jqt7l5i7iJpr/_not-found");