{"/_not-found/page": "/_not-found", "/api/add-feedback/route": "/api/add-feedback", "/api/ai/transfer-file/route": "/api/ai/transfer-file", "/api/ai/models/route": "/api/ai/models", "/api/admin/models/translations/route": "/api/admin/models/translations", "/api/ai/estimate-cost/route": "/api/ai/estimate-cost", "/api/ai/usage/route": "/api/ai/usage", "/api/ai/result/route": "/api/ai/result", "/api/checkout/route": "/api/checkout", "/api/ai/upload-image/route": "/api/ai/upload-image", "/api/get-user-credits/route": "/api/get-user-credits", "/api/download-proxy/route": "/api/download-proxy", "/api/get-user-info/route": "/api/get-user-info", "/api/ping/route": "/api/ping", "/api/stripe-notify/route": "/api/stripe-notify", "/api/update-invite-code/route": "/api/update-invite-code", "/api/test/replicate/route": "/api/test/replicate", "/api/update-invite/route": "/api/update-invite", "/api/volcengine/asr/query/route": "/api/volcengine/asr/query", "/api/volcengine/callback/route": "/api/volcengine/callback", "/api/volcengine/asr/submit/route": "/api/volcengine/asr/submit", "/api/volcengine/tts-async/submit/route": "/api/volcengine/tts-async/submit", "/api/volcengine/tts-async/query/route": "/api/volcengine/tts-async/query", "/api/ai/generate/route": "/api/ai/generate", "/api/volcengine/tts/route": "/api/volcengine/tts", "/api/volcengine/voice-clone/status/route": "/api/volcengine/voice-clone/status", "/api/volcengine/voice-clone/upload/route": "/api/volcengine/voice-clone/upload", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/(legal)/privacy-policy/page": "/privacy-policy", "/(legal)/terms-of-service/page": "/terms-of-service", "/[locale]/auth/signin/page": "/[locale]/auth/signin", "/[locale]/pay-success/[session_id]/page": "/[locale]/pay-success/[session_id]", "/[locale]/test-i18n/page": "/[locale]/test-i18n", "/[locale]/test-replicate/page": "/[locale]/test-replicate", "/[locale]/test-language-detection/page": "/[locale]/test-language-detection", "/[locale]/test-vol/page": "/[locale]/test-vol", "/[locale]/(admin)/admin/feedbacks/page": "/[locale]/admin/feedbacks", "/[locale]/(admin)/admin/posts/add/page": "/[locale]/admin/posts/add", "/[locale]/(admin)/admin/posts/[uuid]/edit/page": "/[locale]/admin/posts/[uuid]/edit", "/[locale]/(admin)/admin/orders/page": "/[locale]/admin/orders", "/[locale]/(admin)/admin/page": "/[locale]/admin", "/[locale]/(admin)/admin/users/page": "/[locale]/admin/users", "/[locale]/(default)/components-demo/data/page": "/[locale]/components-demo/data", "/[locale]/(default)/ai-dashboard/page": "/[locale]/ai-dashboard", "/[locale]/(default)/components-demo/editors/page": "/[locale]/components-demo/editors", "/[locale]/(default)/components-demo/page": "/[locale]/components-demo", "/[locale]/(default)/components-demo/content/page": "/[locale]/components-demo/content", "/[locale]/(default)/components-demo/interactive/page": "/[locale]/components-demo/interactive", "/[locale]/(default)/components-demo/layout/page": "/[locale]/components-demo/layout", "/[locale]/(default)/components-demo/blog-detail/page": "/[locale]/components-demo/blog-detail", "/[locale]/(default)/page": "/[locale]", "/[locale]/(default)/pricing/page": "/[locale]/pricing", "/[locale]/(default)/posts/page": "/[locale]/posts", "/[locale]/(default)/showcase/page": "/[locale]/showcase", "/[locale]/(default)/components-demo/tools/page": "/[locale]/components-demo/tools", "/[locale]/(default)/posts/[slug]/page": "/[locale]/posts/[slug]", "/[locale]/(default)/i/[code]/page": "/[locale]/i/[code]", "/[locale]/(admin)/admin/posts/page": "/[locale]/admin/posts", "/[locale]/(default)/showcase-demo/page": "/[locale]/showcase-demo", "/[locale]/(default)/admin/translations/page": "/[locale]/admin/translations", "/[locale]/(default)/(console)/api-keys/page": "/[locale]/api-keys", "/[locale]/(default)/(console)/api-keys/create/page": "/[locale]/api-keys/create", "/[locale]/(default)/(console)/my-credits/page": "/[locale]/my-credits", "/[locale]/(default)/(console)/my-orders/page": "/[locale]/my-orders", "/[locale]/(default)/(console)/my-invites/page": "/[locale]/my-invites"}