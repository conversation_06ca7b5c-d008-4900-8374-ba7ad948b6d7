exports.id=631,exports.ids=[631],exports.modules={7330:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>n,TN:()=>d});var a=s(25323),r=s(18542),l=s(51238),o=s(91145);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),n=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,o.cn)(i({variant:t}),e),...s}));n.displayName="Alert",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h5",{ref:s,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},36151:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o});var a=s(96081);s(73636);let r=(e,t)=>{let s="http://localhost:3000";return{"@type":"BreadcrumbList","@id":`${e}#breadcrumb`,itemListElement:[{"@type":"ListItem",position:1,name:"首页",item:s||""},...t.map((e,t)=>({"@type":"ListItem",position:t+2,name:e.name,item:`${s}${e.url}`}))]}},l=async e=>{let{title:t,description:s,image:a,url:r,schemaType:l="SoftwareApplication",datePublished:o,dateModified:i,applicationCategory:n="GameApplication",operatingSystem:d,ratingValue:c,ratingCount:m,authorName:u,wordCount:p,readingTime:x,tags:h}=e,g="http://localhost:3000",f="KREA FLUX";g&&f||console.warn("Schema: Missing required environment variables NEXT_PUBLIC_WEB_URL or NEXT_PUBLIC_PROJECT_NAME");let b={"@context":"https://schema.org","@id":r,description:s,image:{"@type":"ImageObject",url:a,contentUrl:a},url:r,author:{"@type":u?"Person":"Organization",name:u||f||"QQ AI",...u?{}:{url:g||"",logo:{"@type":"ImageObject",url:`${g||""}/logo.png`}}},publisher:{"@type":"Organization",name:f||"QQ AI",url:g||"",logo:{"@type":"ImageObject",url:`${g||""}/logo.png`}},mainEntityOfPage:r,...o&&{datePublished:o},...i&&{dateModified:i},...c&&m&&{aggregateRating:{"@type":"AggregateRating",ratingValue:c,ratingCount:m}}};switch(l){case"SoftwareApplication":return{...b,"@type":"SoftwareApplication",name:t,applicationCategory:n,...d&&{operatingSystem:d}};case"Article":return{...b,"@type":"Article",headline:t,...p&&{wordCount:p},...x&&{timeRequired:`PT${x}M`},...h&&h.length>0&&{keywords:h.join(", ")}};case"WebPage":return{...b,"@type":"WebPage",name:t};default:throw Error(`Unsupported schema type: ${l}`)}};async function o(e){try{let t=await l(e),s=r(e.url,e.breadcrumb);return(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify([t,s],null,0)}})}catch(e){return console.error("Schema generation error:",e),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:"{}"}})}}},60004:(e,t,s)=>{"use strict";s.d(t,{WorkspaceLayout:()=>eb});var a=s(25323),r=s(9392),l=s(37604),o=s(18542);function i(){let[e,t]=(0,o.useState)({deviceType:"desktop",layoutMode:"desktop",isMobile:!1,isTablet:!1,isDesktop:!0,isSmallMobile:!1,screenWidth:1920,screenHeight:1080});return e}let n={container:{smallMobile:{display:"flex flex-col",gap:"gap-1",padding:"p-1",width:"w-full",height:"h-auto"},mobile:{display:"flex flex-col",gap:"gap-2",padding:"p-2",width:"w-full",height:"h-auto"},tablet:{display:"grid grid-cols-5",gap:"gap-4",padding:"p-4",width:"w-full",height:"h-auto"},desktop:{display:"grid grid-cols-10",gap:"gap-6",padding:"p-6",width:"w-full",height:"h-full"}},sidebar:{mobile:{width:"w-full",height:"h-auto",order:"order-1"},tablet:{width:"col-span-2",height:"h-auto",order:"order-none"},desktop:{width:"col-span-1",height:"h-full",order:"order-none"}},main:{mobile:{width:"w-full",height:"h-auto",order:"order-2"},tablet:{width:"col-span-2",height:"h-auto",order:"order-none"},desktop:{width:"col-span-3",height:"h-full",order:"order-none"}},result:{mobile:{width:"w-full",height:"h-auto",order:"order-3"},tablet:{width:"col-span-3",height:"h-auto",order:"order-none"},desktop:{width:"col-span-6",height:"h-full",order:"order-none"}},card:{smallMobile:{padding:"p-1",margin:"m-0",borderRadius:"rounded-md",background:"bg-card",width:"w-full max-w-full"},mobile:{padding:"p-1",margin:"m-0",borderRadius:"rounded-lg",background:"bg-card",width:"w-full max-w-full"},tablet:{padding:"p-4",margin:"m-0",borderRadius:"rounded-xl",background:"bg-card",width:"w-full max-w-full"},desktop:{padding:"p-6",margin:"m-0",borderRadius:"rounded-xl",background:"bg-card",width:"w-full max-w-full"}},button:{mobile:{size:"h-12 px-4",text:"text-sm",gap:"gap-2",direction:"flex-row"},tablet:{size:"h-12 px-4",text:"text-base",gap:"gap-2",direction:"flex-row"},desktop:{size:"h-12 px-4",text:"text-base",gap:"gap-3",direction:"flex-row"}}};function d(e,t,s){return s&&e.smallMobile?e.smallMobile:e[t]||e.desktop||{}}function c(e,t){let{deviceType:s,isSmallMobile:a}=i();return(0,o.useMemo)(()=>{let r=t?d(n[t],s,a):{},l=d(e,s,a),o={...r,...l};return{styles:o,className:Object.values(o).filter(Boolean).join(" "),deviceType:s,isSmallMobile:a}},[e,t,s,a])}var m=s(99110),u=s(8894),p=s(24504),x=s(20856),h=s(7892),g=s(1053),f=s(91145);function b({...e}){return(0,a.jsx)(p.bL,{"data-slot":"rich-select",...e})}function v({...e}){return(0,a.jsx)(p.WT,{"data-slot":"rich-select-value",...e})}function j({className:e,size:t="default",children:s,...r}){return(0,a.jsxs)(p.l9,{"data-slot":"rich-select-trigger","data-size":t,className:(0,f.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50","data-[size=sm]:h-8 data-[size=default]:h-9 data-[size=lg]:h-14","*:data-[slot=rich-select-value]:line-clamp-1 *:data-[slot=rich-select-value]:flex *:data-[slot=rich-select-value]:items-center *:data-[slot=rich-select-value]:gap-2","[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[s,(0,a.jsx)(p.In,{asChild:!0,children:(0,a.jsx)(x.A,{className:"size-4 opacity-50"})})]})}function w({className:e,children:t,position:s="popper",...r}){return(0,a.jsx)(p.ZL,{children:(0,a.jsxs)(p.UC,{"data-slot":"rich-select-content",className:(0,f.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(y,{}),(0,a.jsx)(p.LM,{className:(0,f.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)($,{})]})})}function N({className:e,children:t,option:s,...r}){var l,o;return(0,a.jsxs)(p.q7,{"data-slot":"rich-select-item",className:(0,f.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-3 rounded-sm py-2 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s?.description&&"py-3",e),...r,children:[s?.icon&&(l=s.icon,o="size-8 shrink-0",l?"string"==typeof l?(0,a.jsx)("span",{className:(0,f.cn)(l,o)}):(0,a.jsx)("span",{className:o,children:l}):null),(0,a.jsxs)("div",{className:"flex flex-col gap-0.5 flex-1 min-w-0",children:[(0,a.jsx)(p.p4,{asChild:!0,children:(0,a.jsx)("span",{className:"text-sm font-semibold truncate",children:s?.label||t})}),s?.description&&(0,a.jsx)("span",{className:"text-xs font-medium text-muted-foreground truncate",children:s.description})]}),(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(p.VF,{children:(0,a.jsx)(h.A,{className:"size-4"})})})]})}function y({className:e,...t}){return(0,a.jsx)(p.PP,{"data-slot":"rich-select-scroll-up-button",className:(0,f.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(g.A,{className:"size-4"})})}function $({className:e,...t}){return(0,a.jsx)(p.wn,{"data-slot":"rich-select-scroll-down-button",className:(0,f.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(x.A,{className:"size-4"})})}var _=s(50533),k=s(5727),C=s(44501),A=s(99079),z=s(78087);let S={text:k.A,image:C.A,video:A.A,multimodal:z.A};function P({selectedModel:e,models:t,modelsLoading:s,modelsError:r,onModelSelect:l}){let o=(0,_.c3)("ai-dashboard.models"),{isMobile:n,isSmallMobile:d}=i(),c=e=>{let t=d?"w-6 h-6":n?"w-7 h-7":"w-8 h-8",s=e.icon?(0,a.jsx)("img",{src:e.icon,alt:e.model_name,className:`${t} rounded-full object-contain`}):(()=>{let s=S[e.model_type]||S.text;return(0,a.jsx)(s,{className:`${t} ${d?"p-1":"p-1.5"} rounded-full bg-gradient-to-r from-primary to-accent text-primary-foreground`})})();return{value:e.model_id,label:e.model_name,description:e.description||`${e.credits_per_unit} credits/${e.unit_type} `,icon:s}};return(0,a.jsxs)("div",{className:`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${d?"px-1.5 py-1":n?"px-2 py-1.5":"px-3 py-2"}`,children:["  ",(0,a.jsx)(m.J,{htmlFor:"model",className:`font-medium text-foreground ${d||n?"text-xs":"text-sm"}`,children:o("model_selector")}),s?(0,a.jsxs)("div",{className:`flex items-center justify-center bg-gradient-to-r from-accent/10 to-primary/10 rounded-md border border-border/20 ${d?"p-2 mt-1":n?"p-3 mt-1":"p-4 mt-2"}`,children:[(0,a.jsx)(u.A,{className:`animate-spin mr-2 ${d?"w-3 h-3":"w-4 h-4"}`}),"  ",(0,a.jsx)("span",{className:d?"text-xs":"text-sm",children:o("loading")})]}):r?(0,a.jsx)("div",{className:`bg-gradient-to-r from-destructive/10 to-destructive/5 rounded-md border border-destructive/20 ${d?"p-2 mt-1":n?"p-3 mt-1":"p-4 mt-2"}`,children:(0,a.jsxs)("div",{className:`text-destructive ${d?"text-xs":"text-sm"}`,children:[o("error"),": ",r]})}):(0,a.jsxs)(b,{value:e?.model_id||"",onValueChange:l,children:[(0,a.jsx)(j,{size:n?"default":"lg",className:`w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl ${d||n?"mt-1":"mt-2"}`,children:(0,a.jsx)(v,{placeholder:o("select_model"),children:e&&(0,a.jsxs)("div",{className:`flex items-center ${d?"gap-1.5":"gap-2"}`,children:["  ",c(e).icon,(0,a.jsxs)("div",{className:`flex flex-col items-start text-left min-w-0 flex-1 ${d?"gap-0":"gap-0.5"}`,children:[(0,a.jsx)("span",{className:`font-semibold truncate w-full ${d?"text-xs":"text-sm"}`,children:e.model_name}),!d&&(0,a.jsx)("span",{className:"font-medium text-muted-foreground truncate w-full text-xs",children:e.description||`${e.credits_per_unit} credits/${e.unit_type} `})]})]})})}),(0,a.jsx)(w,{className:"z-[150]",children:t.map(c).map(e=>(0,a.jsx)(N,{value:e.value,option:e},e.value))})]})]})}var R=s(164),T=s(52510),F=s(19087),I=s(67564),L=s(96700);function M({selectedModel:e,options:t,onOptionsChange:s}){let r=(0,_.c3)("ai-dashboard"),{isMobile:l,isSmallMobile:n}=i(),[d,c]=(0,o.useState)(t.uploadedImages||[]),[u,p]=(0,o.useState)(!1),[x,h]=(0,o.useState)(!1),[g,f]=(0,o.useState)(0),y=(0,o.useRef)(null),$=(0,o.useRef)(null);if(!e)return null;let{model_type:k,supported_features:C=[],provider:A}=e,z=e=>{s({...t,...e})},S=async e=>{if(!e.type.startsWith("image/"))return void alert(r("errors.invalid_input"));p(!0);try{let t=new FormData;t.append("file",e);let s=await fetch("/api/ai/upload-image",{method:"POST",body:t}),a=await s.json();if(0===a.code){let e=[...d,a.data.url];c(e),z({uploadedImages:e})}else alert(a.msg||r("errors.network_error"))}catch(e){console.error("Upload error:",e),alert(r("errors.network_error"))}finally{p(!1),y.current&&(y.current.value="")}},P=async e=>{let t=e.target.files;if(!t||0===t.length)return;let s=t[0];await S(s)},M=async e=>{e.preventDefault(),e.stopPropagation(),h(!1),f(0);let t=Array.from(e.dataTransfer.files);if(0===t.length)return;let s=t.filter(e=>e.type.startsWith("image/"));if(0===s.length)return void alert(r("errors.invalid_input"));s.length>1&&alert(`检测到 ${s.length} 个图片文件，将上传第一个：${s[0].name}`),await S(s[0])},E=e=>{let t=d.filter((t,s)=>s!==e);c(t),z({uploadedImages:t})},O=C.includes("image_upload");return(0,a.jsxs)("div",{className:`w-full max-w-full overflow-x-hidden ${n?"space-y-1":l?"space-y-1.5":"space-y-2"}`,children:[O&&(0,a.jsxs)("div",{className:`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden ${n?"px-2 py-1.5":l?"px-2.5 py-1.5":"px-3 py-2"}`,children:[(0,a.jsx)(m.J,{className:`font-medium text-foreground block ${n?"text-xs mb-1":l?"text-xs mb-1.5":"text-sm mb-2"}`,children:"video"===k?r("options.first_frame"):"image"===k?r("options.reference_image"):r("options.image_upload")}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{ref:$,onDragEnter:e=>{e.preventDefault(),e.stopPropagation(),f(e=>e+1),e.dataTransfer.types.includes("Files")&&h(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),f(e=>{let t=e-1;return 0===t&&h(!1),t})},onDragOver:e=>{e.preventDefault(),e.stopPropagation(),e.dataTransfer.types.includes("Files")&&(e.dataTransfer.dropEffect="copy")},onDrop:M,className:`
                relative border-2 border-dashed rounded-xl p-6 transition-all duration-300 cursor-pointer
                ${x?"border-primary bg-primary/10 scale-[1.02] shadow-lg shadow-primary/20":"border-border/50 hover:border-border hover:bg-muted/20"}
                ${u?"pointer-events-none opacity-50":""}
                group
              `,onClick:()=>!u&&y.current?.click(),children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-3 text-center",children:[(0,a.jsx)("div",{className:`
                  p-3 rounded-full transition-all duration-300
                  ${x?"bg-primary text-primary-foreground scale-110 animate-pulse":"bg-muted group-hover:bg-muted/80"}
                `,children:(0,a.jsx)(F.A,{className:`w-6 h-6 transition-transform duration-300 ${x?"scale-110":"group-hover:scale-105"}`})}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:`text-sm font-medium transition-colors duration-300 ${x?"text-primary":"text-foreground"}`,children:u?r("options.uploading"):x?r("options.drop_to_upload"):r("options.drag_drop")}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:r("options.supported_formats")}),x&&(0,a.jsx)("p",{className:"text-xs text-primary font-medium animate-bounce",children:r("options.file_detected")})]})]}),(0,a.jsx)("input",{ref:y,type:"file",accept:"image/*",onChange:P,className:"hidden"})]}),d.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:d.map((e,t)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("img",{src:e,alt:`${r("options.image_upload")} ${t+1}`,className:"w-full h-20 object-cover rounded-lg border border-border/50"}),(0,a.jsx)(T.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>E(t),className:"absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(I.A,{className:"w-3 h-3"})})]},t))})]})]}),"text"===k&&(0,a.jsx)("div",{className:"px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"max_tokens",className:"text-sm font-medium text-foreground",children:r("options.max_tokens")}),(0,a.jsx)(R.p,{id:"max_tokens",type:"number",placeholder:"1000",value:t.max_tokens||"",onChange:e=>z({max_tokens:parseInt(e.target.value)||1e3}),className:"mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"temperature",className:"text-sm font-medium text-foreground",children:r("options.temperature")}),(0,a.jsx)(R.p,{id:"temperature",type:"number",step:"0.1",min:"0",max:"1",placeholder:"0.7",value:t.temperature||"",onChange:e=>z({temperature:parseFloat(e.target.value)||.7}),className:"mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"})]})]})}),"image"===k&&(0,a.jsxs)("div",{className:"px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full",children:[C.includes("variants")&&(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"variants",className:"text-sm font-medium text-foreground",children:r("options.variants")}),(0,a.jsxs)(b,{value:t.variants?.toString()||"1",onValueChange:e=>z({variants:parseInt(e)}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"1",option:{value:"1",label:r("options.1_image"),description:r("options.generate_1")},children:r("options.1_image")}),(0,a.jsx)(N,{value:"2",option:{value:"2",label:r("options.2_images"),description:r("options.generate_2")},children:r("options.2_images")})]})]})]}),(C.includes("aspectRatio")||C.includes("size"))&&(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"size",className:"text-sm font-medium text-foreground",children:r("options.image_size")}),(0,a.jsxs)(b,{value:t.size||t.aspectRatio||"1:1",onValueChange:e=>z({size:e,aspectRatio:e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"1:1",option:{value:"1:1",label:r("options.square"),description:"1:1"},children:r("options.square_ratio")}),(0,a.jsx)(N,{value:"16:9",option:{value:"16:9",label:r("options.landscape"),description:"16:9"},children:r("options.landscape_ratio")}),(0,a.jsx)(N,{value:"9:16",option:{value:"9:16",label:r("options.portrait"),description:"9:16"},children:r("options.portrait_ratio")}),(0,a.jsx)(N,{value:"4:3",option:{value:"4:3",label:"Standard",description:"4:3"},children:"Standard (4:3)"}),(0,a.jsx)(N,{value:"3:2",option:{value:"3:2",label:"Photo",description:"3:2"},children:"Photo (3:2)"})]})]})]})]}),"replicate"===A&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-border/30",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-foreground mb-3",children:"Replicate Advanced Options"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"aspect_ratio",className:"text-sm font-medium text-foreground",children:"Aspect Ratio"}),(0,a.jsxs)(b,{value:t.aspectRatio||"1:1",onValueChange:e=>z({aspectRatio:e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"1:1",option:{value:"1:1",label:"1:1 Square",description:"Perfect for social media posts"},children:"1:1 Square"}),(0,a.jsx)(N,{value:"16:9",option:{value:"16:9",label:"16:9 Landscape",description:"Widescreen format"},children:"16:9 Landscape"}),(0,a.jsx)(N,{value:"21:9",option:{value:"21:9",label:"21:9 Ultra Wide",description:"Cinematic format"},children:"21:9 Ultra Wide"}),(0,a.jsx)(N,{value:"3:2",option:{value:"3:2",label:"3:2 Photo",description:"Classic photo ratio"},children:"3:2 Photo"}),(0,a.jsx)(N,{value:"2:3",option:{value:"2:3",label:"2:3 Portrait Photo",description:"Vertical photo ratio"},children:"2:3 Portrait Photo"}),(0,a.jsx)(N,{value:"4:5",option:{value:"4:5",label:"4:5 Instagram",description:"Instagram portrait"},children:"4:5 Instagram"}),(0,a.jsx)(N,{value:"5:4",option:{value:"5:4",label:"5:4 Classic",description:"Classic landscape"},children:"5:4 Classic"}),(0,a.jsx)(N,{value:"3:4",option:{value:"3:4",label:"3:4 Portrait",description:"Standard portrait"},children:"3:4 Portrait"}),(0,a.jsx)(N,{value:"4:3",option:{value:"4:3",label:"4:3 Standard",description:"Standard landscape"},children:"4:3 Standard"}),(0,a.jsx)(N,{value:"9:16",option:{value:"9:16",label:"9:16 Mobile",description:"Mobile portrait"},children:"9:16 Mobile"}),(0,a.jsx)(N,{value:"9:21",option:{value:"9:21",label:"9:21 Ultra Tall",description:"Ultra tall portrait"},children:"9:21 Ultra Tall"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"num_outputs",className:"text-sm font-medium text-foreground",children:"Number of Outputs"}),(0,a.jsxs)(b,{value:(t.variants||1).toString(),onValueChange:e=>z({variants:parseInt(e)}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"1",option:{value:"1",label:"1 Image",description:"Generate 1 image"},children:"1 Image"}),(0,a.jsx)(N,{value:"2",option:{value:"2",label:"2 Images",description:"Generate 2 images"},children:"2 Images"}),(0,a.jsx)(N,{value:"3",option:{value:"3",label:"3 Images",description:"Generate 3 images"},children:"3 Images"}),(0,a.jsx)(N,{value:"4",option:{value:"4",label:"4 Images",description:"Generate 4 images"},children:"4 Images"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.J,{htmlFor:"output_quality",className:"text-sm font-medium text-foreground",children:"Output Quality"}),(0,a.jsx)("div",{className:"cursor-help",title:"Quality when saving the output images, from 0 to 100. 100 is best quality, 0 is lowest quality. Not relevant for .png outputs",children:(0,a.jsx)(L.A,{className:"w-3 h-3 text-muted-foreground"})})]}),(0,a.jsx)(R.p,{id:"output_quality",type:"number",min:"0",max:"100",value:t.output_quality||80,onChange:e=>z({output_quality:parseInt(e.target.value)}),className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"output_format",className:"text-sm font-medium text-foreground",children:"Output Format"}),(0,a.jsxs)(b,{value:t.output_format||"webp",onValueChange:e=>z({output_format:e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"webp",option:{value:"webp",label:"WebP",description:"Modern format with high compression"},children:"WebP"}),(0,a.jsx)(N,{value:"jpg",option:{value:"jpg",label:"JPG",description:"Universal format, good compression"},children:"JPG"}),(0,a.jsx)(N,{value:"png",option:{value:"png",label:"PNG",description:"Lossless compression, larger files"},children:"PNG"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.J,{htmlFor:"guidance",className:"text-sm font-medium text-foreground",children:"Guidance"}),(0,a.jsx)("div",{className:"cursor-help",title:"Guidance for generated image. Higher values follow the prompt more closely",children:(0,a.jsx)(L.A,{className:"w-3 h-3 text-muted-foreground"})})]}),(0,a.jsx)(R.p,{id:"guidance",type:"number",min:"0",max:"10",step:"0.1",value:t.guidance||3,onChange:e=>z({guidance:parseFloat(e.target.value)}),className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.J,{htmlFor:"num_inference_steps",className:"text-sm font-medium text-foreground",children:"Inference Steps"}),(0,a.jsx)("div",{className:"cursor-help",title:"Number of denoising steps. Recommended range is 28-50. Lower steps = faster but lower quality",children:(0,a.jsx)(L.A,{className:"w-3 h-3 text-muted-foreground"})})]}),(0,a.jsx)(R.p,{id:"num_inference_steps",type:"number",min:"1",max:"50",value:t.num_inference_steps||28,onChange:e=>z({num_inference_steps:parseInt(e.target.value)}),className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.J,{htmlFor:"seed",className:"text-sm font-medium text-foreground",children:"Seed"}),(0,a.jsx)("div",{className:"cursor-help",title:"Random seed. Set for reproducible generation",children:(0,a.jsx)(L.A,{className:"w-3 h-3 text-muted-foreground"})})]}),(0,a.jsx)(R.p,{id:"seed",type:"number",value:t.seed||"",onChange:e=>z({seed:e.target.value?parseInt(e.target.value):void 0}),placeholder:"Random if empty",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.J,{htmlFor:"prompt_strength",className:"text-sm font-medium text-foreground",children:"Prompt Strength"}),(0,a.jsx)("div",{className:"cursor-help",title:"Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image",children:(0,a.jsx)(L.A,{className:"w-3 h-3 text-muted-foreground"})})]}),(0,a.jsx)(R.p,{id:"prompt_strength",type:"number",min:"0",max:"1",step:"0.1",value:t.prompt_strength||.8,onChange:e=>z({prompt_strength:parseFloat(e.target.value)}),className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"megapixels",className:"text-sm font-medium text-foreground",children:"Image Size"}),(0,a.jsxs)(b,{value:t.megapixels||"1",onValueChange:e=>z({megapixels:e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"1",option:{value:"1",label:"1 Megapixel",description:"Standard quality, balanced speed"},children:"1 Megapixel"}),(0,a.jsx)(N,{value:"0.25",option:{value:"0.25",label:"0.25 Megapixel",description:"Faster generation, lower resolution"},children:"0.25 Megapixel"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"go_fast",className:"text-sm font-medium text-foreground",children:"Go Fast"}),(0,a.jsxs)(b,{value:!1!==t.go_fast?"true":"false",onValueChange:e=>z({go_fast:"true"===e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"true",option:{value:"true",label:"Enabled",description:"Faster predictions with fp8 quantization"},children:"Enabled (Faster)"}),(0,a.jsx)(N,{value:"false",option:{value:"false",label:"Disabled",description:"Original bf16 precision, deterministic"},children:"Disabled (Precise)"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"disable_safety_checker",className:"text-sm font-medium text-foreground",children:"Safety Checker"}),(0,a.jsxs)(b,{value:t.disable_safety_checker?"disabled":"enabled",onValueChange:e=>z({disable_safety_checker:"disabled"===e}),children:[(0,a.jsx)(j,{size:"lg",className:"mt-1 w-full",children:(0,a.jsx)(v,{})}),(0,a.jsxs)(w,{className:"z-[150]",children:[(0,a.jsx)(N,{value:"enabled",option:{value:"enabled",label:"Enabled",description:"Safety checker is active"},children:"Enabled"}),(0,a.jsx)(N,{value:"disabled",option:{value:"disabled",label:"Disabled",description:"Disable safety checker for generated images"},children:"Disabled"})]})]})]})]})]})]})]})}var E=s(7330),O=s(56421);function U({costEstimate:e}){let t=(0,_.c3)("ai-dashboard.cost"),{isMobile:s,isSmallMobile:r}=i();return e?(0,a.jsxs)(E.Fc,{className:`bg-gradient-to-r from-accent/10 to-primary/10 border-border/30 backdrop-blur-sm w-full max-w-full ${r?"p-2":s?"p-3":"p-4"}`,children:["  ",(0,a.jsx)(O.A,{className:`${r?"h-3 w-3":"h-4 w-4"}`}),"  ",(0,a.jsxs)(E.TN,{className:r?"text-xs":"text-sm",children:["  ",t("estimated"),": ",e.cost_estimate.estimated_credits," ",t("credits"),!e.user_credits.can_afford&&(0,a.jsxs)("span",{className:`text-destructive ${r?"ml-1":"ml-2"}`,children:["  ","(",t("not_enough",{shortfall:e.user_credits.shortfall??0}),")"]})]})]}):null}function J({userCredits:e}){let t=(0,_.c3)("ai-dashboard.credits"),{isMobile:s,isSmallMobile:r}=i();return(0,a.jsxs)("div",{className:`flex items-center text-muted-foreground bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30 w-full max-w-full ${r?"gap-1 p-1.5":s?"gap-2 p-2":"gap-3 p-3"}`,children:["  ",(0,a.jsx)("div",{children:(0,a.jsxs)("span",{className:`font-medium text-foreground ${r||s?"text-xs":"text-sm"}`,children:[r?`${e} ${t("credits")}`:`${t("current_balance")}: ${e}`,"  "]})})]})}function D({loading:e,selectedModel:t,prompt:s,costEstimate:r,onGenerate:o}){let{isMobile:n,isSmallMobile:d}=i(),{className:m}=c({},"button"),p=(0,_.c3)("ai-dashboard.generator"),x=e||!t||!s.trim()||!!r&&!r.user_credits.can_afford;return(0,a.jsx)(T.$,{onClick:o,disabled:x,className:`${m} w-full max-w-full font-semibold rounded-xl`,size:d||n?"default":"lg",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:`${d?"w-3 h-3":n?"w-4 h-4":"w-5 h-5"} ${d?"mr-1":"mr-2"} animate-spin`}),"  ",(0,a.jsxs)("span",{className:d?"text-xs":void 0,children:["  ",d?p("generating").slice(0,6)+"...":p("generating"),"  "]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:`${d?"w-3 h-3":n?"w-4 h-4":"w-5 h-5"} ${d?"mr-1":"mr-2"}`}),"  ",(0,a.jsxs)("span",{className:d?"text-xs":void 0,children:["  ",d?p("start").slice(0,4):p("start"),"  "]})]})})}var G=s(91624);function W({prompt:e,onPromptChange:t}){let s=(0,_.c3)("ai-dashboard.generator"),{isMobile:r,isSmallMobile:l}=i();return(0,a.jsxs)("div",{className:`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${l?"px-1.5 py-1":r?"px-2 py-1.5":"px-3 py-2"}`,children:["  ",(0,a.jsx)(m.J,{htmlFor:"prompt",className:`font-medium text-foreground ${l||r?"text-xs":"text-sm"}`,children:s("prompt_input")}),(0,a.jsx)(G.T,{id:"prompt",placeholder:s("prompt_placeholder"),value:e,onChange:e=>t(e.target.value),rows:l?2:r?3:4,className:`bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border resize-none rounded-xl w-full max-w-full ${l?"mt-0.5 text-xs":r?"mt-1 text-sm":"mt-2 text-base"}`})]})}var q=s(95234),V=s(54918),B=s(95616),X=s(57210);function H({onExitFullscreen:e}){let t=(0,_.c3)("ai-dashboard"),{isMobile:s}=i();return(0,a.jsx)("div",{className:`sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm ${s?"px-0 py-1":"px-6 py-4"}`,children:(0,a.jsxs)("div",{className:`flex items-center justify-between ${s?"px-2":""}`,children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 md:gap-4",children:["  ",(0,a.jsx)("div",{className:`rounded-xl bg-gradient-to-r from-primary to-accent ${s?"p-1.5":"p-2"}`,children:(0,a.jsx)(B.A,{className:`text-primary-foreground ${s?"w-4 h-4":"w-6 h-6"}`})}),(0,a.jsxs)("h1",{className:`font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${s?"text-sm":"text-xl"}`,children:[s?t("workspace.title"):`${t("workspace.title")} - ${t("workspace.fullscreen")}`,"  "]})]}),(0,a.jsxs)(T.$,{variant:"outline",size:"sm",onClick:e,className:"flex items-center gap-1 md:gap-2 z-50 relative",children:[(0,a.jsx)(X.A,{className:"w-4 h-4"}),!s&&t("toolbar.exit_fullscreen"),"  "]})]})})}var Q=s(29444);function Z({onEnterFullscreen:e}){let t=(0,_.c3)("ai-dashboard.workspace"),{isMobile:s,isSmallMobile:r}=i();return(0,a.jsx)("div",{className:`bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm ${r?"px-3 py-4":s?"px-4 py-6":"px-6 py-12"}`,children:(0,a.jsxs)("div",{className:`flex items-center justify-between ${r?"mb-3":s?"mb-4":"mb-8"}`,children:[(0,a.jsxs)("div",{className:`flex items-center ${r?"gap-2":s?"gap-3":"gap-4"}`,children:[(0,a.jsx)("div",{className:`rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ${r?"p-1.5":s?"p-2":"p-3"}`,children:(0,a.jsx)(B.A,{className:`text-primary-foreground ${r?"w-5 h-5":s?"w-6 h-6":"w-8 h-8"}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:`font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${r?"text-lg":s?"text-xl":"text-3xl"}`,children:t("title")}),!r&&(0,a.jsx)("p",{className:`text-muted-foreground ${s?"mt-0.5 text-sm":"mt-1 text-base"}`,children:t("subtitle")})]})]}),(0,a.jsxs)(T.$,{variant:"outline",size:r?"sm":s?"default":"lg",onClick:e,className:`flex items-center ${r?"gap-1":"gap-2"}`,children:[(0,a.jsx)(Q.A,{className:`${r?"w-3 h-3":s?"w-4 h-4":"w-5 h-5"}`}),!r&&t("fullscreen"),"  "]})]})})}function Y({activeTab:e,onTabChange:t,isFullscreen:s,isDesktop:r,typeButtonClassName:l}){let o=(0,_.c3)("ai-dashboard.tabs"),i=[{value:"text",label:o("text"),icon:k.A,color:"from-blue-500 to-purple-500"},{value:"image",label:o("image"),icon:C.A,color:"from-green-500 to-teal-500"},{value:"video",label:o("video"),icon:A.A,color:"from-orange-500 to-red-500"}];return(0,a.jsx)("div",{className:"space-y-3",children:i.map(({value:o,label:i,icon:n,color:d})=>(0,a.jsxs)(T.$,{variant:e===o?"default":"ghost",className:`${l} transition-all duration-200 ${e===o?`bg-gradient-to-r ${d} text-white shadow-lg`:"hover:bg-accent/50"}`,onClick:()=>t(o),children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${e===o?"bg-white/20":"bg-accent/20"}`,children:(0,a.jsx)(n,{className:"w-4 h-4"})}),(!r||!s)&&(0,a.jsx)("span",{className:"font-medium",children:i}),s&&r&&(0,a.jsx)("span",{className:"text-xs font-medium text-center leading-tight",children:i.replace("Generate","")})]},o))})}var K=s(94670);function ee({activeTab:e,onTabChange:t}){let s=(0,_.c3)("ai-dashboard.tabs"),{isMobile:r,isSmallMobile:l}=i();return(0,a.jsx)(K.tU,{value:e,onValueChange:t,className:"w-full max-w-full overflow-x-hidden",children:(0,a.jsxs)(K.j7,{className:`grid w-full max-w-full grid-cols-3 bg-gradient-to-r from-muted/50 to-muted/30 ${l?"mb-1 h-7":r?"mb-1 h-8":"mb-6 h-10"}`,children:[(0,a.jsxs)(K.Xi,{value:"text",className:`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white ${l?"gap-1 text-xs px-1":r?"gap-1.5 text-sm px-2":"gap-2 text-base px-3"}`,children:[(0,a.jsx)(k.A,{className:`${l?"w-3 h-3":r?"w-3.5 h-3.5":"w-4 h-4"}`}),l?s("text").slice(0,3):s("text"),"  "]}),(0,a.jsxs)(K.Xi,{value:"image",className:`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white ${l?"gap-1 text-xs px-1":r?"gap-1.5 text-sm px-2":"gap-2 text-base px-3"}`,children:[(0,a.jsx)(C.A,{className:`${l?"w-3 h-3":r?"w-3.5 h-3.5":"w-4 h-4"}`}),l?s("image").slice(0,3):s("image"),"  "]}),(0,a.jsxs)(K.Xi,{value:"video",className:`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white ${l?"gap-1 text-xs px-1":r?"gap-1.5 text-sm px-2":"gap-2 text-base px-3"}`,children:[(0,a.jsx)(A.A,{className:`${l?"w-3 h-3":r?"w-3.5 h-3.5":"w-4 h-4"}`}),l?s("video").slice(0,3):s("video"),"  "]})]})})}var et=s(13109);let es=o.forwardRef(({className:e,value:t,...s},r)=>(0,a.jsx)(et.bL,{ref:r,className:(0,f.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,a.jsx)(et.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));es.displayName=et.bL.displayName;var ea=s(93408),er=s(57565),el=s(35406),eo=s(65842),ei=s(41418),en=s(83788),ed=s(16769),ec=s(19524);function em({isOpen:e,onClose:t,imageUrl:s,imageAlt:r,onDownload:l}){let i=(0,_.c3)("ai-dashboard"),[n,d]=(0,o.useState)(1),[c,m]=(0,o.useState)(0),u=()=>{d(1),m(0)},p=()=>{u(),t()};return(0,a.jsx)(ei.lG,{open:e,onOpenChange:p,children:(0,a.jsxs)(ei.Cf,{className:"max-w-[95vw] max-h-[95vh] w-auto h-auto p-0 bg-black/95 border-border/30",children:[(0,a.jsx)(ei.c7,{className:"absolute top-0 left-0 right-0 z-10 p-4 bg-gradient-to-b from-black/80 to-transparent",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.L3,{className:"text-white text-lg font-semibold truncate",children:r}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.$,{size:"sm",variant:"ghost",onClick:()=>{d(e=>Math.max(e-.25,.25))},className:"text-white hover:bg-white/20",disabled:n<=.25,children:(0,a.jsx)(en.A,{className:"w-4 h-4"})}),(0,a.jsxs)("span",{className:"text-white text-sm min-w-[3rem] text-center",children:[Math.round(100*n),"%"]}),(0,a.jsx)(T.$,{size:"sm",variant:"ghost",onClick:()=>{d(e=>Math.min(e+.25,3))},className:"text-white hover:bg-white/20",disabled:n>=3,children:(0,a.jsx)(ed.A,{className:"w-4 h-4"})}),(0,a.jsx)(T.$,{size:"sm",variant:"ghost",onClick:()=>{m(e=>(e+90)%360)},className:"text-white hover:bg-white/20",children:(0,a.jsx)(ec.A,{className:"w-4 h-4"})}),(0,a.jsx)(T.$,{size:"sm",variant:"ghost",onClick:u,className:"text-white hover:bg-white/20",children:"重置"}),(0,a.jsxs)(T.$,{size:"sm",variant:"default",onClick:l,className:"bg-primary hover:bg-primary/90",children:[(0,a.jsx)(el.A,{className:"w-4 h-4 mr-2"}),i("actions.download")]}),(0,a.jsx)(T.$,{size:"sm",variant:"ghost",onClick:p,className:"text-white hover:bg-white/20",children:(0,a.jsx)(I.A,{className:"w-4 h-4"})})]})]})}),(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[50vh] max-h-[95vh] overflow-hidden",children:(0,a.jsx)("img",{src:s,alt:r,className:"max-w-full max-h-full object-contain transition-transform duration-200 ease-out",style:{transform:`scale(${n}) rotate(${c}deg)`,cursor:n>1?"grab":"default"},draggable:!1})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"快捷键：ESC 关闭 • +/- 缩放 • R 旋转 • 0 重置"})})})]})})}function eu(e,t="download"){try{let s=new URL(e).pathname.split("/").pop();if(s&&s.includes("."))return s;let a=function(e){try{let t=new URL(e).pathname,s=t.split(".").pop()?.toLowerCase();if(s&&["jpg","jpeg","png","gif","webp","svg","bmp"].includes(s))return s;return"jpg"}catch(e){return"jpg"}}(e);return`${t}.${a}`}catch(e){return`${t}.jpg`}}async function ep(e,t,s){try{let s=t||eu(e,"generated-file");console.log(`[Download] Starting proxy download: ${e} -> ${s}`);let a=await fetch("/api/download-proxy",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:e,fileName:s})});if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);let r=await a.blob(),l=URL.createObjectURL(r),o=document.createElement("a");return o.href=l,o.download=s,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(l),console.log(`[Download] Successfully downloaded via proxy: ${s}`),{success:!0}}catch(e){return console.error("[Download] Failed to download file via proxy:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}function ex({generationResult:e}){let t=(0,_.c3)("ai-dashboard"),[s,r]=(0,o.useState)(null),[l,i]=(0,o.useState)(new Set),[n,d]=(0,o.useState)(!1),c=(e,t)=>{r({url:e,alt:t})},m=async(e,t)=>{i(t=>new Set(t).add(e));try{let s=`generated-image${void 0!==t?`-${t+1}`:""}.jpg`,a=await ep(e,s);a.success?q.oR.success("图片下载成功"):q.oR.error(`下载失败: ${a.error}`)}catch(e){q.oR.error("下载失败，请重试")}finally{i(t=>{let s=new Set(t);return s.delete(e),s})}},p=async e=>{d(!0);try{let t=await ep(e,"generated-video.mp4");t.success?q.oR.success("视频下载成功"):q.oR.error(`下载失败: ${t.error}`)}catch(e){q.oR.error("下载失败，请重试")}finally{d(!1)}};return e?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30",children:[(0,a.jsxs)("div",{className:`p-2 rounded-lg ${"success"===e.status?"bg-gradient-to-r from-green-500 to-emerald-500":"failed"===e.status?"bg-gradient-to-r from-red-500 to-rose-500":"bg-gradient-to-r from-blue-500 to-purple-500"}`,children:["success"===e.status&&(0,a.jsx)(ea.A,{className:"w-5 h-5 text-white"}),"failed"===e.status&&(0,a.jsx)(O.A,{className:"w-5 h-5 text-white"}),("pending"===e.status||"running"===e.status)&&(0,a.jsx)(u.A,{className:"w-5 h-5 animate-spin text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-semibold text-foreground",children:["success"===e.status&&"生成完成","failed"===e.status&&"生成失败",("pending"===e.status||"running"===e.status)&&"生成中..."]}),void 0!==e.progress&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["进度: ",e.progress,"%"]}),(0,a.jsx)(es,{value:e.progress,className:"w-full"})]})]})]}),"success"===e.status&&e.result&&(0,a.jsxs)("div",{className:"space-y-4",children:[e.result.text&&(0,a.jsx)("div",{className:"p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm leading-relaxed text-foreground",children:e.result.text})}),e.result.images&&(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4",children:e.result.images.map((e,s)=>{let r=l.has(e.url),o=`Generated image ${s+1}`;return(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30",children:[(0,a.jsxs)("div",{className:"relative cursor-pointer group",onClick:()=>c(e.url,o),children:[(0,a.jsx)("img",{src:e.url,alt:o,className:"w-full rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group-hover:scale-[1.02]"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-xl flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-3",children:(0,a.jsx)(er.A,{className:"w-6 h-6 text-gray-800"})})})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(T.$,{size:"sm",variant:"outline",className:"flex-1",onClick:()=>c(e.url,o),children:[(0,a.jsx)(er.A,{className:"w-4 h-4 mr-2"}),t("actions.view")]}),(0,a.jsxs)(T.$,{size:"sm",variant:"default",className:"flex-1",onClick:()=>m(e.url,s),disabled:r,children:[r?(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,a.jsx)(el.A,{className:"w-4 h-4 mr-2"}),r?"下载中...":t("actions.download")]})]})]},s)})}),e.result.video&&(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30",children:[(0,a.jsx)("video",{src:e.result.video.url,controls:!0,className:"w-full rounded-xl shadow-lg"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(T.$,{size:"sm",variant:"outline",className:"flex-1",onClick:()=>window.open(e.result?.video?.url,"_blank"),children:[(0,a.jsx)(er.A,{className:"w-4 h-4 mr-2"}),"在新窗口查看"]}),(0,a.jsxs)(T.$,{size:"sm",variant:"default",className:"flex-1",onClick:()=>e.result?.video?.url&&p(e.result.video.url),disabled:n,children:[n?(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,a.jsx)(el.A,{className:"w-4 h-4 mr-2"}),n?"下载中...":"下载视频"]})]})]}),e.usage&&(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500",children:(0,a.jsx)(eo.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-foreground",children:t("cost.consumed",{amount:e.usage.credits_consumed})})]})]}),"failed"===e.status&&e.error&&(0,a.jsxs)(E.Fc,{variant:"destructive",children:[(0,a.jsx)(O.A,{className:"h-4 w-4"}),(0,a.jsx)(E.TN,{children:t("errors.generation_failed",{detail:e.error.detail})})]}),("pending"===e.status||"running"===e.status)&&(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(u.A,{className:"w-8 h-8 animate-spin mx-auto mb-2"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Generating... please wait...",void 0!==e.progress&&` (${e.progress}%)`]})]}),s&&(0,a.jsx)(em,{isOpen:!!s,onClose:()=>r(null),imageUrl:s.url,imageAlt:s.alt,onDownload:()=>m(s.url)})]}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto",children:(0,a.jsx)(B.A,{className:"w-8 h-8 text-primary-foreground"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:t("workspace.start_create")}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:t("workspace.choose_model")})]})})}function eh({activeTab:e,generationResult:t,isFullscreen:s,isMobile:l}){let o=(0,_.c3)("ai-dashboard.results");return(0,a.jsxs)(r.Zp,{className:`${s?"h-full max-h-full overflow-hidden":l?"min-h-[40vh]":"h-full"} flex flex-col bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full`,style:s?{height:"100%",maxHeight:"100%"}:void 0,children:[(0,a.jsxs)(r.aR,{className:`border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 flex-shrink-0 ${l?"px-2 py-1":"px-6 py-4"}`,children:[(0,a.jsxs)(r.ZB,{className:`flex items-center ${l?"gap-2":"gap-3"}`,children:["  ",(0,a.jsxs)("div",{className:`rounded-lg ${l?"p-1.5":"p-2"} ${"text"===e?"bg-gradient-to-r from-blue-500 to-purple-500":"image"===e?"bg-gradient-to-r from-green-500 to-teal-500":"bg-gradient-to-r from-orange-500 to-red-500"}`,children:["text"===e&&(0,a.jsx)(k.A,{className:`text-white ${l?"w-4 h-4":"w-5 h-5"}`}),"  ","image"===e&&(0,a.jsx)(C.A,{className:`text-white ${l?"w-4 h-4":"w-5 h-5"}`}),"video"===e&&(0,a.jsx)(A.A,{className:`text-white ${l?"w-4 h-4":"w-5 h-5"}`})]}),(0,a.jsxs)("span",{className:`bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${l?"text-sm font-medium":"text-base font-semibold"}`,children:[l?o("result_ready"):o("result_ready").toUpperCase(),"  "]})]}),!l&&(0,a.jsxs)(r.BT,{className:"text-muted-foreground/80",children:["text"===e&&o("text_description"),"image"===e&&o("image_description"),"video"===e&&o("video_description")]})]}),(0,a.jsx)(r.Wu,{className:`flex-1 min-h-0 ${l?"p-1":"p-6"} ${s?"overflow-y-auto overflow-x-hidden":"overflow-y-auto"}`,style:s?{height:l?"calc(100% - 50px)":"calc(100% - 120px)",maxHeight:l?"calc(100% - 50px)":"calc(100% - 120px)",overflowY:"auto",overflowX:"hidden"}:void 0,children:(0,a.jsx)(ex,{generationResult:t})})]})}function eg({modelType:e,onResultChange:t,onGeneratingChange:s,isFullscreen:n}={}){let{selectedModel:d,models:m,modelsLoading:u,modelsError:p,prompt:x,setPrompt:h,options:g,setOptions:f,loading:b,costEstimate:v,userCredits:j,handleGenerate:w,handleModelSelect:N}=function(e,t,s){let a=(0,_.c3)("ai-dashboard");(0,V.Ym)();let[r,l]=(0,o.useState)(null),[i,n]=(0,o.useState)([]),[d,c]=(0,o.useState)([]),[m,u]=(0,o.useState)(!0),[p,x]=(0,o.useState)(null),[h,g]=(0,o.useState)(""),[f,b]=(0,o.useState)({size:"1:1",aspectRatio:"1:1",variants:1,temperature:.7,max_tokens:1e3,cdn:"global",uploadedImages:[],referenceImages:[]}),[v,j]=(0,o.useState)(!1),[w,N]=(0,o.useState)(null),[y,$]=(0,o.useState)(0),k=async()=>{try{let e=await fetch("/api/get-user-info",{method:"POST",headers:{"Content-Type":"application/json"}}),t=await e.json();0===t.code&&$(t.data.credits?.left_credits||0)}catch(e){console.error("Failed to fetch user credits:",e)}},C=async()=>{if(!r||!h.trim())return void q.oR.error(a("errors.invalid_input"));if(w&&!w.user_credits.can_afford)return void q.oR.error(a("errors.insufficient_credits"));j(!0),s?.(!0),t?.(null);try{let e=await fetch("/api/ai/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:r.model_id,type:r.model_type,prompt:h,options:f})}),l=await e.json();0===l.code?"pending"===l.data.status||"running"===l.data.status?(t?.(l.data),A(l.data.id)):(t?.(l.data),q.oR.success(a("status.success")),k(),s?.(!1)):(q.oR.error(l.msg||a("errors.generation_failed",{detail:"Unknown error"})),s?.(!1))}catch(e){q.oR.error(a("errors.network_error")),s?.(!1)}finally{j(!1)}},A=async e=>{let r=0,l=async()=>{try{let o=await fetch("/api/ai/result",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({request_id:e})}),i=await o.json();if(0===i.code){if(t?.(i.data),"success"===i.data.status){q.oR.success(a("status.success")),k(),s?.(!1);return}if("failed"===i.data.status){q.oR.error(a("errors.generation_failed",{detail:i.data.error?.detail||"Unknown error"})),s?.(!1);return}}++r<60?setTimeout(l,5e3):q.oR.error(a("errors.network_error"))}catch(e){console.error("Polling error:",e),++r<60&&setTimeout(l,5e3)}};l()};return{selectedModel:r,models:d,modelsLoading:m,modelsError:p,prompt:h,setPrompt:g,options:f,setOptions:b,loading:v,costEstimate:w,userCredits:y,handleGenerate:C,handleModelSelect:e=>{let t=d.find(t=>t.model_id===e);t&&l(t)},fetchUserCredits:k}}(0,t,s),{isMobile:y}=i(),{className:$}=c({},"card");return(0,a.jsxs)("div",{className:`w-full max-w-full ${n?"h-full overflow-x-hidden":""} ${y?"space-y-1":"space-y-6"}`,children:["  ",(0,a.jsxs)(r.Zp,{className:`${$} bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full ${n?"overflow-x-hidden overflow-y-hidden gap-0":""}`,children:[(0,a.jsx)(r.aR,{className:`border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 ${y?"pb-1 px-2 pt-2":"pb-4 px-6 pt-6"}`,children:(0,a.jsxs)(r.ZB,{className:`flex items-center ${y?"gap-2":"gap-3"}`,children:["  ",(0,a.jsx)("div",{className:`rounded-lg bg-gradient-to-r from-primary to-accent ${y?"p-1.5":"p-2"}`,children:(0,a.jsx)(l.A,{className:`text-primary-foreground ${y?"w-4 h-4":"w-5 h-5"}`})}),(0,a.jsxs)("span",{className:`bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${y?"text-sm font-medium":"text-base font-semibold"}`,children:[y?"AI CONFIG":"AI GENERATION CONFIG","  "]})]})}),(0,a.jsxs)(r.Wu,{className:`w-full max-w-full ${n?"overflow-x-hidden":""} ${y?"space-y-1 px-2 py-1":"space-y-4 px-6 py-4"}`,children:[(0,a.jsx)(P,{selectedModel:d,models:m,modelsLoading:u,modelsError:p,onModelSelect:N}),(0,a.jsx)(W,{prompt:x,onPromptChange:h}),(0,a.jsx)(M,{selectedModel:d,options:g,onOptionsChange:f}),(0,a.jsx)(U,{costEstimate:v}),(0,a.jsx)(J,{userCredits:j}),(0,a.jsx)(D,{loading:b,selectedModel:d,prompt:x,costEstimate:v,onGenerate:w})]})]})]})}var ef=s(3124);function eb(){let{user:e}=(0,ef.U)();if(!e)return null;let{activeTab:t,generationResult:s,handleTabChange:r,setGenerationResult:l,setIsGenerating:n}=function(){let e=process.env.NEXT_PUBLIC_DEFAULT_MODEL_TYPE||"image",[t,s]=(0,o.useState)(e),[a,r]=(0,o.useState)(null),[l,i]=(0,o.useState)(!1);return{activeTab:t,generationResult:a,isGenerating:l,handleTabChange:e=>{s(e),r(null),i(!1)},setGenerationResult:r,setIsGenerating:i}}(),{isFullscreen:d,moduleRef:m,handleFullscreenToggle:u}=function(){let{isFullscreen:e,toggleFullscreen:t}=function(){let[e,t]=(0,o.useState)(!1),s=(0,o.useCallback)(e=>{e&&(console.log("进入全屏模式",e),e.classList.add("ai-module-fullscreen"),e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.width="100vw",e.style.height="100vh",e.style.zIndex="9999",e.style.backgroundColor="white",e.style.overflow="auto",e.style.padding="0",e.style.margin="0",e.style.maxWidth="none",document.body.style.overflow="hidden",t(!0),console.log("CSS类已添加:",e.classList.contains("ai-module-fullscreen")),console.log("内联样式已设置"))},[]),a=(0,o.useCallback)(e=>{e&&(console.log("退出全屏模式",e),e.classList.remove("ai-module-fullscreen"),e.style.position="",e.style.top="",e.style.left="",e.style.width="",e.style.height="",e.style.zIndex="",e.style.backgroundColor="",e.style.overflow="",e.style.padding="",e.style.margin="",e.style.maxWidth="",document.body.style.overflow="",t(!1),console.log("全屏模式已退出"))},[]),r=(0,o.useCallback)(t=>{e?a(t):s(t)},[e,s,a]);return{isFullscreen:e,isSupported:!0,enterFullscreen:s,exitFullscreen:a,toggleFullscreen:r}}(),s=(0,o.useRef)(null);return{isFullscreen:e,moduleRef:s,handleFullscreenToggle:()=>{console.log("全屏按钮被点击",s.current),s.current?(console.log("元素存在，调用toggleFullscreen"),t(s.current)):console.log("元素不存在")}}}(),{isDesktop:p,isMobile:x}=i(),{className:h}=c({mobile:{size:"h-12 px-4",direction:"flex-row",gap:"gap-3",text:"text-sm font-medium",width:"w-full justify-start"},tablet:{size:"h-12 px-4",direction:"flex-row",gap:"gap-3",text:"text-base font-medium",width:"w-full justify-start"},desktop:{size:"h-20 p-3",direction:"flex-col",gap:"gap-2",text:"text-xs font-medium",width:"w-full justify-center"}});return(0,a.jsxs)("div",{ref:m,className:`
        ${d?"fixed inset-0 flex flex-col":"container mx-auto max-w-6xl"}
        transition-all duration-300 ease-in-out
      `,style:d?{zIndex:40,background:"#021e36ff",height:"100vh",maxHeight:"100vh",overflow:"hidden"}:void 0,children:[d&&(0,a.jsx)(H,{onExitFullscreen:u}),!d&&(0,a.jsx)(Z,{onEnterFullscreen:u}),(0,a.jsx)("div",{className:d?x?"px-0 pb-0 pt-0 flex-1 min-h-0 overflow-hidden":"px-6 pb-6 pt-4 flex-1 min-h-0 overflow-hidden":x?"px-1 pb-6":"px-2 md:px-6 pb-12",children:(0,a.jsxs)("div",{className:d?x?"h-full flex flex-col gap-1 min-h-0":"h-full flex gap-6 min-h-0":x?"flex flex-col gap-3":"grid grid-cols-5 gap-6",style:d||x?{}:{alignItems:"stretch",gridAutoRows:"1fr"},children:[d&&p&&!x&&(0,a.jsxs)("div",{className:"flex-[1] h-full p-4 bg-gradient-to-b from-muted/30 to-muted/10 rounded-2xl border border-border/30",children:[(0,a.jsxs)("h3",{className:"text-sm font-semibold text-foreground mb-4 flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent"}),"MODEL TYPE"]}),(0,a.jsx)(Y,{activeTab:t,onTabChange:r,isFullscreen:d,isDesktop:p,typeButtonClassName:h})]}),(0,a.jsxs)("div",{className:`${d?x?"w-full h-full min-h-0":"flex-[2] h-full min-h-0":x?"w-full":"col-span-2"} flex flex-col ${d?"overflow-hidden":""}`,children:["  ",!(d&&p&&!x)&&(0,a.jsx)(ee,{activeTab:t,onTabChange:r}),(0,a.jsxs)("div",{className:d?"flex-1 min-h-0":"",children:["  ",(0,a.jsx)(eg,{modelType:t,onResultChange:l,onGeneratingChange:n,isFullscreen:d})]})]}),(0,a.jsx)("div",{className:`${d?x?"w-full flex-1 min-h-0 overflow-hidden":"flex-[3] h-full min-h-0 overflow-hidden":x?"w-full mt-6":"col-span-3 h-full"}`,children:(0,a.jsx)(eh,{activeTab:t,generationResult:s,isFullscreen:d,isMobile:x})})]})})]})}},64513:(e,t,s)=>{Promise.resolve().then(s.bind(s,89831)),Promise.resolve().then(s.bind(s,53617)),Promise.resolve().then(s.bind(s,51201)),Promise.resolve().then(s.bind(s,55233)),Promise.resolve().then(s.bind(s,90810)),Promise.resolve().then(s.bind(s,26343)),Promise.resolve().then(s.bind(s,71315)),Promise.resolve().then(s.bind(s,80640)),Promise.resolve().then(s.bind(s,76735)),Promise.resolve().then(s.t.bind(s,58044,23)),Promise.resolve().then(s.t.bind(s,22559,23))},77665:(e,t,s)=>{Promise.resolve().then(s.bind(s,60004)),Promise.resolve().then(s.bind(s,71107)),Promise.resolve().then(s.bind(s,46893)),Promise.resolve().then(s.bind(s,73555)),Promise.resolve().then(s.bind(s,84828)),Promise.resolve().then(s.bind(s,13872)),Promise.resolve().then(s.bind(s,42109)),Promise.resolve().then(s.bind(s,38846)),Promise.resolve().then(s.bind(s,93497)),Promise.resolve().then(s.t.bind(s,63962,23)),Promise.resolve().then(s.t.bind(s,60961,23))},89831:(e,t,s)=>{"use strict";s.d(t,{WorkspaceLayout:()=>a});let a=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call WorkspaceLayout() from the server but WorkspaceLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ai-dashboard/workspace-layout.tsx","WorkspaceLayout")},91624:(e,t,s)=>{"use strict";s.d(t,{T:()=>o});var a=s(25323),r=s(18542),l=s(91145);let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));o.displayName="Textarea"},94670:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>n,av:()=>d,j7:()=>i,tU:()=>o});var a=s(25323);s(18542);var r=s(38846),l=s(91145);function o({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...t})}function i({className:e,...t}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted/50 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-xl p-1 backdrop-blur-sm border border-border/30",e),...t})}function n({className:e,...t}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-md focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-9 flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-background/50 data-[state=active]:scale-[1.02] [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...t})}},99110:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var a=s(25323);s(18542);var r=s(68890),l=s(91145);function o({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}}};