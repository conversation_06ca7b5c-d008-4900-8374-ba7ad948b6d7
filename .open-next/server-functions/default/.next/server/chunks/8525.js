"use strict";exports.id=8525,exports.ids=[8525],exports.modules={2175:(e,t,n)=>{function r(e,t,n){return Math.min(Math.max(e,t),n)}function o(e){return"number"==typeof e&&!isNaN(e)}function i(e={}){let t,n,a,s,u=[],c=0,l=0,d=0,f=!1;function p(){y(t.selectedScrollSnap(),1)}function m(){f=!1}function g(){f=!1,c=0,l=0}function b(){let e=t.internalEngine().scrollBody.duration();l=+!e,f=!0,e||p()}function y(e,n){t.scrollSnapList().forEach((o,i)=>{let a=Math.abs(n),s=u[i],l=i===e,p=r(l?s+a:s-a,0,1);u[i]=p;let m=l&&f,g=t.previousScrollSnap();m&&(u[g]=1-p),l&&function(e,n){let{index:r,dragHandler:o,scrollSnaps:i}=t.internalEngine(),a=o.pointerDown(),s=1/(i.length-1),u=e,l=a?t.selectedScrollSnap():t.previousScrollSnap();if(a&&u===l){let e=-1*Math.sign(c);u=l,l=r.clone().set(l).add(e).get()}d=l*s+(u-l)*s*n}(e,p),function(e){let n=t.internalEngine().slideRegistry[e],{scrollSnaps:r,containerRect:o}=t.internalEngine(),i=u[e];n.forEach(n=>{let a=t.slideNodes()[n].style,s=parseFloat(i.toFixed(2)),u=s>0,c=function(e){let{axis:n}=t.internalEngine(),r=n.scroll.toUpperCase();return`translate${r}(${n.direction(e)}px)`}(u?r[e]:o.width+2);u&&(a.transform=c),a.opacity=s.toString(),a.pointerEvents=i>.5?"auto":"none",u||(a.transform=c)})}(i)})}function h(){let{dragHandler:e,index:n,scrollBody:r}=t.internalEngine(),o=t.selectedScrollSnap();if(!e.pointerDown())return o;let i=Math.sign(r.velocity()),a=Math.sign(c),s=n.clone().set(o).add(-1*i).get();return i&&a?a===i?s:o:null}function v(){let{target:e,location:r}=t.internalEngine(),i=Math.abs(e.get()-r.get())>=1,a=h(),s=!o(a);return!function(e){let{dragHandler:r,scrollBody:i}=e.internalEngine(),a=r.pointerDown(),s=i.velocity(),d=i.duration(),f=h(),p=!o(f);if(a){if(!s)return;c+=s,l=Math.abs(s/n);let{scrollSnaps:e,location:r,target:i}=t.internalEngine();o(f)&&!(u[f]<.5)&&(r.set(e[f]),i.set(r))}if(!a){if(!d||p)return;l+=(1-u[f])/d,l*=.68}p||y(f,l)}(t),!s&&!i&&u[a]>.999}function x(){return d}return{name:"fade",options:e,init:function(e){let o=(t=e).selectedScrollSnap(),{scrollBody:i,containerRect:c,axis:l}=t.internalEngine();n=r(.75*l.measureSize(c),200,500),f=!1,u=t.scrollSnapList().map((e,t)=>+(t===o)),a=i.settled,s=t.scrollProgress,i.settled=v,t.scrollProgress=x,t.on("select",b).on("slideFocus",p).on("pointerDown",g).on("pointerUp",m),function(){let{translate:e,slideLooper:n}=t.internalEngine();e.clear(),e.toggleActive(!1),n.loopPoints.forEach(({translate:e})=>{e.clear(),e.toggleActive(!1)})}(),p()},destroy:function(){let{scrollBody:e}=t.internalEngine();e.settled=a,t.scrollProgress=s,t.off("select",b).off("slideFocus",p).off("pointerDown",g).off("pointerUp",m),t.slideNodes().forEach(e=>{let t=e.style;t.opacity="",t.transform="",t.pointerEvents="",e.getAttribute("style")||e.removeAttribute("style")})}}}n.d(t,{A:()=>i}),i.globalOptions=void 0},4503:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(78108).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},21280:(e,t,n)=>{n.d(t,{DX:()=>a});var r=n(73636);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var i=n(96081),a=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,s,u=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(c.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,s=r.Children.toArray(o),c=s.find(u);if(c){let e=c.props.children,o=s.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}("Slot"),s=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},25160:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(73636);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ");var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:s="",children:u,iconNode:c,...l},d)=>(0,r.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:i("lucide",s),...l},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(u)?u:[u]])),u=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},u)=>(0,r.createElement)(s,{ref:u,iconNode:t,className:i(`lucide-${o(e)}`,n),...a}));return n.displayName=`${e}`,n}},38846:(e,t,n)=>{n.d(t,{B8:()=>T,Tabs:()=>v,TabsList:()=>w,TabsTrigger:()=>_,UC:()=>D,bL:()=>k,l9:()=>M});var r=n(18542),o=n(16760),i=n(20849),a=n(95783),s=n(78869),u=n(34757),c=n(2286),l=n(6772),d=n(13788),f=n(25323),p="Tabs",[m,g]=(0,i.A)(p,[a.RG]),b=(0,a.RG)(),[y,h]=m(p),v=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:s,activationMode:m="automatic",...g}=e,b=(0,c.jH)(s),[h,v]=(0,l.i)({prop:r,onChange:o,defaultProp:i??"",caller:p});return(0,f.jsx)(y,{scope:n,baseId:(0,d.B)(),value:h,onValueChange:v,orientation:a,dir:b,activationMode:m,children:(0,f.jsx)(u.sG.div,{dir:b,"data-orientation":a,...g,ref:t})})});v.displayName=p;var x="TabsList",w=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=h(x,n),s=b(n);return(0,f.jsx)(a.bL,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});w.displayName=x;var C="TabsTrigger",_=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...s}=e,c=h(C,n),l=b(n),d=S(c.baseId,r),p=A(c.baseId,r),m=r===c.value;return(0,f.jsx)(a.q7,{asChild:!0,...l,focusable:!i,active:m,children:(0,f.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||i||!e||c.onValueChange(r)})})})});_.displayName=C;var E="TabsContent",j=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...c}=e,l=h(E,n),d=S(l.baseId,o),p=A(l.baseId,o),m=o===l.value,g=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(s.C,{present:i||m,children:({present:n})=>(0,f.jsx)(u.sG.div,{"data-state":m?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:n&&a})})});function S(e,t){return`${e}-trigger-${t}`}function A(e,t){return`${e}-content-${t}`}j.displayName=E;var k=v,T=w,M=_,D=j},77642:(e,t,n)=>{n.d(t,{A:()=>j});var r=n(18542);function o(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function i(e,t){let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&n.every(n=>{let r=e[n],a=t[n];return"function"==typeof r?`${r}`==`${a}`:o(r)&&o(a)?i(r,a):r===a})}function a(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function s(e){return"number"==typeof e}function u(e){return"string"==typeof e}function c(e){return"boolean"==typeof e}function l(e){return"[object Object]"===Object.prototype.toString.call(e)}function d(e){return Math.abs(e)}function f(e){return Math.sign(e)}function p(e){return y(e).map(Number)}function m(e){return e[g(e)]}function g(e){return Math.max(0,e.length-1)}function b(e,t=0){return Array.from(Array(e),(e,n)=>t+n)}function y(e){return Object.keys(e)}function h(e,t){return void 0!==t.MouseEvent&&e instanceof t.MouseEvent}function v(){let e=[],t={add:function(n,r,o,i={passive:!0}){let a;return"addEventListener"in n?(n.addEventListener(r,o,i),a=()=>n.removeEventListener(r,o,i)):(n.addListener(o),a=()=>n.removeListener(o)),e.push(a),t},clear:function(){e=e.filter(e=>e())}};return t}function x(e=0,t=0){let n=d(e-t);function r(n){return n<e||n>t}return{length:n,max:t,min:e,constrain:function(n){return r(n)?n<e?e:t:n},reachedAny:r,reachedMax:function(e){return e>t},reachedMin:function(t){return t<e},removeOffset:function(e){return n?e-n*Math.ceil((e-t)/n):e}}}function w(e){let t=e;function n(e){return s(e)?e:e.get()}return{get:function(){return t},set:function(e){t=n(e)},add:function(e){t+=n(e)},subtract:function(e){t-=n(e)}}}function C(e,t){let n="x"===e.scroll?function(e){return`translate3d(${e}px,0px,0px)`}:function(e){return`translate3d(0px,${e}px,0px)`},r=t.style,o=null,i=!1;return{clear:function(){!i&&(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){if(i)return;let a=Math.round(100*e.direction(t))/100;a!==o&&(r.transform=n(a),o=a)},toggleActive:function(e){i=!e}}}let _={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function E(e,t,n){let r,o,i,a,j,S=e.ownerDocument,A=S.defaultView,k=function(e){function t(e,t){return function e(t,n){return[t,n].reduce((t,n)=>(y(n).forEach(r=>{let o=t[r],i=n[r],a=l(o)&&l(i);t[r]=a?e(o,i):i}),t),{})}(e,t||{})}return{mergeOptions:t,optionsAtMedia:function(n){let r=n.breakpoints||{},o=y(r).filter(t=>e.matchMedia(t).matches).map(e=>r[e]).reduce((e,n)=>t(e,n),{});return t(n,o)},optionsMediaQueries:function(t){return t.map(e=>y(e.breakpoints||{})).reduce((e,t)=>e.concat(t),[]).map(e.matchMedia)}}}(A),T=(j=[],{init:function(e,t){return(j=t.filter(({options:e})=>!1!==k.optionsAtMedia(e).active)).forEach(t=>t.init(e,k)),t.reduce((e,t)=>Object.assign(e,{[t.name]:t}),{})},destroy:function(){j=j.filter(e=>e.destroy())}}),M=v(),D=function(){let e,t={},n={init:function(t){e=t},emit:function(r){return(t[r]||[]).forEach(t=>t(e,r)),n},off:function(e,r){return t[e]=(t[e]||[]).filter(e=>e!==r),n},on:function(e,r){return t[e]=(t[e]||[]).concat([r]),n},clear:function(){t={}}};return n}(),{mergeOptions:I,optionsAtMedia:L,optionsMediaQueries:O}=k,{on:R,off:N,emit:P}=D,F=!1,$=I(_,E.globalOptions),V=I($),U=[];function z(t,n){if(F)return;V=L($=I($,t)),U=n||U;let{container:l,slides:_}=V;i=(u(l)?e.querySelector(l):l)||e.children[0];let E=u(_)?i.querySelectorAll(_):_;a=[].slice.call(E||i.children),r=function t(n){let r=function(e,t,n,r,o,i,a){let l,_,{align:E,axis:j,direction:S,startIndex:A,loop:k,duration:T,dragFree:M,dragThreshold:D,inViewThreshold:I,slidesToScroll:L,skipSnaps:O,containScroll:R,watchResize:N,watchSlides:P,watchDrag:F,watchFocus:$}=i,V={measure:function(e){let{offsetTop:t,offsetLeft:n,offsetWidth:r,offsetHeight:o}=e;return{top:t,right:n+r,bottom:t+o,left:n,width:r,height:o}}},U=V.measure(t),z=n.map(V.measure),H=function(e,t){let n="rtl"===t,r="y"===e,o=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(e){let{height:t,width:n}=e;return r?t:n},direction:function(e){return e*o}}}(j,S),B=H.measureSize(U),q={measure:function(e){return e/100*B}},G=function(e,t){let n={start:function(){return 0},center:function(e){return(t-e)/2},end:function(e){return t-e}};return{measure:function(r,o){return u(e)?n[e](r):e(t,r,o)}}}(E,B),W=!k&&!!R,{slideSizes:K,slideSizesWithGaps:X,startGap:J,endGap:Z}=function(e,t,n,r,o,i){let{measureSize:a,startEdge:s,endEdge:u}=e,c=n[0]&&o,l=function(){if(!c)return 0;let e=n[0];return d(t[s]-e[s])}(),f=c?parseFloat(i.getComputedStyle(m(r)).getPropertyValue(`margin-${u}`)):0,p=n.map(a),b=n.map((e,t,n)=>{let r=t===g(n);return t?r?p[t]+f:n[t+1][s]-e[s]:p[t]+l}).map(d);return{slideSizes:p,slideSizesWithGaps:b,startGap:l,endGap:f}}(H,U,z,n,k||!!R,o),Q=function(e,t,n,r,o,i,a,u,c){let{startEdge:l,endEdge:f,direction:b}=e,y=s(n);return{groupSlides:function(e){return y?p(e).filter(e=>e%n==0).map(t=>e.slice(t,t+n)):e.length?p(e).reduce((n,s,c)=>{let p=m(n)||0,y=s===g(e),h=o[l]-i[p][l],v=o[l]-i[s][f],x=r||0!==p?0:b(a),w=d(v-(!r&&y?b(u):0)-(h+x));return c&&w>t+2&&n.push(s),y&&n.push(e.length),n},[]).map((t,n,r)=>{let o=Math.max(r[n-1]||0);return e.slice(o,t)}):[]}}}(H,B,L,k,U,z,J,Z,0),{snaps:Y,snapsAligned:ee}=function(e,t,n,r,o){let{startEdge:i,endEdge:a}=e,{groupSlides:s}=o,u=s(r).map(e=>m(e)[a]-e[0][i]).map(d).map(t.measure),c=r.map(e=>n[i]-e[i]).map(e=>-d(e)),l=s(c).map(e=>e[0]).map((e,t)=>e+u[t]);return{snaps:c,snapsAligned:l}}(H,G,U,z,Q),et=-m(Y)+m(X),{snapsContained:en,scrollContainLimit:er}=function(e,t,n,r,o){let i=x(-t+e,0),a=n.map((e,t)=>{let{min:r,max:o}=i,a=i.constrain(e),s=t===g(n);return t?s||function(e,t){return 1>=d(e-t)}(r,a)?r:function(e,t){return 1>=d(e-t)}(o,a)?o:a:o}).map(e=>parseFloat(e.toFixed(3))),s=function(){let e=a[0],t=m(a);return x(a.lastIndexOf(e),a.indexOf(t)+1)}();function u(e,t){return 1>=d(e-t)}return{snapsContained:function(){if(t<=e+2)return[i.max];if("keepSnaps"===r)return a;let{min:n,max:o}=s;return a.slice(n,o)}(),scrollContainLimit:s}}(B,et,ee,R,0),eo=W?en:ee,{limit:ei}=function(e,t,n){let r=t[0];return{limit:x(n?r-e:m(t),r)}}(et,eo,k),ea=function e(t,n,r){let{constrain:o}=x(0,t),i=t+1,a=s(n);function s(e){return r?d((i+e)%i):o(e)}function u(){return e(t,a,r)}let c={get:function(){return a},set:function(e){return a=s(e),c},add:function(e){return u().set(a+e)},clone:u};return c}(g(eo),A,k),es=ea.clone(),eu=p(n),ec=({dragHandler:e,scrollBody:t,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(e.pointerDown()),t.seek()},el=({scrollBody:e,translate:t,location:n,offsetLocation:r,previousLocation:o,scrollLooper:i,slideLooper:a,dragHandler:s,animation:u,eventHandler:c,scrollBounds:l,options:{loop:d}},f)=>{let p=e.settled(),m=!l.shouldConstrain(),g=d?p:p&&m,b=g&&!s.pointerDown();b&&u.stop();let y=n.get()*f+o.get()*(1-f);r.set(y),d&&(i.loop(e.direction()),a.loop()),t.to(r.get()),b&&c.emit("settle"),g||c.emit("scroll")},ed=function(e,t,n,r){let o=v(),i=1e3/60,a=null,s=0,u=0;function c(e){if(!u)return;a||(a=e,n(),n());let o=e-a;for(a=e,s+=o;s>=i;)n(),s-=i;r(s/i),u&&(u=t.requestAnimationFrame(c))}function l(){t.cancelAnimationFrame(u),a=null,s=0,u=0}return{init:function(){o.add(e,"visibilitychange",()=>{e.hidden&&(a=null,s=0)})},destroy:function(){l(),o.clear()},start:function(){u||(u=t.requestAnimationFrame(c))},stop:l,update:n,render:r}}(r,o,()=>ec(ej),e=>el(ej,e)),ef=eo[ea.get()],ep=w(ef),em=w(ef),eg=w(ef),eb=w(ef),ey=function(e,t,n,r,o,i){let a=0,s=0,u=o,c=.68,l=e.get(),p=0;function m(e){return u=e,b}function g(e){return c=e,b}let b={direction:function(){return s},duration:function(){return u},velocity:function(){return a},seek:function(){let t=r.get()-e.get(),o=0;return u?(n.set(e),a+=t/u,a*=c,l+=a,e.add(a),o=l-p):(a=0,n.set(r),e.set(r),o=t),s=f(o),p=l,b},settled:function(){return .001>d(r.get()-t.get())},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return m(o)},useFriction:g,useDuration:m};return b}(ep,eg,em,eb,T,.68),eh=function(e,t,n,r,o){let{reachedAny:i,removeOffset:a,constrain:s}=r;function u(e){return e.concat().sort((e,t)=>d(e)-d(t))[0]}function c(t,r){let o=[t,t+n,t-n];if(!e)return t;if(!r)return u(o);let i=o.filter(e=>f(e)===r);return i.length?u(i):m(o)-n}return{byDistance:function(n,r){let u=o.get()+n,{index:l,distance:f}=function(n){let r=e?a(n):s(n),{index:o}=t.map((e,t)=>({diff:c(e-r,0),index:t})).sort((e,t)=>d(e.diff)-d(t.diff))[0];return{index:o,distance:r}}(u),p=!e&&i(u);if(!r||p)return{index:l,distance:n};let m=n+c(t[l]-f,0);return{index:l,distance:m}},byIndex:function(e,n){let r=c(t[e]-o.get(),n);return{index:e,distance:r}},shortcut:c}}(k,eo,et,ei,eb),ev=function(e,t,n,r,o,i,a){function s(o){let s=o.distance,u=o.index!==t.get();i.add(s),s&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),u&&(n.set(t.get()),t.set(o.index),a.emit("select"))}return{distance:function(e,t){s(o.byDistance(e,t))},index:function(e,n){let r=t.clone().set(e);s(o.byIndex(r.get(),n))}}}(ed,ea,es,ey,eh,eb,a),ex=function(e){let{max:t,length:n}=e;return{get:function(e){return n?-((e-t)/n):0}}}(ei),ew=v(),eC=function(e,t,n,r){let o,i={},a=null,s=null,u=!1;return{init:function(){o=new IntersectionObserver(e=>{u||(e.forEach(e=>{i[t.indexOf(e.target)]=e}),a=null,s=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(e=>o.observe(e))},destroy:function(){o&&o.disconnect(),u=!0},get:function(e=!0){if(e&&a)return a;if(!e&&s)return s;let t=y(i).reduce((t,n)=>{let r=parseInt(n),{isIntersecting:o}=i[r];return(e&&o||!e&&!o)&&t.push(r),t},[]);return e&&(a=t),e||(s=t),t}}}(t,n,a,I),{slideRegistry:e_}=function(e,t,n,r,o,i){let{groupSlides:a}=o,{min:s,max:u}=r;return{slideRegistry:function(){let r=a(i);return 1===n.length?[i]:e&&"keepSnaps"!==t?r.slice(s,u).map((e,t,n)=>{let r=t===g(n);return t?r?b(g(i)-m(n)[0]+1,m(n)[0]):e:b(m(n[0])+1)}):r}()}}(W,R,eo,er,Q,eu),eE=function(e,t,n,r,o,i,a,u){let l={passive:!0,capture:!0},d=0;function f(e){"Tab"===e.code&&(d=new Date().getTime())}return{init:function(p){u&&(i.add(document,"keydown",f,!1),t.forEach((t,f)=>{i.add(t,"focus",t=>{(c(u)||u(p,t))&&function(t){if(new Date().getTime()-d>10)return;a.emit("slideFocusStart"),e.scrollLeft=0;let i=n.findIndex(e=>e.includes(t));s(i)&&(o.useDuration(0),r.index(i,0),a.emit("slideFocus"))}(f)},l)}))}}}(e,n,e_,ev,ey,ew,a,$),ej={ownerDocument:r,ownerWindow:o,eventHandler:a,containerRect:U,slideRects:z,animation:ed,axis:H,dragHandler:function(e,t,n,r,o,i,a,s,u,l,p,m,g,b,y,w,C,_,E){let{cross:j,direction:S}=e,A=["INPUT","SELECT","TEXTAREA"],k={passive:!1},T=v(),M=v(),D=x(50,225).constrain(b.measure(20)),I={mouse:300,touch:400},L={mouse:500,touch:600},O=y?43:25,R=!1,N=0,P=0,F=!1,$=!1,V=!1,U=!1;function z(e){if(!h(e,r)&&e.touches.length>=2)return H(e);let t=i.readPoint(e),n=i.readPoint(e,j),a=d(t-N),u=d(n-P);if(!$&&!U&&(!e.cancelable||!($=a>u)))return H(e);let c=i.pointerMove(e);a>w&&(V=!0),l.useFriction(.3).useDuration(.75),s.start(),o.add(S(c)),e.preventDefault()}function H(e){let t=p.byDistance(0,!1).index!==m.get(),n=i.pointerUp(e)*(y?L:I)[U?"mouse":"touch"],r=function(e,t){let n=m.add(-1*f(e)),r=p.byDistance(e,!y).distance;return y||d(e)<D?r:C&&t?.5*r:p.byIndex(n.get(),0).distance}(S(n),t),o=function(e,t){var n,r;if(0===e||0===t||d(e)<=d(t))return 0;let o=(n=d(e),r=d(t),d(n-r));return d(o/e)}(n,r);$=!1,F=!1,M.clear(),l.useDuration(O-10*o).useFriction(.68+o/50),u.distance(r,!y),U=!1,g.emit("pointerUp")}function B(e){V&&(e.stopPropagation(),e.preventDefault(),V=!1)}return{init:function(e){E&&T.add(t,"dragstart",e=>e.preventDefault(),k).add(t,"touchmove",()=>void 0,k).add(t,"touchend",()=>void 0).add(t,"touchstart",s).add(t,"mousedown",s).add(t,"touchcancel",H).add(t,"contextmenu",H).add(t,"click",B,!0);function s(s){(c(E)||E(e,s))&&function(e){let s=h(e,r);if((U=s,V=y&&s&&!e.buttons&&R,R=d(o.get()-a.get())>=2,!s||0===e.button)&&!function(e){let t=e.nodeName||"";return A.includes(t)}(e.target)){F=!0,i.pointerDown(e),l.useFriction(0).useDuration(0),o.set(a);let r=U?n:t;M.add(r,"touchmove",z,k).add(r,"touchend",H).add(r,"mousemove",z,k).add(r,"mouseup",H),N=i.readPoint(e),P=i.readPoint(e,j),g.emit("pointerDown")}}(s)}},destroy:function(){T.clear(),M.clear()},pointerDown:function(){return F}}}(H,e,r,o,eb,function(e,t){let n,r;function o(e){return e.timeStamp}function i(n,r){let o=r||e.scroll,i=`client${"x"===o?"X":"Y"}`;return(h(n,t)?n:n.touches[0])[i]}return{pointerDown:function(e){return n=e,r=e,i(e)},pointerMove:function(e){let t=i(e)-i(r),a=o(e)-o(n)>170;return r=e,a&&(n=e),t},pointerUp:function(e){if(!n||!r)return 0;let t=i(r)-i(n),a=o(e)-o(n),s=o(e)-o(r)>170,u=t/a;return a&&!s&&d(u)>.1?u:0},readPoint:i}}(H,o),ep,ed,ev,ey,eh,ea,a,q,M,D,O,0,F),eventStore:ew,percentOfView:q,index:ea,indexPrevious:es,limit:ei,location:ep,offsetLocation:eg,previousLocation:em,options:i,resizeHandler:function(e,t,n,r,o,i,a){let s,u,l=[e].concat(r),f=[],p=!1;function m(e){return o.measureSize(a.measure(e))}return{init:function(o){i&&(u=m(e),f=r.map(m),s=new ResizeObserver(n=>{(c(i)||i(o,n))&&function(n){for(let i of n){if(p)return;let n=i.target===e,a=r.indexOf(i.target),s=n?u:f[a];if(d(m(n?e:r[a])-s)>=.5){o.reInit(),t.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{l.forEach(e=>s.observe(e))}))},destroy:function(){p=!0,s&&s.disconnect()}}}(t,a,o,n,H,N,V),scrollBody:ey,scrollBounds:function(e,t,n,r,o){let i=o.measure(10),a=o.measure(50),s=x(.1,.99),u=!1;function c(){return!u&&!!e.reachedAny(n.get())&&!!e.reachedAny(t.get())}return{shouldConstrain:c,constrain:function(o){if(!c())return;let u=e.reachedMin(t.get())?"min":"max",l=d(e[u]-t.get()),f=n.get()-t.get(),p=s.constrain(l/a);n.subtract(f*p),!o&&d(f)<i&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(e){u=!e}}}(ei,eg,eb,ey,q),scrollLooper:function(e,t,n,r){let{reachedMin:o,reachedMax:i}=x(t.min+.1,t.max+.1);return{loop:function(t){if(!(1===t?i(n.get()):-1===t&&o(n.get())))return;let a=-1*t*e;r.forEach(e=>e.add(a))}}}(et,ei,eg,[ep,eg,em,eb]),scrollProgress:ex,scrollSnapList:eo.map(ex.get),scrollSnaps:eo,scrollTarget:eh,scrollTo:ev,slideLooper:function(e,t,n,r,o,i,a,s,u){let c=p(o),l=p(o).reverse(),d=g(m(l,a[0]),n,!1).concat(g(m(c,t-a[0]-1),-n,!0));function f(e,t){return e.reduce((e,t)=>e-o[t],t)}function m(e,t){return e.reduce((e,n)=>f(e,t)>0?e.concat([n]):e,[])}function g(o,a,c){let l=i.map((e,n)=>({start:e-r[n]+.5+a,end:e+t-.5+a}));return o.map(t=>{let r=c?0:-n,o=c?n:0,i=l[t][c?"end":"start"];return{index:t,loopPoint:i,slideLocation:w(-1),translate:C(e,u[t]),target:()=>s.get()>i?r:o}})}return{canLoop:function(){return d.every(({index:e})=>.1>=f(c.filter(t=>t!==e),t))},clear:function(){d.forEach(e=>e.translate.clear())},loop:function(){d.forEach(e=>{let{target:t,translate:n,slideLocation:r}=e,o=t();o!==r.get()&&(n.to(o),r.set(o))})},loopPoints:d}}(H,B,et,K,X,Y,eo,eg,n),slideFocus:eE,slidesHandler:(_=!1,{init:function(e){P&&(l=new MutationObserver(t=>{!_&&(c(P)||P(e,t))&&function(t){for(let n of t)if("childList"===n.type){e.reInit(),a.emit("slidesChanged");break}}(t)})).observe(t,{childList:!0})},destroy:function(){l&&l.disconnect(),_=!0}}),slidesInView:eC,slideIndexes:eu,slideRegistry:e_,slidesToScroll:Q,target:eb,translate:C(H,t)};return ej}(e,i,a,S,A,n,D);return n.loop&&!r.slideLooper.canLoop()?t(Object.assign({},n,{loop:!1})):r}(V),O([$,...U.map(({options:e})=>e)]).forEach(e=>M.add(e,"change",H)),V.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(W),r.eventHandler.init(W),r.resizeHandler.init(W),r.slidesHandler.init(W),r.options.loop&&r.slideLooper.loop(),i.offsetParent&&a.length&&r.dragHandler.init(W),o=T.init(W,U))}function H(e,t){let n=G();B(),z(I({startIndex:n},e),t),D.emit("reInit")}function B(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),T.destroy(),M.clear()}function q(e,t,n){V.active&&!F&&(r.scrollBody.useBaseFriction().useDuration(!0===t?0:V.duration),r.scrollTo.index(e,n||0))}function G(){return r.index.get()}let W={canScrollNext:function(){return r.index.add(1).get()!==G()},canScrollPrev:function(){return r.index.add(-1).get()!==G()},containerNode:function(){return i},internalEngine:function(){return r},destroy:function(){F||(F=!0,M.clear(),B(),D.emit("destroy"),D.clear())},off:N,on:R,emit:P,plugins:function(){return o},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:H,rootNode:function(){return e},scrollNext:function(e){q(r.index.add(1).get(),e,-1)},scrollPrev:function(e){q(r.index.add(-1).get(),e,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:q,selectedScrollSnap:G,slideNodes:function(){return a},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return z(t,n),setTimeout(()=>D.emit("init"),0),W}function j(e={},t=[]){let n=(0,r.useRef)(e),o=(0,r.useRef)(t),[s,u]=(0,r.useState)(),[c,l]=(0,r.useState)(),d=(0,r.useCallback)(()=>{s&&s.reInit(n.current,o.current)},[s]);return(0,r.useEffect)(()=>{i(n.current,e)||(n.current=e,d())},[e,d]),(0,r.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let n=a(e),r=a(t);return n.every((e,t)=>i(e,r[t]))}(o.current,t)&&(o.current=t,d())},[t,d]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&c){E.globalOptions=j.globalOptions;let e=E(c,n.current,o.current);return u(e),()=>e.destroy()}u(void 0)},[c,u]),[l,s]}E.globalOptions=void 0,j.globalOptions=void 0},80436:(e,t,n)=>{n.d(t,{F:()=>a});var r=n(64556);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:s}=t,u=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==s?void 0:s[e];if(null===t)return null;let i=o(t)||o(r);return a[e][i]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...s,...c}[t]):({...s,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},80640:(e,t,n)=>{n.d(t,{Tabs:()=>o,TabsList:()=>i,TabsTrigger:()=>a});var r=n(20263);(0,r.registerClientReference)(function(){throw Error("Attempted to call Content() from the server but Content is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","Content"),(0,r.registerClientReference)(function(){throw Error("Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","List"),(0,r.registerClientReference)(function(){throw Error("Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","Root");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","Tabs");(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","TabsContent");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","TabsList"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","TabsTrigger");(0,r.registerClientReference)(function(){throw Error("Attempted to call Trigger() from the server but Trigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","Trigger"),(0,r.registerClientReference)(function(){throw Error("Attempted to call createTabsScope() from the server but createTabsScope is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_db7cf47e65ed6f19856cc900d5a2a87f/node_modules/@radix-ui/react-tabs/dist/index.mjs","createTabsScope")},98628:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(25160).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])}};