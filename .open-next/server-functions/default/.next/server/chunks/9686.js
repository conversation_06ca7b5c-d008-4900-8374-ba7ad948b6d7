exports.id=9686,exports.ids=[9686],exports.modules={49241:(e,t,r)=>{"use strict";r.d(t,{XW:()=>i,fh:()=>n,xO:()=>l,yy:()=>a});var s=r(63363),o=r(14584);async function a(e,t="image",r){try{console.log(`[File Transfer] Starting transfer from: ${e}`);let a=r||(0,o.YJ)(),n=function(e){try{return new URL(e).pathname.split(".").pop()||"bin"}catch(e){return"bin"}}(e),i={png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",webp:"image/webp",gif:"image/gif",bmp:"image/bmp",svg:"image/svg+xml",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",webm:"video/webm",mkv:"video/x-matroska"}[n.toLowerCase()]||"application/octet-stream",l=process.env.STORAGE_BUCKET||"shipany-test",u=`ai-generated/${t}/${a}.${n}`;console.log(`[File Transfer] Target bucket: ${l}, key: ${u}, Content-Type: ${i}`);let c=(0,s.m)(),d=await c.downloadAndUpload({url:e,key:u,bucket:l,contentType:i,disposition:"inline"});return console.log("[File Transfer] Transfer successful:",d),{success:!0,url:d.url,key:d.key}}catch(e){return console.error("[File Transfer] Transfer failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function n(e,t="image",r){let s=[];for(let o=0;o<e.length;o++){let n=e[o],i=r?e.length>1?`${r}_${o}`:r:void 0,l=await a(n,t,i);s.push({originalUrl:n,...l})}return s}function i(e){let t=[];return e.url&&t.push(e.url),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&t.push(e.url)}),e.data&&e.data.url&&t.push(e.data.url),t}function l(e,t){let r=JSON.parse(JSON.stringify(e));return r.url&&t[r.url]&&(r.url=t[r.url]),r.results&&Array.isArray(r.results)&&r.results.forEach(e=>{e.url&&t[e.url]&&(e.url=t[e.url])}),r.data&&r.data.url&&t[r.data.url]&&(r.data.url=t[r.data.url]),r}},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},54467:()=>{},56605:(e,t,r)=>{"use strict";r.d(t,{U:()=>l,g:()=>i});var s=r(14584),o=r(88661),a=r(94685);let n={baseURL:{overseas:"https://api.grsai.com",domestic:"https://grsai.dakka.com.cn"},retryAttempts:3,retryDelay:1e3};class i{constructor(e,t="overseas"){if(this.apiKey=e||process.env.GRSAI_APIKEY||"",this.baseURL=n.baseURL[t],!this.apiKey)throw Error("GRSAI API key is required")}async makeRequest(e,t,r={}){let s=`${this.baseURL}${e}`,o={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify(t)};console.log(`[GRSAI] Making request to: ${s}`),console.log("[GRSAI] Request data:",JSON.stringify(t,null,2));let a=0;for(;a<n.retryAttempts;)try{let e=await fetch(s,o);if(console.log(`[GRSAI] Response status: ${e.status}`),console.log("[GRSAI] Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t=await e.text();throw console.log("[GRSAI] Error response:",t),Error(`HTTP ${e.status}: ${e.statusText} - ${t}`)}if(r.stream)return e;let t=e.headers.get("content-type");if(console.log(`[GRSAI] Content-Type: ${t}`),t&&t.includes("text/plain")){let t=await e.text();return console.log("[GRSAI] SSE response text:",t),this.parseSSEResponse(t)}let a=await e.json();return console.log("[GRSAI] JSON response:",JSON.stringify(a,null,2)),a}catch(e){if(console.log(`[GRSAI] Request attempt ${a+1} failed:`,e),++a>=n.retryAttempts)throw e;await new Promise(e=>setTimeout(e,n.retryDelay*a))}}parseSSEResponse(e){try{console.log(`[GRSAI] Parsing SSE response, total length: ${e.length}`);let t=e.split("\n"),r=null,s=0;for(let e of t)if(e.startsWith("data: ")&&"data: [DONE]"!==e)try{let t=e.substring(6);console.log(`[GRSAI] Parsing JSON line: ${t}`),r=JSON.parse(t),s++,console.log(`[GRSAI] Successfully parsed JSON data #${s}:`,r)}catch(t){console.log(`[GRSAI] Failed to parse JSON line: ${e}`,t)}return console.log(`[GRSAI] Found ${s} valid JSON data entries`),console.log("[GRSAI] Final parsed data:",r),r||{error:"No valid JSON data found in SSE response"}}catch(e){throw console.log("[GRSAI] SSE parsing error:",e),Error(`Failed to parse SSE response: ${e}`)}}async generateText(e){return await this.makeRequest("/v1/chat/completions",e,{stream:e.stream})}async generateImage(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/draw/completions",t)}async generateFluxImage(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/draw/flux",t)}async generateVideo(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/video/veo",t)}async getResult(e){return await this.makeRequest("/v1/draw/result",{id:e})}}class l{constructor(){this.grsaiProvider=new i}async processRequest(e,t){let r=(0,s.YJ)(),n=await (0,a.om)(t.model);if(!n)throw Error(`Model ${t.model} not found`);let i=await this.estimateCost(t,n);await (0,a.b)({user_uuid:e,model_id:t.model,request_id:r,credits_consumed:i,status:"pending",request_params:t,started_at:(0,o.iq)()});try{let e;switch(t.type){case"text":e=await this.handleTextGeneration(t,r);break;case"image":e=await this.handleImageGeneration(t,r);break;case"video":e=await this.handleVideoGeneration(t,r);break;default:throw Error(`Unsupported request type: ${t.type}`)}return"pending"!==e.status&&await (0,a.it)(r,{status:"success"===e.status?"success":"failed",output_size:this.calculateOutputSize(e),response_data:e,completed_at:(0,o.iq)()}),e}catch(e){throw await (0,a.it)(r,{status:"failed",error_reason:"error",error_detail:e instanceof Error?e.message:"Unknown error",completed_at:(0,o.iq)()}),e}}async handleTextGeneration(e,t){let r,s=e.options?.uploadedImages||[];if(s.length>0){let t=[{type:"text",text:e.prompt}];s.forEach(e=>{t.push({type:"image_url",image_url:{url:e}})}),r=e.options?.messages?.map(e=>({role:e.role,content:e.image_url?[{type:"text",text:e.content},{type:"image_url",image_url:{url:e.image_url}}]:e.content}))||[{role:"user",content:t}]}else r=e.options?.messages?.map(e=>({role:e.role,content:e.content}))||[{role:"user",content:e.prompt}];let o={model:e.model,messages:r,stream:e.options?.stream||!1,temperature:e.options?.temperature,max_tokens:e.options?.max_tokens},a=await this.grsaiProvider.generateText(o);return a instanceof ReadableStream?{id:t,type:"text",status:"pending",result:{text:""}}:{id:t,type:"text",status:"success",result:{text:a.choices[0]?.message?.content||""},usage:{input_tokens:this.estimateTokens(e.prompt),output_tokens:this.estimateTokens(a.choices[0]?.message?.content||""),total_tokens:0,credits_consumed:0}}}async handleImageGeneration(e,t){let r;console.log(`[AI Service] Starting image generation for model: ${e.model}`);let s=await (0,a.om)(e.model);if(!s)throw Error(`Model ${e.model} not found`);console.log("[AI Service] Model config:",s);let o=[...e.options?.referenceImages||[],...e.options?.uploadedImages||[]];if("/v1/draw/flux"===s.api_endpoint){let t={model:e.model,prompt:e.prompt,urls:o.length>0?o:void 0,seed:e.options?.seed,aspectRatio:e.options?.aspectRatio||e.options?.size,webHook:"-1",cdn:e.options?.cdn||"global"};console.log("[AI Service] Calling Flux API with request:",t),r=await this.grsaiProvider.generateFluxImage(t)}else{let t={model:e.model,prompt:e.prompt,size:e.options?.size,variants:e.options?.variants,urls:o.length>0?o:void 0,webHook:"-1",cdn:e.options?.cdn||"global"};console.log("[AI Service] Calling Image API with request:",t),r=await this.grsaiProvider.generateImage(t)}return(console.log("[AI Service] GRSAI response:",r),0===r.code&&r.data&&r.data.id)?(console.log(`[AI Service] Task created with ID: ${r.data.id}`),await (0,a.it)(t,{response_data:r,status:"pending"}),{id:t,type:"image",status:"pending",progress:0,usage:{credits_consumed:s.credits_per_unit}}):(console.log(`[AI Service] Direct result response, status: ${r.status}`),{id:t,type:"image",status:"succeeded"===r.status?"success":"failed"===r.status?"failed":"running",progress:r.progress||0,result:{images:"succeeded"===r.status&&r.url?[{url:r.url,width:r.width||1024,height:r.height||1024}]:void 0},error:"failed"===r.status?{reason:r.failure_reason||"error",detail:r.error||"Unknown error"}:void 0,usage:{credits_consumed:s.credits_per_unit}})}async handleVideoGeneration(e,t){let r=e.options?.uploadedImages?.[0]||e.options?.firstFrameUrl,s={model:e.model,prompt:e.prompt,firstFrameUrl:r,webHook:"-1",cdn:e.options?.cdn||"global"},o=await this.grsaiProvider.generateVideo(s),n=await (0,a.om)(e.model);return"code"in o&&"data"in o&&0===o.code&&o.data&&"object"==typeof o.data&&null!==o.data&&"id"in o.data?{id:t,type:"video",status:"pending",progress:0,usage:{credits_consumed:n?.credits_per_unit||0}}:{id:t,type:"video",status:"succeeded"===o.status?"success":"failed"===o.status?"failed":"running",progress:o.progress||0,result:{video:"succeeded"===o.status&&o.url?{url:o.url}:void 0},error:"failed"===o.status?{reason:o.failure_reason||"error",detail:o.error||"Unknown error"}:void 0,usage:{credits_consumed:n?.credits_per_unit||0}}}async estimateCost(e,t){switch(e.type){case"text":let r=this.estimateTokens(e.prompt),s=e.options?.max_tokens||1e3;return await (0,a.lC)(e.model,r,s);case"image":let o=e.options?.variants||1;return t.credits_per_unit*o;default:return t.credits_per_unit}}estimateTokens(e){return Math.ceil((e.match(/[\u4e00-\u9fff]/g)||[]).length+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}calculateOutputSize(e){switch(e.type){case"text":return this.estimateTokens(e.result?.text||"");case"image":return e.result?.images?.length||0;case"video":return+!!e.result?.video;default:return 0}}}},58246:(e,t,r)=>{"use strict";function s(e){return n(0,"ok",e||[])}function o(){return n(0,"ok")}function a(e){return n(-1,e)}function n(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>a,j7:()=>n,rn:()=>o})},63363:(e,t,r)=>{"use strict";r.d(t,{m:()=>a});var s=r(91043),o=r(17492);function a(e){return new n(e)}class n{constructor(e){this.s3=new s.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:t,contentType:r,bucket:s,onProgress:a,disposition:n="inline"}){if(s||(s=process.env.STORAGE_BUCKET||""),!s)throw Error("Bucket is required");let i=new o._({client:this.s3,params:{Bucket:s,Key:t,Body:e,ContentDisposition:n,...r&&{ContentType:r}}});a&&i.on("httpUploadProgress",e=>{a((e.loaded||0)/(e.total||1)*100)});let l=await i.done();return{location:l.Location,bucket:l.Bucket,key:l.Key,filename:l.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${s}/${l.Key}`:l.Location}}async downloadAndUpload({url:e,key:t,bucket:r,contentType:s,disposition:o="inline"}){let a=await fetch(e);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);if(!a.body)throw Error("No body in response");let n=await a.arrayBuffer(),i=Buffer.from(n);return this.uploadFile({body:i,key:t,bucket:r,contentType:s,disposition:o})}}},78443:()=>{},94685:(e,t,r)=>{"use strict";r.d(t,{lC:()=>c,b:()=>d,om:()=>l,zQ:()=>u,kl:()=>m,Kp:()=>i,nv:()=>n,oY:()=>g,c9:()=>f,it:()=>p});var s=r(7279);function o(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let s=Object.values(e).filter(Boolean);if(s.length>0)return s[0]}return""}function a(e,t="en"){return{...e,model_name:o(e.model_name_i18n||e.model_name,t),description:o(e.description_i18n||e.description,t)}}async function n(e="en"){let t=(0,s.A)(),{data:r,error:o}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(o)throw o;return(r||[]).map(t=>a(t,e))}async function i(e,t="en"){let r=(0,s.A)(),{data:o,error:n}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(n)throw n;return(o||[]).map(e=>a(e,t))}async function l(e,t="en"){let r=(0,s.A)(),{data:o,error:n}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return n?null:o?a(o,t):null}async function u(e,t="en"){let r=(0,s.A)(),{data:o,error:n}=await r.from("ai_models").select("*").eq("model_id",e).single();return n?null:o?a(o,t):null}async function c(e,t,r){let o=(0,s.A)(),{data:a,error:n}=await o.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(n)throw n;return a||1}async function d(e){let t=(0,s.A)(),{data:r,error:o}=await t.from("ai_model_usage").insert(e).select().single();if(o)throw o;return r}async function p(e,t){let r=(0,s.A)(),{data:o,error:a}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return a?null:o}async function m(e){let t=(0,s.A)(),{data:r,error:o}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return o?null:r}async function g(e,t=1,r=50){let o=(0,s.A)(),{data:a,error:n}=await o.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(n)throw n;return a||[]}async function f(e){let t=(0,s.A)(),{data:r,error:o}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(o)throw o;return r||[]}}};