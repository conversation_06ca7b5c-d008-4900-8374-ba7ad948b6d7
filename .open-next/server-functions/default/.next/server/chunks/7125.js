exports.id=7125,exports.ids=[7125],exports.modules={7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(54209);function n(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,a.UU)(e,t)}},10061:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(16454);r(15358);var n=r(69803);async function i(){let e=await (0,n.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(31146).D)([i]),(0,a.A)(i,"00454d178be4ce07ce841ef8e90957f7985e91d419",null)},11793:(e,t,r)=>{"use strict";r.d(t,{j2:()=>w,Y9:()=>l});var a=r(17588),n=r(75329),i=r(28420),s=r(10061),o=r(88661),c=r(14584),u=r(53908);let d=[];d.push((0,n.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let n=await a.json();if(!n)return console.log("invalid payload from token"),null;let{email:i,sub:s,given_name:o,family_name:c,email_verified:u,picture:d}=n;return i?{id:s,name:[o,c].join(" "),email:i,image:d,emailVerified:u?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:l,signIn:f,signOut:_,auth:w}=(0,a.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:n})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,c.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,s.T)()};try{let t=await (0,u.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},14584:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>i,ZK:()=>o,f1:()=>s});var a=r(25031),n=r(75184);function i(){return(0,n.A)()}function s(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",a=t.length;for(let n=0;n<e;n++)r+=t[Math.floor(Math.random()*a)];return r}function o(){return new a.F({workerId:1}).NextId().toString()}},30354:(e,t,r)=>{"use strict";r.d(t,{H3:()=>o,OI:()=>f,Ty:()=>_,d_:()=>l,jL:()=>w,ll:()=>g,mP:()=>u,nX:()=>d,rN:()=>c});var a=r(67332),n=r(87009),i=r(88661),s=r(14584),o=function(e){return e.NewUser="new_user",e.OrderPay="order_pay",e.SystemAdd="system_add",e.Ping="ping",e.TextGeneration="text_generation",e.ImageGeneration="image_generation",e.VideoGeneration="video_generation",e.AIModelUsage="ai_model_usage",e}({}),c=function(e){return e[e.NewUserGet=100]="NewUserGet",e[e.PingCost=1]="PingCost",e[e.TextGenerationBase=5]="TextGenerationBase",e[e.ImageGenerationBase=50]="ImageGenerationBase",e[e.VideoGenerationBase=200]="VideoGenerationBase",e}({});async function u(e){let t={left_credits:0};try{await (0,n.Yw)(e)&&(t.is_recharged=!0);let r=await (0,a.uo)(e);return r&&r.forEach(e=>{t.left_credits+=e.credits}),t.left_credits<0&&(t.left_credits=0),t.left_credits>0&&(t.is_pro=!0),t}catch(e){return console.log("get user credits failed: ",e),t}}async function d({user_uuid:e,trans_type:t,credits:r}){try{let n="",o="",c=0,u=await (0,a.uo)(e);if(u)for(let e=0,t=u.length;e<t;e++){let t=u[e];if((c+=t.credits)>=r){n=t.order_no,o=t.expired_at||"";break}}let d={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:t,credits:0-r,order_no:n,expired_at:o};await (0,a.Ni)(d)}catch(e){throw console.log("decrease credits failed: ",e),e}}async function l({user_uuid:e,trans_type:t,credits:r,expired_at:n,order_no:o}){try{let c={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:t,credits:r,order_no:o||"",expired_at:n||""};await (0,a.Ni)(c)}catch(e){throw console.log("increase credits failed: ",e),e}}async function f(e){try{if(await (0,a.jR)(e.order_no))return;await l({user_uuid:e.user_uuid,trans_type:"order_pay",credits:e.credits,expired_at:e.expired_at,order_no:e.order_no})}catch(e){throw console.log("update credit for order failed: ",e),e}}async function _({user_uuid:e,model_id:t,request_id:r,credits:n,trans_type:o="ai_model_usage"}){try{let c="",u="",d=0,l=await (0,a.uo)(e);if(l)for(let e=0,t=l.length;e<t;e++){let t=l[e];if((d+=t.credits)>=n){c=t.order_no,u=t.expired_at||"";break}}if(d<n)throw Error("Insufficient credits");let f={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:o,credits:0-n,order_no:c,expired_at:u,model_id:t,request_id:r};await (0,a.Ni)(f)}catch(e){throw console.log("decrease credits for AI model failed: ",e),e}}async function w({user_uuid:e,model_id:t,request_id:r,credits:n,original_trans_no:o}){try{let o={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:"ai_model_refund",credits:n,order_no:"",expired_at:(0,i.MI)(),model_id:t,request_id:r};await (0,a.Ni)(o)}catch(e){throw console.log("refund credits for AI model failed: ",e),e}}async function g(e,t){try{return(await u(e)).left_credits>=t}catch(e){return console.log("check sufficient credits failed: ",e),!1}}},33102:(e,t,r)=>{"use strict";r.d(t,{Gs:()=>s,HW:()=>i,Nh:()=>d,PD:()=>_,QZ:()=>l,XQ:()=>u,in:()=>w,lo:()=>c,pX:()=>o,y_:()=>f});var a=r(88661),n=r(7279);async function i(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("users").insert(e);if(a)throw a;return r}async function s(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("users").select("*").eq("email",e).limit(1).single();if(!a)return r}async function o(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("users").select("*").eq("uuid",e).single();if(!a)return r}async function c(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,a=(0,n.A)(),{data:i,error:s}=await a.from("users").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(!s)return i}async function u(e,t){let r=(0,n.A)(),i=(0,a.iq)(),{data:s,error:o}=await r.from("users").update({invite_code:t,updated_at:i}).eq("uuid",e);if(o)throw o;return s}async function d(e,t){let r=(0,n.A)(),i=(0,a.iq)(),{data:s,error:o}=await r.from("users").update({invited_by:t,updated_at:i}).eq("uuid",e);if(o)throw o;return s}async function l(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("users").select("*").in("uuid",e);return a?[]:r}async function f(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("users").select("*").eq("invite_code",e).single();if(!a)return r}async function _(){let e=(0,n.A)(),{data:t,error:r}=await e.from("users").select("count",{count:"exact"});if(!r)return t[0].count}async function w(e){let t=(0,n.A)().from("users").select("created_at").gte("created_at",e);t=t.order("created_at",{ascending:!0});let{data:r,error:a}=await t;if(a)return;let i=new Map;return r.forEach(e=>{let t=e.created_at.split("T")[0];i.set(t,(i.get(t)||0)+1)}),i}},44962:(e,t,r)=>{"use strict";r.d(t,{N1:()=>i,ai:()=>n,ox:()=>s,zz:()=>o});var a=r(7279),n=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").insert(e);if(n)throw n;return r}async function s(e,t=1,r=50){let n=(t-1)*r,i=(0,a.A)(),{data:o,error:c}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(n,n+r-1);if(!c)return o}async function o(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!n)return r?.user_uuid}},45590:()=>{},52367:()=>{},53908:(e,t,r)=>{"use strict";r.d(t,{$c:()=>u,TG:()=>d,qo:()=>f,ug:()=>_});var a=r(30354),n=r(33102),i=r(11793),s=r(88661),o=r(44962),c=r(69803);async function u(e){try{let t=await (0,n.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,n.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,s.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await l();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,i.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function l(){let e=(await (0,c.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function f(){let e="",t=await (0,i.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function _(){let e=await d();if(e)return await (0,n.pX)(e)}},67332:(e,t,r)=>{"use strict";r.d(t,{Ni:()=>n,_3:()=>o,jR:()=>i,uo:()=>s});var a=r(7279);async function n(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("credits").insert(e);if(n)throw n;return r}async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("credits").select("*").eq("order_no",e).limit(1).single();if(!n)return r}async function s(e){let t=new Date().toISOString(),r=(0,a.A)(),{data:n,error:i}=await r.from("credits").select("*").eq("user_uuid",e).gte("expired_at",t).order("expired_at",{ascending:!0});if(!i)return n}async function o(e,t=1,r=50){let n=(0,a.A)(),{data:i,error:s}=await n.from("credits").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(!s)return i}},87009:(e,t,r)=>{"use strict";r.d(t,{BJ:()=>u,El:()=>n,I5:()=>c,K7:()=>i,Kv:()=>o,PG:()=>d,Yc:()=>f,Yw:()=>s,jE:()=>l,nz:()=>_});var a=r(7279);async function n(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("orders").insert(e);if(n)throw n;return r}async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("order_no",e).single();if(!n)return r}async function s(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!0}).limit(1).single();if(!n)return r}async function o(e,t,r,n,i){let s=(0,a.A)(),{data:o,error:c}=await s.from("orders").update({status:t,paid_at:r,paid_detail:i,paid_email:n}).eq("order_no",e);if(c)throw c;return o}async function c(e,t,r){let n=(0,a.A)(),{data:i,error:s}=await n.from("orders").update({stripe_session_id:t,order_detail:r}).eq("order_no",e);if(s)throw s;return i}async function u(e){new Date().toISOString();let t=(0,a.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!1});if(!n)return r}async function d(e){new Date().toISOString();let t=(0,a.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("paid_email",e).eq("status","paid").order("created_at",{ascending:!1});if(!n)return r}async function l(e,t){let r=(0,a.A)(),{data:n,error:i}=await r.from("orders").select("*").eq("status","paid").order("created_at",{ascending:!1}).range((e-1)*t,e*t);if(!i)return n}async function f(){let e=(0,a.A)(),{data:t,error:r}=await e.from("orders").select("count",{count:"exact"}).eq("status","paid");if(!r)return t[0].count}async function _(e,t){let r=(0,a.A)().from("orders").select("created_at").gte("created_at",e);t&&(r=r.eq("status",t)),r=r.order("created_at",{ascending:!0});let{data:n,error:i}=await r;if(i)return;let s=new Map;return n.forEach(e=>{let t=e.created_at.split("T")[0];s.set(t,(s.get(t)||0)+1)}),s}},88661:(e,t,r)=>{"use strict";function a(){return new Date().toISOString()}r.d(t,{MI:()=>n,iq:()=>a});let n=()=>{let e=new Date,t=new Date(e);return t.setFullYear(e.getFullYear()+1),t.toISOString()}}};