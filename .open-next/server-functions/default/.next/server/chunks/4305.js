exports.id=4305,exports.ids=[4305],exports.modules={7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(54209);function n(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,i.UU)(e,t)}},14584:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>a,ZK:()=>o,f1:()=>s});var i=r(25031),n=r(75184);function a(){return(0,n.A)()}function s(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",i=t.length;for(let n=0;n<e;n++)r+=t[Math.floor(Math.random()*i)];return r}function o(){return new i.F({workerId:1}).NextId().toString()}},24608:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var i=r(30354),n=r(87009),a=r(88661),s=r(35482),o=r(30647),u=r(33102);async function d(e){try{let t=await (0,u.pX)(e.user_uuid);if(t&&t.uuid&&t.invited_by&&t.invited_by!==t.uuid){if(await (0,s.N$)(e.order_no))return;await (0,s.Rt)({user_uuid:t.uuid,invited_by:t.invited_by,created_at:(0,a.iq)(),status:o.OJ.Completed,paid_order_no:e.order_no,paid_amount:e.amount,reward_percent:o.oR.Paied,reward_amount:o.h1.Paied})}}catch(e){throw console.log("update affiliate for order failed: ",e),e}}async function c(e){try{if(!e||!e.metadata||!e.metadata.order_no||"paid"!==e.payment_status)throw Error("invalid session");let t=e.metadata.order_no,r=e.customer_details?.email||e.customer_email||"",s=JSON.stringify(e),o=await (0,n.K7)(t);if(!o||"created"!==o.status)throw Error("invalid order");let u=(0,a.iq)();await (0,n.Kv)(t,"paid",u,r,s),o.user_uuid&&(o.credits>0&&await (0,i.OI)(o),await d(o)),console.log("handle order session successed: ",t,u,r,s)}catch(e){throw console.log("handle order session failed: ",e),e}}},25031:(e,t,r)=>{"use strict";t.F=void 0;let i=r(74698);Object.defineProperty(t,"F",{enumerable:!0,get:function(){return i.SnowflakeIdv1}})},30354:(e,t,r)=>{"use strict";r.d(t,{H3:()=>o,OI:()=>_,Ty:()=>f,d_:()=>l,jL:()=>m,ll:()=>h,mP:()=>d,nX:()=>c,rN:()=>u});var i=r(67332),n=r(87009),a=r(88661),s=r(14584),o=function(e){return e.NewUser="new_user",e.OrderPay="order_pay",e.SystemAdd="system_add",e.Ping="ping",e.TextGeneration="text_generation",e.ImageGeneration="image_generation",e.VideoGeneration="video_generation",e.AIModelUsage="ai_model_usage",e}({}),u=function(e){return e[e.NewUserGet=100]="NewUserGet",e[e.PingCost=1]="PingCost",e[e.TextGenerationBase=5]="TextGenerationBase",e[e.ImageGenerationBase=50]="ImageGenerationBase",e[e.VideoGenerationBase=200]="VideoGenerationBase",e}({});async function d(e){let t={left_credits:0};try{await (0,n.Yw)(e)&&(t.is_recharged=!0);let r=await (0,i.uo)(e);return r&&r.forEach(e=>{t.left_credits+=e.credits}),t.left_credits<0&&(t.left_credits=0),t.left_credits>0&&(t.is_pro=!0),t}catch(e){return console.log("get user credits failed: ",e),t}}async function c({user_uuid:e,trans_type:t,credits:r}){try{let n="",o="",u=0,d=await (0,i.uo)(e);if(d)for(let e=0,t=d.length;e<t;e++){let t=d[e];if((u+=t.credits)>=r){n=t.order_no,o=t.expired_at||"";break}}let c={trans_no:(0,s.ZK)(),created_at:(0,a.iq)(),user_uuid:e,trans_type:t,credits:0-r,order_no:n,expired_at:o};await (0,i.Ni)(c)}catch(e){throw console.log("decrease credits failed: ",e),e}}async function l({user_uuid:e,trans_type:t,credits:r,expired_at:n,order_no:o}){try{let u={trans_no:(0,s.ZK)(),created_at:(0,a.iq)(),user_uuid:e,trans_type:t,credits:r,order_no:o||"",expired_at:n||""};await (0,i.Ni)(u)}catch(e){throw console.log("increase credits failed: ",e),e}}async function _(e){try{if(await (0,i.jR)(e.order_no))return;await l({user_uuid:e.user_uuid,trans_type:"order_pay",credits:e.credits,expired_at:e.expired_at,order_no:e.order_no})}catch(e){throw console.log("update credit for order failed: ",e),e}}async function f({user_uuid:e,model_id:t,request_id:r,credits:n,trans_type:o="ai_model_usage"}){try{let u="",d="",c=0,l=await (0,i.uo)(e);if(l)for(let e=0,t=l.length;e<t;e++){let t=l[e];if((c+=t.credits)>=n){u=t.order_no,d=t.expired_at||"";break}}if(c<n)throw Error("Insufficient credits");let _={trans_no:(0,s.ZK)(),created_at:(0,a.iq)(),user_uuid:e,trans_type:o,credits:0-n,order_no:u,expired_at:d,model_id:t,request_id:r};await (0,i.Ni)(_)}catch(e){throw console.log("decrease credits for AI model failed: ",e),e}}async function m({user_uuid:e,model_id:t,request_id:r,credits:n,original_trans_no:o}){try{let o={trans_no:(0,s.ZK)(),created_at:(0,a.iq)(),user_uuid:e,trans_type:"ai_model_refund",credits:n,order_no:"",expired_at:(0,a.MI)(),model_id:t,request_id:r};await (0,i.Ni)(o)}catch(e){throw console.log("refund credits for AI model failed: ",e),e}}async function h(e,t){try{return(await d(e)).left_credits>=t}catch(e){return console.log("check sufficient credits failed: ",e),!1}}},30647:(e,t,r)=>{"use strict";r.d(t,{OJ:()=>i,h1:()=>a,oR:()=>n});let i={Pending:"pending",Completed:"completed"},n={Invited:0,Paied:20},a={Invited:0,Paied:5e3}},33102:(e,t,r)=>{"use strict";r.d(t,{Gs:()=>s,HW:()=>a,Nh:()=>c,PD:()=>f,QZ:()=>l,XQ:()=>d,in:()=>m,lo:()=>u,pX:()=>o,y_:()=>_});var i=r(88661),n=r(7279);async function a(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").insert(e);if(i)throw i;return r}async function s(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("email",e).limit(1).single();if(!i)return r}async function o(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("uuid",e).single();if(!i)return r}async function u(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,i=(0,n.A)(),{data:a,error:s}=await i.from("users").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(!s)return a}async function d(e,t){let r=(0,n.A)(),a=(0,i.iq)(),{data:s,error:o}=await r.from("users").update({invite_code:t,updated_at:a}).eq("uuid",e);if(o)throw o;return s}async function c(e,t){let r=(0,n.A)(),a=(0,i.iq)(),{data:s,error:o}=await r.from("users").update({invited_by:t,updated_at:a}).eq("uuid",e);if(o)throw o;return s}async function l(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").in("uuid",e);return i?[]:r}async function _(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("invite_code",e).single();if(!i)return r}async function f(){let e=(0,n.A)(),{data:t,error:r}=await e.from("users").select("count",{count:"exact"});if(!r)return t[0].count}async function m(e){let t=(0,n.A)().from("users").select("created_at").gte("created_at",e);t=t.order("created_at",{ascending:!0});let{data:r,error:i}=await t;if(i)return;let a=new Map;return r.forEach(e=>{let t=e.created_at.split("T")[0];a.set(t,(a.get(t)||0)+1)}),a}},35482:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,Ex:()=>s,N$:()=>u,Rt:()=>a});var i=r(7279),n=r(33102);async function a(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").insert({user_uuid:e.user_uuid,invited_by:e.invited_by,created_at:e.created_at,status:e.status,paid_order_no:e.paid_order_no,paid_amount:e.paid_amount,reward_percent:e.reward_percent,reward_amount:e.reward_amount});if(n)throw n;return r}async function s(e,t=1,r=50){let a=(0,i.A)(),{data:o,error:u}=await a.from("affiliates").select("*").eq("invited_by",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r);if(u)return console.error("Error fetching user invites:",u),[];if(!o||0===o.length)return;let d=Array.from(new Set(o.map(e=>e.user_uuid))),c=await (0,n.QZ)(d);return o.map(e=>{let t=c.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function o(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").select("*").eq("invited_by",e),a={total_invited:0,total_paid:0,total_reward:0};if(n)return a;let s=new Set,o=new Set;return r.forEach(e=>{s.add(e.user_uuid),e.paid_amount>0&&(o.add(e.user_uuid),a.total_reward+=e.reward_amount)}),a.total_invited=s.size,a.total_paid=o.size,a}async function u(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").select("*").eq("paid_order_no",e).single();if(!n)return r}},45590:()=>{},52367:()=>{},54467:()=>{},67332:(e,t,r)=>{"use strict";r.d(t,{Ni:()=>n,_3:()=>o,jR:()=>a,uo:()=>s});var i=r(7279);async function n(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("credits").insert(e);if(n)throw n;return r}async function a(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("credits").select("*").eq("order_no",e).limit(1).single();if(!n)return r}async function s(e){let t=new Date().toISOString(),r=(0,i.A)(),{data:n,error:a}=await r.from("credits").select("*").eq("user_uuid",e).gte("expired_at",t).order("expired_at",{ascending:!0});if(!a)return n}async function o(e,t=1,r=50){let n=(0,i.A)(),{data:a,error:s}=await n.from("credits").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(!s)return a}},74698:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SnowflakeIdv1=void 0;class r{constructor(e){if(void 0===e.workerId)throw Error("lost WorkerId");(!e.baseTime||e.baseTime<0)&&(e.baseTime=15778368e5),(!e.workerIdBitLength||e.workerIdBitLength<0)&&(e.workerIdBitLength=6),(!e.seqBitLength||e.seqBitLength<0)&&(e.seqBitLength=6),(void 0==e.maxSeqNumber||e.maxSeqNumber<=0)&&(e.maxSeqNumber=63),(void 0==e.minSeqNumber||e.minSeqNumber<0)&&(e.minSeqNumber=5),(void 0==e.topOverCostCount||e.topOverCostCount<0)&&(e.topOverCostCount=2e3),2!==e.method?e.method=1:e.method=2,this.Method=BigInt(e.method),this.BaseTime=BigInt(e.baseTime),this.WorkerId=BigInt(e.workerId),this.WorkerIdBitLength=BigInt(e.workerIdBitLength),this.SeqBitLength=BigInt(e.seqBitLength),this.MaxSeqNumber=BigInt(e.maxSeqNumber),this.MinSeqNumber=BigInt(e.minSeqNumber),this.TopOverCostCount=BigInt(e.topOverCostCount);let t=this.WorkerIdBitLength+this.SeqBitLength,r=this.MinSeqNumber;this._TimestampShift=t,this._CurrentSeqNumber=r,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(e){}EndOverCostAction(e){}BeginTurnBackAction(e){}EndTurnBackAction(e){}NextOverCostId(){let e=this.GetCurrentTimeTick();return e>this._LastTimeTick?(this.EndOverCostAction(e),this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(e),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let e=this.GetCurrentTimeTick();return e<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),e>this._LastTimeTick)?(this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(e),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,t}CalcTurnBackId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,t}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let e=this.GetCurrentTimeTick();for(;e<=this._LastTimeTick;)e=this.GetCurrentTimeTick();return e}NextNumber(){if(this._IsOverCost){let e=this.NextOverCostId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}{let e=this.NextNormalId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}}NextId(){if(this._IsOverCost){let e=this.NextOverCostId();return e>=9007199254740992n?e:parseInt(e.toString())}{let e=this.NextNormalId();return e>=9007199254740992n?e:parseInt(e.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}t.SnowflakeIdv1=r},75184:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(55511);let n={randomUUID:i.randomUUID},a=new Uint8Array(256),s=a.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(n.randomUUID&&!t&&!e)return n.randomUUID();let u=(e=e||{}).random??e.rng?.()??(s>a.length-16&&((0,i.randomFillSync)(a),s=0),a.slice(s,s+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(u)}},78443:()=>{},87009:(e,t,r)=>{"use strict";r.d(t,{BJ:()=>d,El:()=>n,I5:()=>u,K7:()=>a,Kv:()=>o,PG:()=>c,Yc:()=>_,Yw:()=>s,jE:()=>l,nz:()=>f});var i=r(7279);async function n(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("orders").insert(e);if(n)throw n;return r}async function a(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("order_no",e).single();if(!n)return r}async function s(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!0}).limit(1).single();if(!n)return r}async function o(e,t,r,n,a){let s=(0,i.A)(),{data:o,error:u}=await s.from("orders").update({status:t,paid_at:r,paid_detail:a,paid_email:n}).eq("order_no",e);if(u)throw u;return o}async function u(e,t,r){let n=(0,i.A)(),{data:a,error:s}=await n.from("orders").update({stripe_session_id:t,order_detail:r}).eq("order_no",e);if(s)throw s;return a}async function d(e){new Date().toISOString();let t=(0,i.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!1});if(!n)return r}async function c(e){new Date().toISOString();let t=(0,i.A)(),{data:r,error:n}=await t.from("orders").select("*").eq("paid_email",e).eq("status","paid").order("created_at",{ascending:!1});if(!n)return r}async function l(e,t){let r=(0,i.A)(),{data:n,error:a}=await r.from("orders").select("*").eq("status","paid").order("created_at",{ascending:!1}).range((e-1)*t,e*t);if(!a)return n}async function _(){let e=(0,i.A)(),{data:t,error:r}=await e.from("orders").select("count",{count:"exact"}).eq("status","paid");if(!r)return t[0].count}async function f(e,t){let r=(0,i.A)().from("orders").select("created_at").gte("created_at",e);t&&(r=r.eq("status",t)),r=r.order("created_at",{ascending:!0});let{data:n,error:a}=await r;if(a)return;let s=new Map;return n.forEach(e=>{let t=e.created_at.split("T")[0];s.set(t,(s.get(t)||0)+1)}),s}},88661:(e,t,r)=>{"use strict";function i(){return new Date().toISOString()}r.d(t,{MI:()=>n,iq:()=>i});let n=()=>{let e=new Date,t=new Date(e);return t.setFullYear(e.getFullYear()+1),t.toISOString()}}};