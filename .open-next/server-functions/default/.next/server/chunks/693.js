exports.id=693,exports.ids=[693],exports.modules={4850:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(73636),s=r(47830),o=r(13266),n=(0,a.cache)(function(e,t){return function({_cache:e=(0,o.d)(),_formatters:t=(0,o.b)(e),getMessageFallback:r=o.f,messages:a,namespace:s,onError:n=o.g,...i}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,o.r)(t,"!"),(0,o.e)({...r,messages:e,namespace:t})}({...i,onError:n,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:s?`!.${s}`:"!"},"!")}({...e,namespace:t})}),i=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,s.A)(r),t)})},6385:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(18542),s=r(34757),o=r(25323),n="horizontal",i=["horizontal","vertical"],l=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:l=n,...c}=e,d=(r=l,i.includes(r))?l:n;return(0,o.jsx)(s.sG.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},17121:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>n});var a=r(25323);r(18542);var s=r(6385),o=r(91145);function n({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return(0,a.jsx)(s.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}},21490:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(96081),s=r(47720),o=r(26343),n=r(29753);function i({items:e}){return(0,a.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?(0,a.jsx)(n.N_,{href:e.url,children:(0,a.jsxs)(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):(0,a.jsxs)(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},21956:(e,t,r)=>{Promise.resolve().then(r.bind(r,51419)),Promise.resolve().then(r.bind(r,93497))},23044:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx","default")},23483:(e,t,r)=>{Promise.resolve().then(r.bind(r,94658)),Promise.resolve().then(r.bind(r,13872)),Promise.resolve().then(r.bind(r,17121)),Promise.resolve().then(r.bind(r,59656)),Promise.resolve().then(r.bind(r,93497))},24747:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/separator.tsx","Separator")},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(96081);r(73636);var s=r(21280),o=r(80436),n=r(73515);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...o}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),e),...o})}},47720:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(96081);r(73636);var s=r(21280),o=r(80436),n=r(73515);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:o=!1,...l}){let c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:r,className:e})),...l})}},47944:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(96081),s=r(79654);function o({value:e,options:t,className:r}){return(0,a.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var n=r(42711);function i({value:e,options:t,className:r}){return(0,a.jsx)(n.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(64694),c=r(3116);function d({value:e,options:t,className:r}){return(0,a.jsx)("div",{className:r,children:t?.format?(0,l.GP)(new Date(e),t?.format):(0,c.m)(new Date(e),{addSuffix:!0})})}var u=r(23044);function m({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,a.jsxs)(s.Table,{className:"w-full",children:[(0,a.jsx)(s.TableHeader,{className:"",children:(0,a.jsx)(s.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,a.jsx)(s.TableHead,{className:e.className,children:e.title},t))})}),(0,a.jsx)(s.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,a.jsx)(s.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let n=t[e.name],l=e.callback?e.callback(t):n,c=l;return"image"===e.type?c=(0,a.jsx)(o,{value:n,options:e.options,className:e.className}):"time"===e.type?c=(0,a.jsx)(d,{value:n,options:e.options,className:e.className}):"label"===e.type?c=(0,a.jsx)(i,{value:n,options:e.options,className:e.className}):"copy"===e.type&&n&&(c=(0,a.jsx)(u.default,{text:n,children:l})),(0,a.jsx)(s.TableCell,{className:e.className,children:c},r)})},r)):(0,a.jsx)(s.TableRow,{className:"",children:(0,a.jsx)(s.TableCell,{colSpan:e.length,children:(0,a.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:r})})})})})]})}},48468:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(96081),s=r(24747),o=r(47944),n=r(21490);function i({...e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.tip&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.tip.description||e.tip.title}),e.toolbar&&(0,a.jsx)(n.A,{items:e.toolbar.items}),(0,a.jsx)(s.Separator,{}),(0,a.jsx)(o.A,{...e})]})}},51406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(96081),s=r(74405);async function o({children:e,sidebar:t}){return(0,a.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,a.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[t?.nav?.items&&(0,a.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,a.jsx)(s.default,{items:t.nav?.items})}),(0,a.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var n=r(4850),i=r(53908),l=r(17984);async function c({children:e}){let t=await (0,i.ug)();t&&t.email||(0,l.redirect)("/auth/signin");let r=await (0,n.A)(),s={nav:{items:[{title:r("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:r("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:r("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:r("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,a.jsx)(o,{sidebar:s,children:e})}},51419:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(25323),s=r(13872),o=r(2783),n=r(52510),i=r(91145),l=r(41025);function c({className:e,items:t,...r}){let c=(0,l.usePathname)();return console.log(c),(0,a.jsx)("nav",{className:(0,i.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...r,children:t.map((e,t)=>(0,a.jsxs)(o.N_,{href:e.url,className:(0,i.cn)((0,n.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,a.jsx)(s.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>a.T});var a=r(10061)},57827:(e,t,r)=>{Promise.resolve().then(r.bind(r,23044)),Promise.resolve().then(r.bind(r,26343)),Promise.resolve().then(r.bind(r,24747)),Promise.resolve().then(r.bind(r,79654)),Promise.resolve().then(r.bind(r,76735))},59656:(e,t,r)=>{"use strict";r.d(t,{Table:()=>o,TableBody:()=>i,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>n,TableRow:()=>l});var a=r(25323);r(18542);var s=r(91145);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},74405:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx","default")},79654:(e,t,r)=>{"use strict";r.d(t,{Table:()=>s,TableBody:()=>n,TableCell:()=>c,TableHead:()=>i,TableHeader:()=>o,TableRow:()=>l});var a=r(20263);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","Table"),o=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHeader"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableBody");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableFooter");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHead"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableRow"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCell");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCaption")},94658:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(25323),s=r(13180),o=r(95234);function n({text:e,children:t}){return(0,a.jsx)(s.CopyToClipboard,{text:e,onCopy:()=>o.oR.success("Copied"),children:(0,a.jsx)("div",{className:"cursor-pointer",children:t})})}},97988:(e,t,r)=>{Promise.resolve().then(r.bind(r,74405)),Promise.resolve().then(r.bind(r,76735))}};