exports.id=2742,exports.ids=[2742],exports.modules={164:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(25323);r(18542);var n=r(91145);function s({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},2783:(e,t,r)=>{"use strict";r.d(t,{N_:()=>i,a8:()=>l,rd:()=>d});var a=r(33531),n=r(39544);let s=(0,r(96633).A)({locales:a.IB,defaultLocale:a.q,localePrefix:a.b,pathnames:a.u7,localeDetection:a.GB}),{Link:i,redirect:o,usePathname:l,useRouter:d}=(0,n.A)(s)},11329:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/dashboard/sidebar/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/dashboard/sidebar/index.tsx","default")},13872:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(25323),n=r(18542),s=r(45533),i=r(24089),o=r(82356);let l={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.RQr,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.LrS,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:i.sOK,GoThumbsup:o.VZG,GoArrowUpRight:o.zny},d=(0,n.memo)(({name:e,className:t,onClick:r,...n})=>{let s=l[e];return s?(0,a.jsx)(s,{className:t,onClick:r,style:{cursor:r?"pointer":"default"},...n}):null})},14309:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});var a=r(18542);function n(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},16710:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>u});var a=r(96081);r(73636);var n=r(73515);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},17121:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>i});var a=r(25323);r(18542);var n=r(6385),s=r(91145);function i({className:e,orientation:t="horizontal",decorative:r=!0,...i}){return(0,a.jsx)(n.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...i})}},17609:(e,t,r)=>{"use strict";r.d(t,{CG:()=>l,Fm:()=>m,Qs:()=>f,cj:()=>o,h:()=>u,qp:()=>p});var a=r(25323);r(18542);var n=r(61445),s=r(67564),i=r(91145);function o({...e}){return(0,a.jsx)(n.bL,{"data-slot":"sheet",...e})}function l({...e}){return(0,a.jsx)(n.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,a.jsx)(n.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,a.jsx)(n.hJ,{"data-slot":"sheet-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:r="right",...o}){return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(n.UC,{"data-slot":"sheet-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...o,children:[t,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(s.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,i.cn)("flex flex-col gap-1.5 p-4",e),...t})}function p({className:e,...t}){return(0,a.jsx)(n.hE,{"data-slot":"sheet-title",className:(0,i.cn)("text-foreground font-semibold",e),...t})}function f({className:e,...t}){return(0,a.jsx)(n.VY,{"data-slot":"sheet-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},21902:(e,t,r)=>{"use strict";r.d(t,{SidebarInset:()=>n,SidebarProvider:()=>s,SidebarTrigger:()=>i});var a=r(20263);(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","Sidebar"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarFooter"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarGroup"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarGroupAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarGroupContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarGroupLabel"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarHeader"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarInput");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarInset");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenu"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuBadge"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuSub"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuSubButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarMenuSubItem");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarRail"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarSeparator");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","SidebarTrigger");(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/sidebar.tsx","useSidebar")},24747:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/separator.tsx","Separator")},27659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(96081),n=r(21902),s=r(11329);function i({children:e,sidebar:t}){return(0,a.jsxs)(n.SidebarProvider,{style:{"--sidebar-width":"calc(var(--spacing) * 60)","--header-height":"calc(var(--spacing) * 12)"},children:[(0,a.jsx)(s.default,{variant:"inset",sidebar:t}),(0,a.jsx)(n.SidebarInset,{children:e})]})}var o=r(72160),l=r(53908),d=r(17984);async function c({children:e}){let t=await (0,l.ug)();t&&t.email||(0,d.redirect)("/auth/signin");let r=process.env.ADMIN_EMAILS?.split(",");return r?.includes(t?.email)?(0,a.jsx)(i,{sidebar:{brand:{title:"ShipAny",logo:{src:"/logo.png",alt:"ShipAny"},url:"/admin"},nav:{items:[{title:"Dashboard",url:"/admin",icon:"RiDashboardLine"}]},library:{title:"Menu",items:[{title:"Users",url:"/admin/users",icon:"RiUserLine"},{title:"Orders",icon:"RiOrderPlayLine",url:"/admin/orders"},{title:"Posts",url:"/admin/posts",icon:"RiArticleLine"},{title:"Feedbacks",url:"/admin/feedbacks",icon:"RiMessage2Line"}]},bottomNav:{items:[{title:"Documents",url:"https://docs.shipany.ai",target:"_blank",icon:"RiFileTextLine"},{title:"Blocks",url:"https://shipany.ai/blocks",target:"_blank",icon:"RiDashboardLine"},{title:"Showcases",url:"https://shipany.ai/showcase",target:"_blank",icon:"RiAppsLine"}]},social:{items:[{title:"Home",url:"/",target:"_blank",icon:"RiHomeLine"},{title:"Github",url:"https://github.com/shipanyai/shipany-template-one",target:"_blank",icon:"RiGithubLine"},{title:"Discord",url:"https://discord.gg/HQNnrzjZQS",target:"_blank",icon:"RiDiscordLine"},{title:"X",url:"https://x.com/shipanyai",target:"_blank",icon:"RiTwitterLine"}]},account:{items:[{title:"Home",url:"/",icon:"RiHomeLine",target:"_blank"},{title:"Recharge",url:"/pricing",icon:"RiMoneyDollarBoxLine",target:"_blank"}]}},children:e}):(0,a.jsx)(o.A,{message:"No access"})}},38071:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(96081),n=r(73636),s=r(82646),i=r(73515);function o({...e}){return(0,a.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function l({className:e,...t}){return(0,a.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,i.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function d({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,i.cn)("inline-flex items-center gap-1.5",e),...t})}function c({className:e,...t}){return(0,a.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("text-foreground font-normal",e),...t})}function u({children:e,className:t,...r}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,a.jsx)(s.A,{})})}var m=r(58044),p=r.n(m),f=r(24747),b=r(21902);function h({crumb:e}){return(0,a.jsx)("header",{className:"flex border-b border-border py-3 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,a.jsx)(b.SidebarTrigger,{className:"-ml-1"}),e&&e.items&&e.items.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.Separator,{orientation:"vertical",className:"mr-2 h-4"}),(0,a.jsx)(o,{children:(0,a.jsx)(l,{children:e.items.map((e,t)=>e.is_active?(0,a.jsx)(d,{children:(0,a.jsx)(c,{children:e.title})},t):(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(d,{className:"hidden md:block",children:(0,a.jsx)(p(),{href:e.url||"",className:"hover:text-primary",children:e.title})}),(0,a.jsx)(u,{className:"hidden md:block"})]},t))})})]})]})})}},41251:(e,t,r)=>{Promise.resolve().then(r.bind(r,11329)),Promise.resolve().then(r.bind(r,21902))},42109:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>i,AvatarImage:()=>o,q:()=>l});var a=r(25323);r(18542);var n=r(48714),s=r(91145);function i({className:e,...t}){return(0,a.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,a.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full object-cover",e),...t})}function l({className:e,...t}){return(0,a.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},55323:(e,t,r)=>{Promise.resolve().then(r.bind(r,68967)),Promise.resolve().then(r.bind(r,77679))},68967:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var a=r(25323),n=r(18542),s=r(77679),i=r(2783),o=r(13872),l=r(41025);function d({nav:e}){let t=(0,l.usePathname)();return(0,a.jsx)(s.Cn,{children:(0,a.jsx)(s.rQ,{className:"flex flex-col gap-2 mt-4",children:(0,a.jsx)(s.wZ,{children:e?.items?.map(e=>(0,a.jsx)(s.FX,{children:(0,a.jsx)(s.Uj,{tooltip:e.title,className:`${e.is_active||t.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""}`,children:e.url?(0,a.jsxs)(i.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon}),(0,a.jsx)("span",{children:e.title})]}):(0,a.jsxs)(a.Fragment,{children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon}),(0,a.jsx)("span",{children:e.title})]})})},e.title))})})})}var c=r(14622),u=r(42109),m=r(71163),p=r(88091),f=r(69685),b=r(52510),h=r(68560),x=r(3124),g=r(50533);function v({account:e}){let t=(0,g.c3)(),{user:r,setShowSignModal:l}=(0,x.U)(),{isMobile:d,open:c}=(0,s.cL)();return(0,a.jsx)(a.Fragment,{children:r?(0,a.jsxs)(s.wZ,{className:"gap-4",children:[!c&&(0,a.jsx)(s.FX,{children:(0,a.jsx)(s.Uj,{className:"cursor-pointer",asChild:!0,children:(0,a.jsx)(s.SidebarTrigger,{})})}),(0,a.jsx)(s.FX,{children:(0,a.jsxs)(f.rI,{children:[(0,a.jsx)(f.ty,{asChild:!0,children:(0,a.jsxs)(s.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,a.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,a.jsx)(u.AvatarImage,{src:r?.avatar_url,alt:r?.nickname}),(0,a.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:r.nickname}),(0,a.jsx)("span",{className:"truncate text-xs",children:r.email})]}),(0,a.jsx)(m.A,{className:"ml-auto size-4"})]})}),(0,a.jsxs)(f.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg bg-background",side:d?"bottom":"right",align:"end",sideOffset:4,children:[(0,a.jsx)(f.lp,{className:"p-0 font-normal",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,a.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,a.jsx)(u.AvatarImage,{src:r?.avatar_url,alt:r?.nickname}),(0,a.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:r?.nickname}),(0,a.jsx)("span",{className:"truncate text-xs",children:r?.email})]})]})}),(0,a.jsx)(f.mB,{}),(0,a.jsxs)(f.I,{children:[e?.items?.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(f._2,{className:"cursor-pointer",children:(0,a.jsxs)(i.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2",children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon}),e.title]})}),(0,a.jsx)(f.mB,{})]},t)),(0,a.jsxs)(f._2,{className:"cursor-pointer",onClick:()=>(0,h.CI)(),children:[(0,a.jsx)(p.A,{}),t("user.sign_out")]})]})]})]})})]}):(0,a.jsx)(a.Fragment,{children:c?(0,a.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-4",children:(0,a.jsx)(b.$,{className:"w-full",onClick:()=>l(!0),children:t("user.sign_in")})}):(0,a.jsx)(s.wZ,{children:(0,a.jsx)(s.FX,{children:(0,a.jsx)(s.Uj,{className:"cursor-pointer",asChild:!0,children:(0,a.jsx)(s.SidebarTrigger,{})})})})})})}function j({social:e}){let{open:t}=(0,s.cL)();return(0,a.jsx)(a.Fragment,{children:t?(0,a.jsx)("div",{className:"w-full flex items-center justify-center mx-auto gap-x-4 px-4 py-4 border-t border-gray-200",children:e?.items?.map((e,t)=>(0,a.jsx)("div",{className:"cursor-pointer hover:text-primary",children:(0,a.jsx)(i.N_,{href:e.url,target:e.target||"_self",className:"cursor-pointer",children:e.icon&&(0,a.jsx)(o.default,{name:e.icon,className:"text-xl"})})},t))}):null})}var w=r(32512);function y({library:e}){let{isMobile:t}=(0,s.cL)(),r=(0,i.a8)();return(0,a.jsxs)(s.Cn,{className:"group-data-[collapsible=icon]:hidden",children:[(0,a.jsx)(s.jj,{children:e.title}),(0,a.jsxs)(s.wZ,{children:[e.items?.map((e,t)=>(0,a.jsxs)(s.FX,{children:[(0,a.jsx)(s.Uj,{tooltip:e.title,className:`${e.is_active||r.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""}`,children:(0,a.jsxs)(i.N_,{href:e.url||"",target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon}),(0,a.jsx)("span",{children:e.title})]})}),(0,a.jsx)(f.rI,{})]},t)),e.more&&(0,a.jsx)(s.FX,{children:(0,a.jsxs)(s.Uj,{className:"text-sidebar-foreground/70",children:[(0,a.jsx)(w.A,{className:"text-sidebar-foreground/70"}),(0,a.jsx)("span",{children:e.more.title})]})})]})]})}function C({nav:e,...t}){return(0,a.jsx)(s.Cn,{...t,children:(0,a.jsx)(s.rQ,{children:(0,a.jsx)(s.wZ,{children:e.items?.map((e,t)=>(0,a.jsx)(s.FX,{children:(0,a.jsx)(s.Uj,{asChild:!0,children:(0,a.jsxs)(i.N_,{href:e.url,target:e.target,children:[e.icon&&(0,a.jsx)(o.default,{name:e.icon}),(0,a.jsx)("span",{children:e.title})]})})},t))})})})}function S({sidebar:e,...t}){return(0,a.jsxs)(s.Bx,{collapsible:"offcanvas",...t,children:[(0,a.jsx)(s.Gh,{children:(0,a.jsx)(s.wZ,{children:(0,a.jsx)(s.FX,{children:(0,a.jsx)(s.Uj,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:(0,a.jsxs)(i.N_,{href:e.brand?.url,className:"flex items-center gap-2",children:[e.brand?.logo&&(0,a.jsx)(c.default,{src:e.brand?.logo?.src,alt:e.brand?.title,width:28,height:28,className:"rounded-full"}),(0,a.jsx)("span",{className:"text-base font-semibold",children:e.brand?.title})]})})})})}),(0,a.jsxs)(s.Yv,{children:[e.nav&&(0,a.jsx)(d,{nav:e.nav}),e.library&&(0,a.jsx)(y,{library:e.library}),e.bottomNav&&(0,a.jsx)(C,{nav:e.bottomNav,className:"mt-auto"})]}),(0,a.jsxs)(s.CG,{children:[(0,a.jsx)(v,{account:e.account}),e?.social&&(0,a.jsx)(j,{social:e.social})]})]})}},69685:(e,t,r)=>{"use strict";r.d(t,{I:()=>d,SQ:()=>l,_2:()=>c,lp:()=>u,mB:()=>m,rI:()=>i,ty:()=>o});var a=r(25323);r(18542);var n=r(7738),s=r(91145);function i({...e}){return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function d({...e}){return(0,a.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...e})}function c({className:e,inset:t,variant:r="default",...i}){return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function u({className:e,inset:t,...r}){return(0,a.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function m({className:e,...t}){return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},72160:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(96081);function n({message:e}){return(0,a.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,a.jsx)("p",{children:e})})}},77679:(e,t,r)=>{"use strict";r.d(t,{Bx:()=>j,Yv:()=>N,CG:()=>S,Cn:()=>k,rQ:()=>M,jj:()=>R,Gh:()=>C,SidebarInset:()=>y,wZ:()=>A,Uj:()=>_,FX:()=>L,SidebarProvider:()=>v,SidebarTrigger:()=>w,cL:()=>g});var a=r(25323),n=r(18542),s=r(48771),i=r(51238),o=r(64038),l=r(14309),d=r(91145),c=r(52510);r(164),r(17121);var u=r(17609),m=r(1369);function p({delayDuration:e=0,...t}){return(0,a.jsx)(m.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function f({...e}){return(0,a.jsx)(p,{children:(0,a.jsx)(m.bL,{"data-slot":"tooltip",...e})})}function b({...e}){return(0,a.jsx)(m.l9,{"data-slot":"tooltip-trigger",...e})}function h({className:e,sideOffset:t=0,children:r,...n}){return(0,a.jsx)(m.ZL,{children:(0,a.jsxs)(m.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,d.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[r,(0,a.jsx)(m.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let x=n.createContext(null);function g(){let e=n.useContext(x);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function v({defaultOpen:e=!0,open:t,onOpenChange:r,className:s,style:i,children:o,...c}){let u=(0,l.a)(),[m,f]=n.useState(!1),[b,h]=n.useState(e),g=t??b,v=n.useCallback(e=>{let t="function"==typeof e?e(g):e;r?r(t):h(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[r,g]),j=n.useCallback(()=>u?f(e=>!e):v(e=>!e),[u,v,f]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),j())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j]);let w=g?"expanded":"collapsed",y=n.useMemo(()=>({state:w,open:g,setOpen:v,isMobile:u,openMobile:m,setOpenMobile:f,toggleSidebar:j}),[w,g,v,u,m,f,j]);return(0,a.jsx)(x.Provider,{value:y,children:(0,a.jsx)(p,{delayDuration:0,children:(0,a.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...c,children:o})})})}function j({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:s,...i}){let{isMobile:o,state:l,openMobile:c,setOpenMobile:m}=g();return"none"===r?(0,a.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...i,children:s}):o?(0,a.jsx)(u.cj,{open:c,onOpenChange:m,...i,children:(0,a.jsxs)(u.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,a.jsxs)(u.Fm,{className:"sr-only",children:[(0,a.jsx)(u.qp,{children:"Sidebar"}),(0,a.jsx)(u.Qs,{children:"Displays the mobile sidebar."})]}),(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,a.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,a.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,a.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...i,children:(0,a.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function w({className:e,onClick:t,...r}){let{toggleSidebar:n}=g();return(0,a.jsxs)(c.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",e),onClick:e=>{t?.(e),n()},...r,children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function y({className:e,...t}){return(0,a.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function C({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function S({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function N({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function k({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function R({className:e,asChild:t=!1,...r}){let n=t?s.DX:"div";return(0,a.jsx)(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function M({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,d.cn)("w-full text-sm",e),...t})}function A({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function L({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",e),...t})}let I=(0,i.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function _({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:i,className:o,...l}){let c=e?s.DX:"button",{isMobile:u,state:m}=g(),p=(0,a.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:(0,d.cn)(I({variant:r,size:n}),o),...l});return i?("string"==typeof i&&(i={children:i}),(0,a.jsxs)(f,{children:[(0,a.jsx)(b,{asChild:!0,children:p}),(0,a.jsx)(h,{side:"right",align:"center",hidden:"collapsed"!==m||u,...i})]})):p}}};