"use strict";exports.id=8723,exports.ids=[8723],exports.modules={7738:(e,t,r)=>{r.d(t,{UC:()=>eW,YJ:()=>ez,q7:()=>eQ,JU:()=>eJ,ZL:()=>eX,bL:()=>eY,wv:()=>e$,l9:()=>eq});var n=r(18542),o=r(16760),a=r(30061),l=r(20849),i=r(6772),u=r(34757),c=r(62159),d=r(2286),s=r(59425),f=r(99621),C=r(42020),p=r(13788),h=r(77842),g=r(37072),v=r(78869),m=r(95783),w=r(48771),x=r(69036),M=r(52793),L=r(35296),y=r(25323),H=["Enter"," "],V=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...V],b={ltr:[...H,"ArrowRight"],rtl:[...H,"ArrowLeft"]},j={ltr:["ArrowLeft"],rtl:["ArrowRight"]},R="Menu",[Z,D,P]=(0,c.N)(R),[_,S]=(0,l.A)(R,[P,h.Bk,m.RG]),E=(0,h.Bk)(),N=(0,m.RG)(),[I,O]=_(R),[T,A]=_(R),B=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=E(t),[c,s]=n.useState(null),f=n.useRef(!1),C=(0,x.c)(l),p=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,y.jsx)(h.bL,{...u,children:(0,y.jsx)(I,{scope:t,open:r,onOpenChange:C,content:c,onContentChange:s,children:(0,y.jsx)(T,{scope:t,onClose:n.useCallback(()=>C(!1),[C]),isUsingKeyboardRef:f,dir:p,modal:i,children:o})})})};B.displayName=R;var F=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=E(r);return(0,y.jsx)(h.Mz,{...o,...n,ref:t})});F.displayName="MenuAnchor";var G="MenuPortal",[K,U]=_(G,{forceMount:void 0}),Y=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=O(G,t);return(0,y.jsx)(K,{scope:t,forceMount:r,children:(0,y.jsx)(v.C,{present:r||a.open,children:(0,y.jsx)(g.Z,{asChild:!0,container:o,children:n})})})};Y.displayName=G;var q="MenuContent",[X,W]=_(q),z=n.forwardRef((e,t)=>{let r=U(q,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=O(q,e.__scopeMenu),l=A(q,e.__scopeMenu);return(0,y.jsx)(Z.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(v.C,{present:n||a.open,children:(0,y.jsx)(Z.Slot,{scope:e.__scopeMenu,children:l.modal?(0,y.jsx)(J,{...o,ref:t}):(0,y.jsx)(Q,{...o,ref:t})})})})}),J=n.forwardRef((e,t)=>{let r=O(q,e.__scopeMenu),l=n.useRef(null),i=(0,a.s)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,M.Eq)(e)},[]),(0,y.jsx)(ee,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=O(q,e.__scopeMenu);return(0,y.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,w.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:p,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:x,onDismiss:M,disableOutsideScroll:H,...b}=e,j=O(q,r),R=A(q,r),Z=E(r),P=N(r),_=D(r),[S,I]=n.useState(null),T=n.useRef(null),B=(0,a.s)(t,T,j.onContentChange),F=n.useRef(0),G=n.useRef(""),K=n.useRef(0),U=n.useRef(null),Y=n.useRef("right"),W=n.useRef(0),z=H?L.A:n.Fragment,J=e=>{let t=G.current+e,r=_().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,l=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===a)?.ref.current;!function e(t){G.current=t,window.clearTimeout(F.current),""!==t&&(F.current=window.setTimeout(()=>e(""),1e3))}(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(F.current),[]),(0,f.Oh)();let Q=n.useCallback(e=>Y.current===U.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],u=l.x,c=l.y,d=i.x,s=i.y;c>n!=s>n&&r<(d-u)*(n-c)/(s-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,U.current?.area),[]);return(0,y.jsx)(X,{scope:r,searchRef:G,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(T.current?.focus(),I(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:K,onPointerGraceIntentChange:n.useCallback(e=>{U.current=e},[]),children:(0,y.jsx)(z,{...H?{as:$,allowPinchZoom:!0}:void 0,children:(0,y.jsx)(C.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),T.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,y.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:x,onDismiss:M,children:(0,y.jsx)(m.bL,{asChild:!0,...P,dir:R.dir,orientation:"vertical",loop:l,currentTabStopId:S,onCurrentTabStopIdChange:I,onEntryFocus:(0,o.m)(p,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":ek(j.open),"data-radix-menu-content":"",dir:R.dir,...Z,...b,ref:B,style:{outline:"none",...b.style},onKeyDown:(0,o.m)(b.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&J(e.key));let o=T.current;if(e.target!==o||!k.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);V.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(F.current),G.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eR(e=>{let t=e.target,r=W.current!==e.clientX;e.currentTarget.contains(t)&&r&&(Y.current=e.clientX>W.current?"right":"left",W.current=e.clientX)}))})})})})})})});z.displayName=q;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...i}=e,c=n.useRef(null),d=A(en,e.__scopeMenu),s=W(en,e.__scopeMenu),f=(0,a.s)(t,c),C=n.useRef(!1);return(0,y.jsx)(el,{...i,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>l?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?C.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),C.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{C.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==s.searchRef.current;r||t&&" "===e.key||H.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var el=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:i,...c}=e,d=W(en,r),s=N(r),f=n.useRef(null),C=(0,a.s)(t,f),[p,h]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&v((e.textContent??"").trim())},[c.children]),(0,y.jsx)(Z.ItemSlot,{scope:r,disabled:l,textValue:i??g,children:(0,y.jsx)(m.q7,{asChild:!0,...s,focusable:!l,children:(0,y.jsx)(u.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:C,onPointerMove:(0,o.m)(e.onPointerMove,eR(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eR(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),ei=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,y.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eb(r)?"mixed":r,...a,ref:t,"data-state":ej(r),onSelect:(0,o.m)(a.onSelect,()=>n?.(!!eb(r)||!r),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,ed]=_(eu,{value:void 0,onValueChange:()=>{}}),es=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,x.c)(n);return(0,y.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,y.jsx)(et,{...o,ref:t})})});es.displayName=eu;var ef="MenuRadioItem",eC=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ed(ef,e.__scopeMenu),l=r===a.value;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:l,children:(0,y.jsx)(ea,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":ej(l),onSelect:(0,o.m)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eC.displayName=ef;var ep="MenuItemIndicator",[eh,eg]=_(ep,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eg(ep,r);return(0,y.jsx)(v.C,{present:n||eb(a.checked)||!0===a.checked,children:(0,y.jsx)(u.sG.span,{...o,ref:t,"data-state":ej(a.checked)})})});ev.displayName=ep;var em=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});em.displayName="MenuSeparator";var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=E(r);return(0,y.jsx)(h.i3,{...o,...n,ref:t})});ew.displayName="MenuArrow";var[ex,eM]=_("MenuSub"),eL="MenuSubTrigger",ey=n.forwardRef((e,t)=>{let r=O(eL,e.__scopeMenu),l=A(eL,e.__scopeMenu),i=eM(eL,e.__scopeMenu),u=W(eL,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:s}=u,f={__scopeMenu:e.__scopeMenu},C=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>C,[C]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),s(null)}},[d,s]),(0,y.jsx)(F,{asChild:!0,...f,children:(0,y.jsx)(el,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":ek(r.open),...e,ref:(0,a.t)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eR(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),C()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eR(e=>{C();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],l=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;e.disabled||n&&" "===t.key||b[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});ey.displayName=eL;var eH="MenuSubContent",eV=n.forwardRef((e,t)=>{let r=U(q,e.__scopeMenu),{forceMount:l=r.forceMount,...i}=e,u=O(q,e.__scopeMenu),c=A(q,e.__scopeMenu),d=eM(eH,e.__scopeMenu),s=n.useRef(null),f=(0,a.s)(t,s);return(0,y.jsx)(Z.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(v.C,{present:l||u.open,children:(0,y.jsx)(Z.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&s.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=j[c.dir].includes(e.key);t&&r&&(u.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function ek(e){return e?"open":"closed"}function eb(e){return"indeterminate"===e}function ej(e){return eb(e)?"indeterminate":e?"checked":"unchecked"}function eR(e){return t=>"mouse"===t.pointerType?e(t):void 0}eV.displayName=eH;var eZ="DropdownMenu",[eD,eP]=(0,l.A)(eZ,[S]),e_=S(),[eS,eE]=eD(eZ),eN=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,d=e_(t),s=n.useRef(null),[f,C]=(0,i.i)({prop:a,defaultProp:l??!1,onChange:u,caller:eZ});return(0,y.jsx)(eS,{scope:t,triggerId:(0,p.B)(),triggerRef:s,contentId:(0,p.B)(),open:f,onOpenChange:C,onOpenToggle:n.useCallback(()=>C(e=>!e),[C]),modal:c,children:(0,y.jsx)(B,{...d,open:f,onOpenChange:C,dir:o,modal:c,children:r})})};eN.displayName=eZ;var eI="DropdownMenuTrigger",eO=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,i=eE(eI,r),c=e_(r);return(0,y.jsx)(F,{asChild:!0,...c,children:(0,y.jsx)(u.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(t,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eO.displayName=eI;var eT=e=>{let{__scopeDropdownMenu:t,...r}=e,n=e_(t);return(0,y.jsx)(Y,{...n,...r})};eT.displayName="DropdownMenuPortal";var eA="DropdownMenuContent",eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eE(eA,r),i=e_(r),u=n.useRef(!1);return(0,y.jsx)(z,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||l.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eA;var eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(et,{...o,...n,ref:t})});eF.displayName="DropdownMenuGroup";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(er,{...o,...n,ref:t})});eG.displayName="DropdownMenuLabel";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(ea,{...o,...n,ref:t})});eK.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(ei,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(es,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(eC,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(ev,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(em,{...o,...n,ref:t})});eU.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(ew,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(ey,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=e_(r);return(0,y.jsx)(eV,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eY=eN,eq=eO,eX=eT,eW=eB,ez=eF,eJ=eG,eQ=eK,e$=eU},24089:(e,t,r)=>{r.d(t,{sOK:()=>o});var n=r(98913);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M458.4 64.3C400.6 15.7 311.3 23 256 79.3 200.7 23 111.4 15.6 53.6 64.3-21.6 127.6-10.6 230.8 43 285.5l175.4 178.7c10 10.2 23.4 15.9 37.6 15.9 14.3 0 27.6-5.6 37.6-15.8L469 285.6c53.5-54.7 64.7-157.9-10.6-221.3zm-23.6 187.5L259.4 430.5c-2.4 2.4-4.4 2.4-6.8 0L77.2 251.8c-36.5-37.2-43.9-107.6 7.3-150.7 38.9-32.7 98.9-27.8 136.5 10.5l35 35.7 35-35.7c37.8-38.5 97.8-43.2 136.5-10.6 51.1 43.1 43.5 113.9 7.3 150.8z"},child:[]}]})(e)}},39544:(e,t,r)=>{r.d(t,{A:()=>C});var n=r(41025),o=r(18542),a=r.t(o,2),l=r(54918),i=a["use".trim()],u=r(88320),c=r(93497),d=r(85407),s=r(25323),f=r(49799);function C(e){let{Link:t,config:r,getPathname:a,...C}=function(e,t){var r,a,l;let f={...r=t||{},localePrefix:"object"==typeof(l=r.localePrefix)?l:{mode:l||"always"},localeCookie:!!((a=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof a&&a},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},C=f.pathnames,p=(0,o.forwardRef)(function({href:t,locale:r,...n},o){let a,l;"object"==typeof t?(a=t.pathname,l=t.params):a=t;let d=(0,u._x)(t),p=e(),g=(0,u.yL)(p)?i(p):p,v=d?h({locale:r||g,href:null==C?a:{pathname:a,params:l},forcePrefix:null!=r||void 0}):a;return(0,s.jsx)(c.default,{ref:o,href:"object"==typeof t?{...t,pathname:v}:v,locale:r,localeCookie:f.localeCookie,...n})});function h(e){let t,{forcePrefix:r,href:n,locale:o}=e;return null==C?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,d.Zn)(n.query))):t=n:t=(0,d.FP)({locale:o,...(0,d.TK)(n),pathnames:f.pathnames}),(0,d.x3)(t,o,f,r)}function g(e){return function(t,...r){return e(h(t),...r)}}return{config:f,Link:p,redirect:g(n.redirect),permanentRedirect:g(n.permanentRedirect),getPathname:h}}(l.Ym,e);return{...C,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),r=(0,l.Ym)();return(0,o.useMemo)(()=>{if(!t)return t;let n=t,o=(0,u.XP)(r,e.localePrefix);if((0,u.wO)(o,t))n=(0,u.MY)(t,o);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,u.bL)(r);(0,u.wO)(e,t)&&(n=(0,u.MY)(t,e))}return n},[e.localePrefix,r,t])}(r),t=(0,l.Ym)();return(0,o.useMemo)(()=>e&&r.pathnames?(0,d.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,l.Ym)(),i=(0,n.usePathname)();return(0,o.useMemo)(()=>{function n(e){return function(n,o){let{locale:l,...u}=o||{},c=[a({href:n,locale:l||t})];Object.keys(u).length>0&&c.push(u),(0,f.A)(r.localeCookie,i,t,l),e(...c)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,i,e])},getPathname:a}}},45533:(e,t,r)=>{r.d(t,{AN5:()=>d,Bgv:()=>l,Dcp:()=>m,Lcj:()=>c,LrS:()=>i,QWc:()=>p,R0Y:()=>a,RQr:()=>g,SJ3:()=>o,VNl:()=>M,WN7:()=>x,ase:()=>C,bwM:()=>L,fsL:()=>h,r53:()=>s,sAW:()=>f,tLq:()=>w,uEe:()=>y,xi0:()=>v,y_v:()=>u});var n=r(98913);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M16.0037 9.41421L7.39712 18.0208L5.98291 16.6066L14.5895 8H7.00373V6H18.0037V17H16.0037V9.41421Z"},child:[]}]})(e)}function a(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3ZM20 7.23792L12.0718 14.338L4 7.21594V19H20V7.23792ZM4.51146 5L12.0619 11.662L19.501 5H4.51146Z"},child:[]}]})(e)}function l(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H2L4.92893 19.0711C3.11929 17.2614 2 14.7614 2 12ZM6.82843 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 14.1524 4.85124 16.1649 6.34315 17.6569L7.75736 19.0711L6.82843 20ZM8 13H16C16 15.2091 14.2091 17 12 17C9.79086 17 8 15.2091 8 13Z"},child:[]}]})(e)}function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 2C17.5222 2 22 5.97778 22 10.8889C22 13.9556 19.5111 16.4444 16.4444 16.4444H14.4778C13.5556 16.4444 12.8111 17.1889 12.8111 18.1111C12.8111 18.5333 12.9778 18.9222 13.2333 19.2111C13.5 19.5111 13.6667 19.9 13.6667 20.3333C13.6667 21.2556 12.9 22 12 22C6.47778 22 2 17.5222 2 12C2 6.47778 6.47778 2 12 2ZM10.8111 18.1111C10.8111 16.0843 12.451 14.4444 14.4778 14.4444H16.4444C18.4065 14.4444 20 12.851 20 10.8889C20 7.1392 16.4677 4 12 4C7.58235 4 4 7.58235 4 12C4 16.19 7.2226 19.6285 11.324 19.9718C10.9948 19.4168 10.8111 18.7761 10.8111 18.1111ZM7.5 12C6.67157 12 6 11.3284 6 10.5C6 9.67157 6.67157 9 7.5 9C8.32843 9 9 9.67157 9 10.5C9 11.3284 8.32843 12 7.5 12ZM16.5 12C15.6716 12 15 11.3284 15 10.5C15 9.67157 15.6716 9 16.5 9C17.3284 9 18 9.67157 18 10.5C18 11.3284 17.3284 12 16.5 12ZM12 9C11.1716 9 10.5 8.32843 10.5 7.5C10.5 6.67157 11.1716 6 12 6C12.8284 6 13.5 6.67157 13.5 7.5C13.5 8.32843 12.8284 9 12 9Z"},child:[]}]})(e)}function u(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M6 18H18V6H6V18ZM14 20H10V22H8V20H5C4.44772 20 4 19.5523 4 19V16H2V14H4V10H2V8H4V5C4 4.44772 4.44772 4 5 4H8V2H10V4H14V2H16V4H19C19.5523 4 20 4.44772 20 5V8H22V10H20V14H22V16H20V19C20 19.5523 19.5523 20 19 20H16V22H14V20ZM8 8H16V16H8V8Z"},child:[]}]})(e)}function c(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0049 22.0027C6.48204 22.0027 2.00488 17.5256 2.00488 12.0027C2.00488 6.4799 6.48204 2.00275 12.0049 2.00275C17.5277 2.00275 22.0049 6.4799 22.0049 12.0027C22.0049 17.5256 17.5277 22.0027 12.0049 22.0027ZM12.0049 20.0027C16.4232 20.0027 20.0049 16.421 20.0049 12.0027C20.0049 7.58447 16.4232 4.00275 12.0049 4.00275C7.5866 4.00275 4.00488 7.58447 4.00488 12.0027C4.00488 16.421 7.5866 20.0027 12.0049 20.0027ZM7.00488 13.0027H16.0049V15.0027H12.0049V18.0027L7.00488 13.0027ZM12.0049 9.00275V6.00275L17.0049 11.0027H8.00488V9.00275H12.0049Z"},child:[]}]})(e)}function d(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0049 22.0027C6.48204 22.0027 2.00488 17.5256 2.00488 12.0027C2.00488 6.4799 6.48204 2.00275 12.0049 2.00275C17.5277 2.00275 22.0049 6.4799 22.0049 12.0027C22.0049 17.5256 17.5277 22.0027 12.0049 22.0027ZM12.0049 20.0027C16.4232 20.0027 20.0049 16.421 20.0049 12.0027C20.0049 7.58447 16.4232 4.00275 12.0049 4.00275C7.5866 4.00275 4.00488 7.58447 4.00488 12.0027C4.00488 16.421 7.5866 20.0027 12.0049 20.0027ZM8.50488 14.0027H14.0049C14.281 14.0027 14.5049 13.7789 14.5049 13.5027C14.5049 13.2266 14.281 13.0027 14.0049 13.0027H10.0049C8.62417 13.0027 7.50488 11.8835 7.50488 10.5027C7.50488 9.12203 8.62417 8.00275 10.0049 8.00275H11.0049V6.00275H13.0049V8.00275H15.5049V10.0027H10.0049C9.72874 10.0027 9.50488 10.2266 9.50488 10.5027C9.50488 10.7789 9.72874 11.0027 10.0049 11.0027H14.0049C15.3856 11.0027 16.5049 12.122 16.5049 13.5027C16.5049 14.8835 15.3856 16.0027 14.0049 16.0027H13.0049V18.0027H11.0049V16.0027H8.50488V14.0027Z"},child:[]}]})(e)}function s(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M19.3034 5.33716C17.9344 4.71103 16.4805 4.2547 14.9629 4C14.7719 4.32899 14.5596 4.77471 14.411 5.12492C12.7969 4.89144 11.1944 4.89144 9.60255 5.12492C9.45397 4.77471 9.2311 4.32899 9.05068 4C7.52251 4.2547 6.06861 4.71103 4.70915 5.33716C1.96053 9.39111 1.21766 13.3495 1.5891 17.2549C3.41443 18.5815 5.17612 19.388 6.90701 19.9187C7.33151 19.3456 7.71356 18.73 8.04255 18.0827C7.41641 17.8492 6.82211 17.5627 6.24904 17.2231C6.39762 17.117 6.5462 17.0003 6.68416 16.8835C10.1438 18.4648 13.8911 18.4648 17.3082 16.8835C17.4568 17.0003 17.5948 17.117 17.7434 17.2231C17.1703 17.5627 16.576 17.8492 15.9499 18.0827C16.2789 18.73 16.6609 19.3456 17.0854 19.9187C18.8152 19.388 20.5875 18.5815 22.4033 17.2549C22.8596 12.7341 21.6806 8.80747 19.3034 5.33716ZM8.5201 14.8459C7.48007 14.8459 6.63107 13.9014 6.63107 12.7447C6.63107 11.5879 7.45884 10.6434 8.5201 10.6434C9.57071 10.6434 10.4303 11.5879 10.4091 12.7447C10.4091 13.9014 9.57071 14.8459 8.5201 14.8459ZM15.4936 14.8459C14.4535 14.8459 13.6034 13.9014 13.6034 12.7447C13.6034 11.5879 14.4323 10.6434 15.4936 10.6434C16.5442 10.6434 17.4038 11.5879 17.3825 12.7447C17.3825 13.9014 16.5548 14.8459 15.4936 14.8459Z"},child:[]}]})(e)}function f(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.001 2C6.47598 2 2.00098 6.475 2.00098 12C2.00098 16.425 4.86348 20.1625 8.83848 21.4875C9.33848 21.575 9.52598 21.275 9.52598 21.0125C9.52598 20.775 9.51348 19.9875 9.51348 19.15C7.00098 19.6125 6.35098 18.5375 6.15098 17.975C6.03848 17.6875 5.55098 16.8 5.12598 16.5625C4.77598 16.375 4.27598 15.9125 5.11348 15.9C5.90098 15.8875 6.46348 16.625 6.65098 16.925C7.55098 18.4375 8.98848 18.0125 9.56348 17.75C9.65098 17.1 9.91348 16.6625 10.201 16.4125C7.97598 16.1625 5.65098 15.3 5.65098 11.475C5.65098 10.3875 6.03848 9.4875 6.67598 8.7875C6.57598 8.5375 6.22598 7.5125 6.77598 6.1375C6.77598 6.1375 7.61348 5.875 9.52598 7.1625C10.326 6.9375 11.176 6.825 12.026 6.825C12.876 6.825 13.726 6.9375 14.526 7.1625C16.4385 5.8625 17.276 6.1375 17.276 6.1375C17.826 7.5125 17.476 8.5375 17.376 8.7875C18.0135 9.4875 18.401 10.375 18.401 11.475C18.401 15.3125 16.0635 16.1625 13.8385 16.4125C14.201 16.725 14.5135 17.325 14.5135 18.2625C14.5135 19.6 14.501 20.675 14.501 21.0125C14.501 21.275 14.6885 21.5875 15.1885 21.4875C19.259 20.1133 21.9999 16.2963 22.001 12C22.001 6.475 17.526 2 12.001 2Z"},child:[]}]})(e)}function C(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M17.6874 3.0625L12.6907 8.77425L8.37045 3.0625H2.11328L9.58961 12.8387L2.50378 20.9375H5.53795L11.0068 14.6886L15.7863 20.9375H21.8885L14.095 10.6342L20.7198 3.0625H17.6874ZM16.6232 19.1225L5.65436 4.78217H7.45745L18.3034 19.1225H16.6232Z"},child:[]}]})(e)}function p(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M4.99958 12.9999C4.99958 7.91198 7.90222 3.5636 11.9996 1.81799C16.0969 3.5636 18.9996 7.91198 18.9996 12.9999C18.9996 13.8229 18.9236 14.6264 18.779 15.4027L20.7194 17.2353C20.8845 17.3913 20.9238 17.6389 20.815 17.8383L18.3196 22.4133C18.1873 22.6557 17.8836 22.7451 17.6412 22.6128C17.5993 22.59 17.5608 22.5612 17.5271 22.5274L15.2925 20.2928C15.1049 20.1053 14.8506 19.9999 14.5854 19.9999H9.41379C9.14857 19.9999 8.89422 20.1053 8.70668 20.2928L6.47209 22.5274C6.27683 22.7227 5.96025 22.7227 5.76498 22.5274C5.73122 22.4937 5.70246 22.4552 5.67959 22.4133L3.18412 17.8383C3.07537 17.6389 3.11464 17.3913 3.27975 17.2353L5.22014 15.4027C5.07551 14.6264 4.99958 13.8229 4.99958 12.9999ZM6.47542 19.6957L7.29247 18.8786C7.85508 18.316 8.61814 17.9999 9.41379 17.9999H14.5854C15.381 17.9999 16.1441 18.316 16.7067 18.8786L17.5237 19.6957L18.5056 17.8955L17.4058 16.8568C16.9117 16.3901 16.6884 15.7045 16.8128 15.0364C16.9366 14.3722 16.9996 13.6911 16.9996 12.9999C16.9996 9.13037 15.0045 5.69965 11.9996 4.04033C8.99462 5.69965 6.99958 9.13037 6.99958 12.9999C6.99958 13.6911 7.06255 14.3722 7.18631 15.0364C7.31078 15.7045 7.08746 16.3901 6.59338 16.8568L5.49353 17.8955L6.47542 19.6957ZM11.9996 12.9999C10.895 12.9999 9.99958 12.1045 9.99958 10.9999C9.99958 9.89537 10.895 8.99994 11.9996 8.99994C13.1041 8.99994 13.9996 9.89537 13.9996 10.9999C13.9996 12.1045 13.1041 12.9999 11.9996 12.9999Z"},child:[]}]})(e)}function h(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M2.9918 21C2.44405 21 2 20.5551 2 20.0066V3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.44495 22 3.9934V20.0066C22 20.5552 21.5447 21 21.0082 21H2.9918ZM20 15V5H4V19L14 9L20 15ZM20 17.8284L14 11.8284L6.82843 19H20V17.8284ZM8 11C6.89543 11 6 10.1046 6 9C6 7.89543 6.89543 7 8 7C9.10457 7 10 7.89543 10 9C10 10.1046 9.10457 11 8 11Z"},child:[]}]})(e)}function g(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M11.9998 3C10.3429 3 8.99976 4.34315 8.99976 6V10C8.99976 11.6569 10.3429 13 11.9998 13C13.6566 13 14.9998 11.6569 14.9998 10V6C14.9998 4.34315 13.6566 3 11.9998 3ZM11.9998 1C14.7612 1 16.9998 3.23858 16.9998 6V10C16.9998 12.7614 14.7612 15 11.9998 15C9.23833 15 6.99976 12.7614 6.99976 10V6C6.99976 3.23858 9.23833 1 11.9998 1ZM3.05469 11H5.07065C5.55588 14.3923 8.47329 17 11.9998 17C15.5262 17 18.4436 14.3923 18.9289 11H20.9448C20.4837 15.1716 17.1714 18.4839 12.9998 18.9451V23H10.9998V18.9451C6.82814 18.4839 3.51584 15.1716 3.05469 11Z"},child:[]}]})(e)}function v(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M3 3.9934C3 3.44476 3.44495 3 3.9934 3H20.0066C20.5552 3 21 3.44495 21 3.9934V20.0066C21 20.5552 20.5551 21 20.0066 21H3.9934C3.44476 21 3 20.5551 3 20.0066V3.9934ZM5 5V19H19V5H5ZM10.6219 8.41459L15.5008 11.6672C15.6846 11.7897 15.7343 12.0381 15.6117 12.2219C15.5824 12.2658 15.5447 12.3035 15.5008 12.3328L10.6219 15.5854C10.4381 15.708 10.1897 15.6583 10.0672 15.4745C10.0234 15.4088 10 15.3316 10 15.2526V8.74741C10 8.52649 10.1791 8.34741 10.4 8.34741C10.479 8.34741 10.5562 8.37078 10.6219 8.41459Z"},child:[]}]})(e)}function m(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M5 7H7V17H5V7ZM1 10H3V14H1V10ZM9 2H11V20H9V2ZM13 4H15V22H13V4ZM17 7H19V17H17V7ZM21 10H23V14H21V10Z"},child:[]}]})(e)}function w(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0003 3C17.3924 3 21.8784 6.87976 22.8189 12C21.8784 17.1202 17.3924 21 12.0003 21C6.60812 21 2.12215 17.1202 1.18164 12C2.12215 6.87976 6.60812 3 12.0003 3ZM12.0003 19C16.2359 19 19.8603 16.052 20.7777 12C19.8603 7.94803 16.2359 5 12.0003 5C7.7646 5 4.14022 7.94803 3.22278 12C4.14022 16.052 7.7646 19 12.0003 19ZM12.0003 16.5C9.51498 16.5 7.50026 14.4853 7.50026 12C7.50026 9.51472 9.51498 7.5 12.0003 7.5C14.4855 7.5 16.5003 9.51472 16.5003 12C16.5003 14.4853 14.4855 16.5 12.0003 16.5ZM12.0003 14.5C13.381 14.5 14.5003 13.3807 14.5003 12C14.5003 10.6193 13.381 9.5 12.0003 9.5C10.6196 9.5 9.50026 10.6193 9.50026 12C9.50026 13.3807 10.6196 14.5 12.0003 14.5Z"},child:[]}]})(e)}function x(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0006 18.26L4.94715 22.2082L6.52248 14.2799L0.587891 8.7918L8.61493 7.84006L12.0006 0.5L15.3862 7.84006L23.4132 8.7918L17.4787 14.2799L19.054 22.2082L12.0006 18.26ZM12.0006 15.968L16.2473 18.3451L15.2988 13.5717L18.8719 10.2674L14.039 9.69434L12.0006 5.27502L9.96214 9.69434L5.12921 10.2674L8.70231 13.5717L7.75383 18.3451L12.0006 15.968Z"},child:[]}]})(e)}function M(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM7 12H9C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12H17C17 14.7614 14.7614 17 12 17C9.23858 17 7 14.7614 7 12Z"},child:[]}]})(e)}function L(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M13 10H20L11 23V14H4L13 1V10Z"},child:[]}]})(e)}function y(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M13 9H21L11 24V15H4L13 0V9ZM11 11V7.22063L7.53238 13H13V17.3944L17.263 11H11Z"},child:[]}]})(e)}},48714:(e,t,r)=>{r.d(t,{H4:()=>H,_V:()=>y,bL:()=>L});var n=r(18542),o=r(20849),a=r(69036),l=r(59232),i=r(34757),u=r(81856);function c(){return()=>{}}var d=r(25323),s="Avatar",[f,C]=(0,o.A)(s),[p,h]=f(s),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,d.jsx)(p,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,d.jsx)(i.sG.span,{...o,ref:t})})});g.displayName=s;var v="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:s=()=>{},...f}=e,C=h(v,r),p=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),a=n.useRef(null),i=o?(a.current||(a.current=new window.Image),a.current):null,[d,s]=n.useState(()=>M(i,e));return(0,l.N)(()=>{s(M(i,e))},[i,e]),(0,l.N)(()=>{let e=e=>()=>{s(e)};if(!i)return;let n=e("loaded"),o=e("error");return i.addEventListener("load",n),i.addEventListener("error",o),t&&(i.referrerPolicy=t),"string"==typeof r&&(i.crossOrigin=r),()=>{i.removeEventListener("load",n),i.removeEventListener("error",o)}},[i,r,t]),d}(o,f),g=(0,a.c)(e=>{s(e),C.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==p&&g(p)},[p,g]),"loaded"===p?(0,d.jsx)(i.sG.img,{...f,ref:t,src:o}):null});m.displayName=v;var w="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=h(w,r),[u,c]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(i.sG.span,{...a,ref:t}):null});function M(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var L=g,y=m,H=x},66333:(e,t,r)=>{var n=r(18542),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,l=n.useEffect,i=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,d=n[1];return i(function(){o.value=r,o.getSnapshot=t,c(o)&&d({inst:o})},[e,r,t]),l(function(){return c(o)&&d({inst:o}),e(function(){c(o)&&d({inst:o})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},81856:(e,t,r)=>{e.exports=r(66333)},82356:(e,t,r)=>{r.d(t,{VZG:()=>a,zny:()=>o});var n=r(98913);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M18.25 15.5a.75.75 0 0 1-.75-.75V7.56L7.28 17.78a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L16.44 6.5H9.25a.75.75 0 0 1 0-1.5h9a.75.75 0 0 1 .75.75v9a.75.75 0 0 1-.75.75Z"},child:[]}]})(e)}function a(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.596 2.043c1.075.076 2.059.281 2.743.956.698.688.92 1.696.92 2.941 0 .432-.057.955-.117 1.438-.026.2-.051.392-.076.572l-.056.429h2.05c.752 0 1.446.108 2.036.404.612.306 1.062.787 1.355 1.431.551 1.214.542 3.008.223 5.394l-.051.39c-.134 1.01-.248 1.872-.396 2.58-.166.795-.394 1.496-.816 2.05-.89 1.168-2.395 1.372-4.583 1.372-2.331 0-4.08-.418-5.544-.824l-.602-.17c-1.023-.29-1.852-.526-2.69-.586A1.75 1.75 0 0 1 5.25 22h-1.5A1.75 1.75 0 0 1 2 20.25V9.75C2 8.784 2.784 8 3.75 8h1.5a1.75 1.75 0 0 1 1.746 1.633 1.85 1.85 0 0 0 .523-.131c.961-.415 2.774-1.534 2.774-4.2V4.249c0-1.22 1.002-2.298 2.303-2.206ZM7 18.918c1.059.064 2.079.355 3.118.652l.568.16c1.406.39 3.006.77 5.142.77 2.277 0 3.004-.274 3.39-.781.216-.283.388-.718.54-1.448.136-.65.242-1.45.379-2.477l.05-.384c.32-2.4.253-3.795-.102-4.575-.16-.352-.375-.568-.66-.711-.305-.153-.74-.245-1.365-.245h-2.37c-.681 0-1.293-.57-1.211-1.328.026-.243.065-.537.105-.834l.07-.527c.06-.482.105-.921.105-1.25 0-1.125-.213-1.617-.473-1.873-.275-.27-.774-.455-1.795-.528-.351-.024-.698.274-.698.71v1.053c0 3.55-2.488 5.063-3.68 5.577-.372.16-.754.232-1.113.26ZM3.75 20.5h1.5a.25.25 0 0 0 .25-.25V9.75a.25.25 0 0 0-.25-.25h-1.5a.25.25 0 0 0-.25.25v10.5c0 .138.112.25.25.25Z"},child:[]}]})(e)}},96633:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return e}}};