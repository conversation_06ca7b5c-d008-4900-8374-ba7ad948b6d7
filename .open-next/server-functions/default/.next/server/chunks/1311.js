exports.id=1311,exports.ids=[1311],exports.modules={16244:(e,t,r)=>{Promise.resolve().then(r.bind(r,23044)),Promise.resolve().then(r.bind(r,26343)),Promise.resolve().then(r.bind(r,24747)),Promise.resolve().then(r.bind(r,21902)),Promise.resolve().then(r.bind(r,79654)),Promise.resolve().then(r.bind(r,76735)),Promise.resolve().then(r.t.bind(r,58044,23))},19511:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(96081),s=r(38071),n=r(47944),a=r(21490),i=r(16710);function l({...e}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.A,{crumb:e.crumb}),(0,o.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,o.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),e.description&&(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.description}),e.tip&&(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.tip.description||e.tip.title}),e.toolbar&&(0,o.jsx)(a.A,{items:e.toolbar.items}),(0,o.jsx)(i.Zp,{className:"overflow-x-auto px-6",children:(0,o.jsx)(n.A,{columns:e.columns??[],data:e.data??[]})})]})]})}},21490:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(96081),s=r(47720),n=r(26343),a=r(29753);function i({items:e}){return(0,o.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?(0,o.jsx)(a.N_,{href:e.url,children:(0,o.jsxs)(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,o.jsx)(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):(0,o.jsxs)(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,o.jsx)(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},23044:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx","default")},26343:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx","default")},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var o=r(96081);r(73636);var s=r(21280),n=r(80436),a=r(73515);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?s.DX:"span";return(0,o.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(i({variant:t}),e),...n})}},47720:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var o=r(96081);r(73636);var s=r(21280),n=r(80436),a=r(73515);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let c=n?s.DX:"button";return(0,o.jsx)(c,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...l})}},47944:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var o=r(96081),s=r(79654);function n({value:e,options:t,className:r}){return(0,o.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var a=r(42711);function i({value:e,options:t,className:r}){return(0,o.jsx)(a.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(64694),c=r(3116);function d({value:e,options:t,className:r}){return(0,o.jsx)("div",{className:r,children:t?.format?(0,l.GP)(new Date(e),t?.format):(0,c.m)(new Date(e),{addSuffix:!0})})}var u=r(23044);function m({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,o.jsxs)(s.Table,{className:"w-full",children:[(0,o.jsx)(s.TableHeader,{className:"",children:(0,o.jsx)(s.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,o.jsx)(s.TableHead,{className:e.className,children:e.title},t))})}),(0,o.jsx)(s.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,o.jsx)(s.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let a=t[e.name],l=e.callback?e.callback(t):a,c=l;return"image"===e.type?c=(0,o.jsx)(n,{value:a,options:e.options,className:e.className}):"time"===e.type?c=(0,o.jsx)(d,{value:a,options:e.options,className:e.className}):"label"===e.type?c=(0,o.jsx)(i,{value:a,options:e.options,className:e.className}):"copy"===e.type&&a&&(c=(0,o.jsx)(u.default,{text:a,children:l})),(0,o.jsx)(s.TableCell,{className:e.className,children:c},r)})},r)):(0,o.jsx)(s.TableRow,{className:"",children:(0,o.jsx)(s.TableCell,{colSpan:e.length,children:(0,o.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,o.jsx)("p",{children:r})})})})})]})}},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>o.T});var o=r(10061)},59656:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>a,TableRow:()=>l});var o=r(25323);r(18542);var s=r(91145);function n({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,o.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function a({className:e,...t}){return(0,o.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,o.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,o.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,o.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,o.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},68980:(e,t,r)=>{Promise.resolve().then(r.bind(r,94658)),Promise.resolve().then(r.bind(r,13872)),Promise.resolve().then(r.bind(r,17121)),Promise.resolve().then(r.bind(r,77679)),Promise.resolve().then(r.bind(r,59656)),Promise.resolve().then(r.bind(r,93497)),Promise.resolve().then(r.t.bind(r,63962,23))},79654:(e,t,r)=>{"use strict";r.d(t,{Table:()=>s,TableBody:()=>a,TableCell:()=>c,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var o=r(20263);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","Table"),n=(0,o.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHeader"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableBody");(0,o.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableFooter");let i=(0,o.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHead"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableRow"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCell");(0,o.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCaption")},94658:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var o=r(25323),s=r(13180),n=r(95234);function a({text:e,children:t}){return(0,o.jsx)(s.CopyToClipboard,{text:e,onCopy:()=>n.oR.success("Copied"),children:(0,o.jsx)("div",{className:"cursor-pointer",children:t})})}}};