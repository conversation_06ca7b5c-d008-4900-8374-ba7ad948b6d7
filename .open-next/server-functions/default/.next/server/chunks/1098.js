"use strict";exports.id=1098,exports.ids=[1098],exports.modules={2286:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(18542);n(25323);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},40484:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(18542),o=n(59232);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},62159:(e,t,n)=>{n.d(t,{N:()=>f});var r=n(18542),o=n(20849),i=n(30061),l=n(48771),a=n(25323);function f(e){let t=e+"CollectionProvider",[n,f]=(0,o.A)(t),[s,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,a.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};c.displayName=t;let d=e+"CollectionSlot",p=(0,l.TL)(d),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(d,n),l=(0,i.s)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:r})});h.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",w=(0,l.TL)(m),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,f=r.useRef(null),s=(0,i.s)(t,f),c=u(m,n);return r.useEffect(()=>(c.itemMap.set(f,{ref:f,...l}),()=>void c.itemMap.delete(f))),(0,a.jsx)(w,{...{[g]:""},ref:s,children:o})});return y.displayName=m,[{Provider:c,Slot:h,ItemSlot:y},function(t){let n=u(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},f]}var s=new WeakMap;function u(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=c(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function c(e){return e!=e||0===e?0:Math.trunc(e)}},72777:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>f});var r=n(18542),o=n(34757),i=n(25323),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var f=a},77842:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e6,Bk:()=>eX});var r=n(18542);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let w=new Set(["top","bottom"]);function y(e){return w.has(p(e))?"y":"x"}function x(e){return e.replace(/start|end/g,e=>c[e])}let v=["left","right"],b=["right","left"],R=["top","bottom"],A=["bottom","top"];function S(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function T(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function L(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=m(y(t)),f=g(a),s=p(t),u="y"===l,c=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,w=o[f]/2-i[f]/2;switch(s){case"top":r={x:c,y:o.y-i.height};break;case"bottom":r={x:c,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=w*(n&&u?-1:1);break;case"end":r[a]+=w*(n&&u?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:c}=C(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:w,data:y,reset:x}=await m({x:u,y:c,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});u=null!=g?g:u,c=null!=w?w:c,p={...p,[i]:{...p[i],...y}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:u,y:c}=C(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:o,middlewareData:p}};async function P(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:f}=e,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=T(h),g=a[p?"floating"===c?"reference":"floating":c],w=L(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),y="floating"===c?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),v=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},b=L(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:f}):y);return{top:(w.top-b.top+m.top)/v.y,bottom:(b.bottom-w.bottom+m.bottom)/v.y,left:(w.left-b.left+m.left)/v.x,right:(b.right-w.right+m.right)/v.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return o.some(t=>e[t]>=0)}let F=new Set(["left","top"]);async function j(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),f="y"===y(n),s=F.has(l)?-1:1,u=i&&f?-1:1,c=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*u,y:m*s}:{x:m*s,y:g*u}}function k(){return"undefined"!=typeof window}function M(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function H(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return!!k()&&(e instanceof Node||e instanceof H(e).Node)}function B(e){return!!k()&&(e instanceof Element||e instanceof H(e).Element)}function W(e){return!!k()&&(e instanceof HTMLElement||e instanceof H(e).HTMLElement)}function z(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof H(e).ShadowRoot)}let $=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!$.has(o)}let V=new Set(["table","td","th"]),K=[":popover-open",":modal"];function _(e){return K.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let X=["transform","translate","scale","rotate","perspective"],Y=["transform","translate","scale","rotate","perspective","filter"],q=["paint","layout","strict","content"];function U(e){let t=Q(),n=B(e)?ee(e):e;return X.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||Y.some(e=>(n.willChange||"").includes(e))||q.some(e=>(n.contain||"").includes(e))}function Q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Z(e){return J.has(M(e))}function ee(e){return H(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===M(e))return e;let t=e.assignedSlot||e.parentNode||z(e)&&e.host||N(e);return z(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:W(n)&&G(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=H(o);if(i){let e=eo(l);return t.concat(l,l.visualViewport||[],G(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=W(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,f=a(n)!==i||a(r)!==l;return f&&(n=i,r=l),{width:n,height:r,$:f}}function el(e){return B(e)?e:e.contextElement}function ea(e){let t=el(e);if(!W(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),l=(i?a(n.width):n.width)/r,f=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let ef=s(0);function es(e){let t=H(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ef}function eu(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=el(e),a=s(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let f=(void 0===(o=n)&&(o=!1),r&&(!o||r===H(l))&&o)?es(l):s(0),u=(i.left+f.x)/a.x,c=(i.top+f.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=H(l),t=r&&B(r)?H(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=ea(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,p*=e.y,u+=i,c+=l,o=eo(n=H(o))}}return L({width:d,height:p,x:u,y:c})}function ec(e,t){let n=et(e).scrollLeft;return t?t.left+n:eu(N(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=H(e),r=N(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,f=0;if(o){i=o.width,l=o.height;let e=Q();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,f=o.offsetTop)}return{width:i,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=et(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),f=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:f}}(N(e));else if(B(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=W(e)?ea(e):s(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=es(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return L(r)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!W(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function ew(e,t){var n;let r=H(e);if(_(e))return r;if(!W(e)){let t=en(e);for(;t&&!Z(t);){if(B(t)&&!em(t))return t;t=en(t)}return r}let o=eg(e,t);for(;o&&(n=o,V.has(M(n)))&&em(o);)o=eg(o,t);return o&&Z(o)&&em(o)&&!U(o)?r:o||function(e){let t=en(e);for(;W(t)&&!Z(t);){if(U(t))return t;if(_(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||ew,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=W(t),o=N(t),i="fixed"===n,l=eu(e,!0,i,t),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!i)if(("body"!==M(t)||G(o))&&(a=et(t)),r){let e=eu(t,!0,i,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else o&&(f.x=ec(o));i&&!r&&o&&(f.x=ec(o));let u=!o||r||i?s(0):ed(o,a);return{x:l.left+a.scrollLeft-f.x-u.x,y:l.top+a.scrollTop-f.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ex={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=N(r),a=!!t&&_(t.floating);if(r===l||a&&i)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0),d=W(r);if((d||!d&&!i)&&(("body"!==M(r)||G(l))&&(f=et(r)),W(r))){let e=eu(r);u=ea(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||i?s(0):ed(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-f.scrollTop*u.y+c.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?_(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==M(e)),o=null,i="fixed"===ee(e).position,l=i?en(e):e;for(;B(l)&&!Z(l);){let t=ee(l),n=U(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||G(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||Z(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],s=a.reduce((e,n)=>{let r=eh(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,f,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ew,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ev(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:f,elements:s,middlewareData:u}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let w=T(p),x={x:n,y:r},v=m(y(o)),b=g(v),R=await f.getDimensions(c),A="y"===v,S=A?"clientHeight":"clientWidth",L=a.reference[b]+a.reference[v]-x[v]-a.floating[b],C=x[v]-a.reference[v],E=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),P=E?E[S]:0;P&&await (null==f.isElement?void 0:f.isElement(E))||(P=s.floating[S]||a.floating[b]);let O=P/2-R[b]/2-1,D=i(w[A?"top":"left"],O),F=i(w[A?"bottom":"right"],O),j=P-R[b]-F,k=P/2-R[b]/2+(L/2-C/2),M=l(D,i(k,j)),H=!u.arrow&&null!=h(o)&&k!==M&&a.reference[b]/2-(k<D?D:F)-R[b]/2<0,N=H?k<D?k-D:k-j:0;return{[v]:x[v]+N,data:{[v]:M,centerOffset:k-M-N,...H&&{alignmentOffset:N}},reset:H}}}),eR=(e,t,n)=>{let r=new Map,o={platform:ex,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})};var eA=n(19123),eS="undefined"!=typeof document?r.useLayoutEffect:function(){};function eT(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eT(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eT(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eL(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eC(e,t){let n=eL(e);return Math.round(t*n)/n}function eE(e){let t=r.useRef(e);return eS(()=>{t.current=e}),t}let eP=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),eO=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,f=await j(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+f.x,y:i+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=d(e,t),c={x:n,y:r},h=await P(t,u),g=y(p(o)),w=m(g),x=c[w],v=c[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,i(v,r))}let b=s.fn({...t,[w]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),eF=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(e,t),u={x:n,y:r},c=y(o),h=m(c),g=u[h],w=u[c],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+v.mainAxis,n=i.reference[h]+i.reference[e]-v.mainAxis;g<t?g=t:g>n&&(g=n)}if(s){var b,R;let e="y"===h?"width":"height",t=F.has(p(o)),n=i.reference[c]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:v.crossAxis),r=i.reference[c]+i.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[c])||0)-(t?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[c]:w}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:w}=t,{mainAxis:T=!0,crossAxis:L=!0,fallbackPlacements:C,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:D=!0,...F}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let j=p(a),k=y(u),M=p(u)===u,H=await (null==c.isRTL?void 0:c.isRTL(w.floating)),N=C||(M||!D?[S(u)]:function(e){let t=S(e);return[x(e),t,x(t)]}(u)),I="none"!==O;!C&&I&&N.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:v;return t?v:b;case"left":case"right":return t?R:A;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(x)))),i}(u,D,O,H));let B=[u,...N],W=await P(t,F),z=[],$=(null==(r=f.flip)?void 0:r.overflows)||[];if(T&&z.push(W[j]),L){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),i=g(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=S(l)),[l,S(l)]}(a,s,H);z.push(W[e[0]],W[e[1]])}if($=[...$,{placement:a,overflows:z}],!z.every(e=>e<=0)){let e=((null==(o=f.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==L||k===y(t)||$.every(e=>e.overflows[0]>0&&y(e.placement)===k)))return{data:{index:e,overflows:$},reset:{placement:t}};let n=null==(i=$.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(E){case"bestFit":{let e=null==(l=$.filter(e=>{if(I){let t=y(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:f,rects:s,platform:u,elements:c}=t,{apply:m=()=>{},...g}=d(e,t),w=await P(t,g),x=p(f),v=h(f),b="y"===y(f),{width:R,height:A}=s.floating;"top"===x||"bottom"===x?(o=x,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=x,o="end"===v?"top":"bottom");let S=A-w.top-w.bottom,T=R-w.left-w.right,L=i(A-w[o],S),C=i(R-w[a],T),E=!t.middlewareData.shift,O=L,D=C;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=S),E&&!v){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?D=R-2*(0!==e||0!==t?e+t:l(w.left,w.right)):O=A-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:D,availableHeight:O});let F=await u.getDimensions(c.floating);return R!==F.width||A!==F.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=O(await P(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:D(e)}}}case"escaped":{let e=O(await P(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:D(e)}}}default:return{}}}}}(e),options:[e,t]}),eH=(e,t)=>({...eP(e),options:[e,t]});var eN=n(34757),eI=n(25323),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eI.jsx)(eN.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eI.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var eW=n(30061),ez=n(20849),e$=n(69036),eG=n(59232),eV=n(40484),eK="Popper",[e_,eX]=(0,ez.A)(eK),[eY,eq]=e_(eK),eU=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eI.jsx)(eY,{scope:t,anchor:o,onAnchorChange:i,children:n})};eU.displayName=eK;var eQ="PopperAnchor",eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eq(eQ,n),a=r.useRef(null),f=(0,eW.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eI.jsx)(eN.sG.div,{...i,ref:f})});eJ.displayName=eQ;var eZ="PopperContent",[e0,e1]=e_(eZ),e2=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:s="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:w="optimized",onPlaced:y,...x}=e,v=eq(eZ,n),[b,R]=r.useState(null),A=(0,eW.s)(t,e=>R(e)),[S,T]=r.useState(null),L=(0,eV.X)(S),C=L?.width??0,E=L?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},O=Array.isArray(p)?p:[p],D=O.length>0,F={padding:P,boundary:O.filter(e8),altBoundary:D},{refs:j,floatingStyles:k,placement:M,isPositioned:H,middlewareData:I}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eT(p,o)||h(o);let[m,g]=r.useState(null),[w,y]=r.useState(null),x=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),v=r.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),b=l||m,R=a||w,A=r.useRef(null),S=r.useRef(null),T=r.useRef(c),L=null!=s,C=eE(s),E=eE(i),P=eE(u),O=r.useCallback(()=>{if(!A.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};E.current&&(e.platform=E.current),eR(A.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};D.current&&!eT(T.current,t)&&(T.current=t,eA.flushSync(()=>{d(t)}))})},[p,t,n,E,P]);eS(()=>{!1===u&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[u]);let D=r.useRef(!1);eS(()=>(D.current=!0,()=>{D.current=!1}),[]),eS(()=>{if(b&&(A.current=b),R&&(S.current=R),b&&R){if(C.current)return C.current(b,R,O);O()}},[b,R,O,C,L]);let F=r.useMemo(()=>({reference:A,floating:S,setReference:x,setFloating:v}),[x,v]),j=r.useMemo(()=>({reference:b,floating:R}),[b,R]),k=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eC(j.floating,c.x),r=eC(j.floating,c.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...eL(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,j.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:O,refs:F,elements:j,floatingStyles:k}),[c,O,F,j,k])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=el(e),h=a||s?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,o=N(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(u||t(),!m||!g)return;let w=f(h),y=f(o.clientWidth-(p+m)),x={rootMargin:-w+"px "+-y+"px "+-f(o.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,i(1,c))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ev(d,e.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(p,n):null,g=-1,w=null;u&&(w=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&w&&(w.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=w)||e.observe(t)})),n()}),p&&!d&&w.observe(p),w.observe(t));let y=d?eu(e):null;return d&&function t(){let r=eu(e);y&&!ev(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=w)||e.disconnect(),w=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:v.anchor},middleware:[eO({mainAxis:a+E,alignmentAxis:u}),d&&eD({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eF():void 0,...F}),d&&ej({...F}),ek({...F,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&eH({element:S,padding:c}),e4({arrowWidth:C,arrowHeight:E}),g&&eM({strategy:"referenceHidden",...F})]}),[B,W]=e9(M),z=(0,e$.c)(y);(0,eG.N)(()=>{H&&z?.()},[H,z]);let $=I.arrow?.x,G=I.arrow?.y,V=I.arrow?.centerOffset!==0,[K,_]=r.useState();return(0,eG.N)(()=>{b&&_(window.getComputedStyle(b).zIndex)},[b]),(0,eI.jsx)("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...k,transform:H?k.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eI.jsx)(e0,{scope:n,placedSide:B,onArrowChange:T,arrowX:$,arrowY:G,shouldHideArrow:V,children:(0,eI.jsx)(eN.sG.div,{"data-side":B,"data-align":W,...x,ref:A,style:{...x.style,animation:H?void 0:"none"}})})})});e2.displayName=eZ;var e5="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e7=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e5,n),i=e3[o.placedSide];return(0,eI.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eI.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e8(e){return null!==e}e7.displayName=e5;var e4=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[f,s]=e9(n),u={start:"0%",center:"50%",end:"100%"}[s],c=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===f?(p=i?u:`${c}px`,h=`${-a}px`):"top"===f?(p=i?u:`${c}px`,h=`${r.floating.height+a}px`):"right"===f?(p=`${-a}px`,h=i?u:`${d}px`):"left"===f&&(p=`${r.floating.width+a}px`,h=i?u:`${d}px`),{data:{x:p,y:h}}}});function e9(e){let[t,n="center"]=e.split("-");return[t,n]}var e6=eU,te=eJ,tt=e2,tn=e7},95783:(e,t,n)=>{n.d(t,{RG:()=>b,bL:()=>O,q7:()=>D});var r=n(18542),o=n(16760),i=n(62159),l=n(30061),a=n(20849),f=n(13788),s=n(34757),u=n(69036),c=n(6772),d=n(2286),p=n(25323),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[w,y,x]=(0,i.N)(g),[v,b]=(0,a.A)(g,[x]),[R,A]=v(g),S=r.forwardRef((e,t)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(T,{...e,ref:t})})}));S.displayName=g;var T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:a=!1,dir:f,currentTabStopId:w,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:v,onEntryFocus:b,preventScrollOnEntryFocus:A=!1,...S}=e,T=r.useRef(null),L=(0,l.s)(t,T),C=(0,d.jH)(f),[E,O]=(0,c.i)({prop:w,defaultProp:x??null,onChange:v,caller:g}),[D,F]=r.useState(!1),j=(0,u.c)(b),k=y(n),M=r.useRef(!1),[H,N]=r.useState(0);return r.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(h,j),()=>e.removeEventListener(h,j)},[j]),(0,p.jsx)(R,{scope:n,orientation:i,dir:C,loop:a,currentTabStopId:E,onItemFocus:r.useCallback(e=>O(e),[O]),onItemShiftTab:r.useCallback(()=>F(!0),[]),onFocusableItemAdd:r.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>N(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:D||0===H?-1:0,"data-orientation":i,...S,ref:L,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),A)}}M.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>F(!1))})})}),L="RovingFocusGroupItem",C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,children:u,...c}=e,d=(0,f.B)(),h=a||d,m=A(L,n),g=m.currentTabStopId===h,x=y(n),{onFocusableItemAdd:v,onFocusableItemRemove:b,currentTabStopId:R}=m;return r.useEffect(()=>{if(i)return v(),()=>b()},[i,v,b]),(0,p.jsx)(w.ItemSlot,{scope:n,id:h,focusable:i,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>P(n))}}),children:"function"==typeof u?u({isCurrentTabStop:g,hasTabStop:null!=R}):u})})});C.displayName=L;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var O=S,D=C}};