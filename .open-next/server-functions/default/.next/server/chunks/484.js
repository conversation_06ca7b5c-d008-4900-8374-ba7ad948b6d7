exports.id=484,exports.ids=[484],exports.modules={2783:(e,t,s)=>{"use strict";s.d(t,{N_:()=>i,a8:()=>o,rd:()=>d});var a=s(33531),n=s(39544);let r=(0,s(96633).A)({locales:a.IB,defaultLocale:a.q,localePrefix:a.b,pathnames:a.u7,localeDetection:a.GB}),{Link:i,redirect:l,usePathname:o,useRouter:d}=(0,n.A)(r)},10926:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>d,yv:()=>c});var a=s(25323);s(18542);var n=s(24504),r=s(20856),i=s(7892),l=s(1053),o=s(91145);function d({...e}){return(0,a.jsx)(n.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(n.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:s="popper",...r}){return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(f,{}),(0,a.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function x({className:e,children:t,...s}){return(0,a.jsxs)(n.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(n.p4,{children:t})]})}function f({className:e,...t}){return(0,a.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(r.A,{className:"size-4"})})}},12667:(e,t,s)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./ai-dashboard/zh.json":[9776,9776],"./landing/en.json":[42881,2881],"./landing/zh.json":[30956,956],"./pricing/en.json":[71718,1718],"./pricing/zh.json":[42743,2743],"./showcase/en.json":[20651,651],"./showcase/zh.json":[33198,3198]};function n(e){if(!s.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],n=t[0];return s.e(t[1]).then(()=>s.t(n,19))}n.keys=()=>Object.keys(a),n.id=12667,e.exports=n},13872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(25323),n=s(18542),r=s(45533),i=s(24089),l=s(82356);let o={RiChatSmile3Line:r.Bgv,RiImageLine:r.fsL,RiVideoLine:r.xi0,RiMicLine:r.RQr,RiMoneyDollarCircleLine:r.AN5,RiArrowRightUpLine:r.SJ3,RiFlashlightFill:r.bwM,RiEyeLine:r.tLq,RiCpuLine:r.y_v,RiUserSmileLine:r.VNl,RiFlashlightLine:r.uEe,RiStarLine:r.WN7,RiPaletteLine:r.LrS,RiRocketLine:r.QWc,RiVoiceprintLine:r.Dcp,RiExchangeLine:r.Lcj,RiTwitterXFill:r.ase,RiGithubFill:r.sAW,RiDiscordFill:r.r53,RiMailLine:r.R0Y,FaRegHeart:i.sOK,GoThumbsup:l.VZG,GoArrowUpRight:l.zny},d=(0,n.memo)(({name:e,className:t,onClick:s,...n})=>{let r=o[e];return r?(0,a.jsx)(r,{className:t,onClick:s,style:{cursor:s?"pointer":"default"},...n}):null})},13950:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(96081),n=s(54596),r=s(75114),i=s(60752);async function l({children:e,params:t}){let{locale:s}=await t,l=await (0,i.JE)(s);return(0,a.jsxs)(a.Fragment,{children:[l.header&&(0,a.jsx)(r.default,{header:l.header}),(0,a.jsx)("main",{className:"overflow-x-hidden",children:e}),l.footer&&(0,a.jsx)(n.A,{footer:l.footer})]})}},17609:(e,t,s)=>{"use strict";s.d(t,{CG:()=>o,Fm:()=>u,Qs:()=>f,cj:()=>l,h:()=>m,qp:()=>x});var a=s(25323);s(18542);var n=s(61445),r=s(67564),i=s(91145);function l({...e}){return(0,a.jsx)(n.bL,{"data-slot":"sheet",...e})}function o({...e}){return(0,a.jsx)(n.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,a.jsx)(n.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,a.jsx)(n.hJ,{"data-slot":"sheet-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,side:s="right",...l}){return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(n.UC,{"data-slot":"sheet-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...l,children:[t,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,i.cn)("flex flex-col gap-1.5 p-4",e),...t})}function x({className:e,...t}){return(0,a.jsx)(n.hE,{"data-slot":"sheet-title",className:(0,i.cn)("text-foreground font-semibold",e),...t})}function f({className:e,...t}){return(0,a.jsx)(n.VY,{"data-slot":"sheet-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},26343:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx","default")},29056:(e,t,s)=>{Promise.resolve().then(s.bind(s,75114)),Promise.resolve().then(s.bind(s,26343))},29110:(e,t,s)=>{"use strict";s.d(t,{$m:()=>c,As:()=>d,nD:()=>o,ub:()=>m});var a=s(25323),n=s(18542),r=s(88471),i=s(20856),l=s(91145);let o=r.bL,d=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.q7,{ref:s,className:(0,l.cn)("border-b",e),...t}));d.displayName="AccordionItem";let c=n.forwardRef(({className:e,children:t,...s},n)=>(0,a.jsx)(r.Y9,{className:"flex",children:(0,a.jsxs)(r.l9,{ref:n,className:(0,l.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...s,children:[t,(0,a.jsx)(i.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=r.l9.displayName;let m=n.forwardRef(({className:e,children:t,...s},n)=>(0,a.jsx)(r.UC,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,a.jsx)("div",{className:(0,l.cn)("pb-4 pt-0",e),children:t})}));m.displayName=r.UC.displayName},42109:(e,t,s)=>{"use strict";s.d(t,{Avatar:()=>i,AvatarImage:()=>l,q:()=>o});var a=s(25323);s(18542);var n=s(48714),r=s(91145);function i({className:e,...t}){return(0,a.jsx)(n.bL,{"data-slot":"avatar",className:(0,r.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function l({className:e,...t}){return(0,a.jsx)(n._V,{"data-slot":"avatar-image",className:(0,r.cn)("aspect-square size-full object-cover",e),...t})}function o({className:e,...t}){return(0,a.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,r.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},54596:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(96081),n=s(26343);function r({footer:e}){return e.disabled?null:(0,a.jsx)("section",{id:e.name,className:"py-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-8",children:(0,a.jsxs)("footer",{children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-between gap-10 text-center lg:flex-row lg:text-left",children:[(0,a.jsxs)("div",{className:"flex w-full max-w-96 shrink flex-col items-center justify-between gap-6 lg:items-start",children:[e.brand&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 lg:justify-start",children:[e.brand.logo&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"h-11"}),e.brand.title&&(0,a.jsx)("p",{className:"text-3xl font-semibold",children:e.brand.title})]}),e.brand.description&&(0,a.jsx)("p",{className:"mt-6 text-md text-muted-foreground",children:e.brand.description})]}),e.social&&(0,a.jsx)("ul",{className:"flex items-center space-x-6 text-muted-foreground",children:e.social.items?.map((e,t)=>(0,a.jsx)("li",{className:"font-medium hover:text-primary",children:(0,a.jsx)("a",{href:e.url,target:e.target,children:e.icon&&(0,a.jsx)(n.default,{name:e.icon,className:"size-4"})})},t))})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-6 lg:gap-20",children:e.nav?.items?.map((e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-6 font-bold",children:e.title}),(0,a.jsx)("ul",{className:"space-y-4 text-sm text-muted-foreground",children:e.children?.map((e,t)=>(0,a.jsx)("li",{className:"font-medium hover:text-primary",children:(0,a.jsx)("a",{href:e.url,target:e.target,children:e.title})},t))})]},t))})]}),(0,a.jsxs)("div",{className:"mt-8 flex flex-col justify-between gap-4 border-t pt-8 text-center text-sm font-medium text-muted-foreground lg:flex-row lg:items-center lg:text-left",children:[e.copyright&&(0,a.jsx)("p",{children:e.copyright}),e.agreement&&(0,a.jsx)("ul",{className:"flex justify-center gap-4 lg:justify-start",children:e.agreement.items?.map((e,t)=>(0,a.jsx)("li",{className:"hover:text-primary",children:(0,a.jsx)("a",{href:e.url,target:e.target,children:e.title})},t))})]})]})})})}},60752:(e,t,s)=>{"use strict";async function a(e){return await i("landing",e)}async function n(e){return await i("pricing",e)}async function r(e){return await i("showcase",e)}async function i(e,t){try{return"zh-CN"===t&&(t="zh"),await s(12667)(`./${e}/${t.toLowerCase()}.json`).then(e=>e.default)}catch(a){return console.warn(`Failed to load ${t}.json, falling back to en.json`),await s(87188)(`./${e}/en.json`).then(e=>e.default)}}s.d(t,{DN:()=>r,JE:()=>a,eI:()=>n})},62230:(e,t,s)=>{"use strict";s.d(t,{default:()=>O});var a=s(25323),n=s(29110),r=s(52510),i=s(18542),l=s(75839),o=s(51238),d=s(20856),c=s(91145);let m=i.forwardRef(({className:e,children:t,...s},n)=>(0,a.jsxs)(l.bL,{ref:n,className:(0,c.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",e),...s,children:[t,(0,a.jsx)(j,{})]}));m.displayName=l.bL.displayName;let u=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.B8,{ref:s,className:(0,c.cn)("group flex flex-1 list-none items-center justify-center space-x-1",e),...t}));u.displayName=l.B8.displayName;let x=l.q7,f=(0,o.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-hidden disabled:pointer-events-none disabled:opacity-50 data-active:bg-accent/50 data-[state=open]:bg-accent/50"),h=i.forwardRef(({className:e,children:t,...s},n)=>(0,a.jsxs)(l.l9,{ref:n,className:(0,c.cn)(f(),"group",e),...s,children:[t," ",(0,a.jsx)(d.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]}));h.displayName=l.l9.displayName;let p=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.UC,{ref:s,className:(0,c.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",e),...t}));p.displayName=l.UC.displayName;let g=l.N_,j=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{className:(0,c.cn)("absolute left-0 top-full flex justify-center"),children:(0,a.jsx)(l.LM,{className:(0,c.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",e),ref:s,...t})}));j.displayName=l.LM.displayName,i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(l.C1,{ref:s,className:(0,c.cn)("top-full z-1 flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",e),...t,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})).displayName=l.C1.displayName;var v=s(17609),b=s(13872),N=s(2783),w=s(10926),y=s(41025),k=s(25161),z=s(33531);function _({isIcon:e=!1}){let t=(0,y.useParams)().locale,s=(0,y.useRouter)(),n=(0,y.usePathname)();return(0,a.jsxs)(w.l6,{value:t,onValueChange:e=>{if(e!==t){let a=n.replace(`/${t}`,`/${e}`);a.startsWith(`/${e}`)||(a=`/${e}${a}`),s.push(a)}},children:[(0,a.jsxs)(w.bq,{className:"flex items-center gap-2 border-none text-muted-foreground hover:text-foreground hover:bg-accent/10 focus:ring-0 focus:ring-offset-0 transition-colors",children:[(0,a.jsx)(k.aVW,{className:"text-xl"}),!e&&(0,a.jsx)("span",{className:"hidden md:block",children:z.L$[t]})]}),(0,a.jsx)(w.gC,{className:"z-50 ",children:Object.keys(z.L$).map(e=>{let t=z.L$[e];return(0,a.jsx)(w.eb,{className:"cursor-pointer px-4",value:e,children:t},e)})})]})}var C=s(24645),L=s(3124),R=s(50533);function A(){let e=(0,R.c3)(),{setShowSignModal:t}=(0,L.U)();return(0,a.jsx)(r.$,{variant:"default",onClick:()=>t(!0),className:"cursor-pointer",children:e("user.sign_in")})}var U=s(42109),$=s(69685),P=s(68560);function F({user:e}){let t=(0,R.c3)(),s=[{title:e.nickname},{title:t("user.user_center"),url:"/my-orders"},{title:t("user.admin_system"),url:"/admin/users"},{title:t("user.sign_out"),onClick:()=>(0,P.CI)()}];return(0,a.jsxs)($.rI,{children:[(0,a.jsx)($.ty,{asChild:!0,children:(0,a.jsxs)(U.Avatar,{className:"cursor-pointer",children:[(0,a.jsx)(U.AvatarImage,{src:e.avatar_url,alt:e.nickname}),(0,a.jsx)(U.q,{children:e.nickname})]})}),(0,a.jsx)($.SQ,{className:"mx-4 bg-background",children:s.map((e,t)=>(0,a.jsxs)(i.Fragment,{children:[(0,a.jsx)($._2,{className:"flex justify-center cursor-pointer",children:e.url?(0,a.jsx)(N.N_,{href:e.url,target:e.target,children:e.title}):(0,a.jsx)("button",{onClick:e.onClick,children:e.title})},t),t!==s.length-1&&(0,a.jsx)($.mB,{})]},t))})]})}function I(){(0,R.c3)();let{user:e}=(0,L.U)();return(0,a.jsx)("div",{className:"flex items-center gap-x-2 px-2 cursor-pointer",children:e?(0,a.jsx)(F,{user:e}):(0,a.jsx)(A,{})})}var q=s(59203),M=s(5261),D=s(57566);function E(){let{theme:e,setTheme:t}=(0,L.U)(),s=function(s){s!==e&&((0,D.PW)(M.wD.Theme,s,-1),t(s))};return(0,a.jsx)("div",{className:"flex items-center gap-x-2 px-2",children:"dark"===e?(0,a.jsx)(q.uSI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>s("light"),width:80,height:20}):(0,a.jsx)(q.KsI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>s("dark"),width:80,height:20})})}function O({header:e}){return e.disabled?null:(0,a.jsx)("section",{className:"py-3",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("nav",{className:"hidden justify-between lg:flex",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)(N.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl text-primary font-bold",children:e.brand?.title||""})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(m,{children:(0,a.jsx)(u,{children:e.nav?.items?.map((e,t)=>e.children&&e.children.length>0?(0,a.jsxs)(x,{className:"text-muted-foreground",children:[(0,a.jsxs)(h,{children:[e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0 mr-2"}),(0,a.jsx)("span",{children:e.title})]}),(0,a.jsx)(p,{children:(0,a.jsx)("ul",{className:"w-80 p-3",children:(0,a.jsx)(g,{children:e.children.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsxs)(N.N_,{className:(0,c.cn)("flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-5 shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-semibold",children:e.title}),(0,a.jsx)("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]})},t))})})})]},t):(0,a.jsx)(x,{children:(0,a.jsxs)(N.N_,{className:(0,c.cn)("text-muted-foreground",f,(0,r.r)({variant:"ghost"})),href:e.url,target:e.target,children:[e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0 mr-0"}),e.title]})},t))})})})]}),(0,a.jsxs)("div",{className:"shrink-0 flex gap-2 items-center",children:[e.show_locale&&(0,a.jsx)(_,{}),e.show_theme&&(0,a.jsx)(E,{}),e.buttons?.map((e,t)=>(0,a.jsx)(r.$,{variant:e.variant,children:(0,a.jsxs)(N.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1 cursor-pointer",children:[e.title,e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),e.show_sign&&(0,a.jsx)(I,{})]})]}),(0,a.jsx)("div",{className:"block lg:hidden",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl font-bold",children:e.brand?.title||""})]}),(0,a.jsxs)(v.cj,{children:[(0,a.jsx)(v.CG,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"default",size:"icon",children:(0,a.jsx)(C.A,{className:"size-4"})})}),(0,a.jsxs)(v.h,{className:"overflow-y-auto",children:[(0,a.jsx)(v.Fm,{children:(0,a.jsx)(v.qp,{children:(0,a.jsxs)(N.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl font-bold",children:e.brand?.title||""})]})})}),(0,a.jsx)("div",{className:"mb-8 mt-8 flex flex-col gap-4",children:(0,a.jsx)(n.nD,{type:"single",collapsible:!0,className:"w-full",children:e.nav?.items?.map((e,t)=>e.children&&e.children.length>0?(0,a.jsxs)(n.As,{value:e.title||"",className:"border-b-0",children:[(0,a.jsx)(n.$m,{className:"mb-4 py-0 font-semibold hover:no-underline text-left",children:e.title}),(0,a.jsx)(n.ub,{className:"mt-2",children:e.children.map((e,t)=>(0,a.jsxs)(N.N_,{className:(0,c.cn)("flex select-none gap-4 rounded-md p-3 leading-none outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-semibold",children:e.title}),(0,a.jsx)("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]},t))})]},t):(0,a.jsxs)(N.N_,{href:e.url,target:e.target,className:"font-semibold my-4 flex items-center gap-2 px-4",children:[e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0"}),e.title]},t))})}),(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"mt-2 flex flex-col gap-3",children:[e.buttons?.map((e,t)=>(0,a.jsx)(r.$,{variant:e.variant,children:(0,a.jsxs)(N.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1",children:[e.title,e.icon&&(0,a.jsx)(b.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),e.show_sign&&(0,a.jsx)(I,{})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center gap-2",children:[e.show_locale&&(0,a.jsx)(_,{}),(0,a.jsx)("div",{className:"flex-1"}),e.show_theme&&(0,a.jsx)(E,{})]})]})]})]})]})})]})})}},69685:(e,t,s)=>{"use strict";s.d(t,{I:()=>d,SQ:()=>o,_2:()=>c,lp:()=>m,mB:()=>u,rI:()=>i,ty:()=>l});var a=s(25323);s(18542);var n=s(7738),r=s(91145);function i({...e}){return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...e})}function l({...e}){return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...e})}function o({className:e,sideOffset:t=4,...s}){return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function d({...e}){return(0,a.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...e})}function c({className:e,inset:t,variant:s="default",...i}){return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,r.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function m({className:e,inset:t,...s}){return(0,a.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,r.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...s})}function u({className:e,...t}){return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,r.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},75114:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/header/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/header/index.tsx","default")},87188:(e,t,s)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./landing/en.json":[42881,2881],"./pricing/en.json":[71718,1718],"./showcase/en.json":[20651,651]};function n(e){if(!s.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],n=t[0];return s.e(t[1]).then(()=>s.t(n,19))}n.keys=()=>Object.keys(a),n.id=87188,e.exports=n},92608:(e,t,s)=>{Promise.resolve().then(s.bind(s,62230)),Promise.resolve().then(s.bind(s,13872))}};