"use strict";exports.id=3644,exports.ids=[3644],exports.modules={164:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(25323);s(18542);var l=s(91145);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},31645:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(96081),l=s(47720),i=s(26343),a=s(58044),n=s.n(a);function d({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsx)("div",{className:"px-8",children:(0,r.jsx)("div",{className:'flex items-center justify-center rounded-2xl  bg-[url("/imgs/masks/circle.svg")] bg-cover bg-center px-8 py-12 text-center md:p-16',children:(0,r.jsxs)("div",{className:"mx-auto max-w-(--breakpoint-md)",children:[(0,r.jsx)("h2",{className:"mb-4 text-balance text-3xl font-semibold md:text-5xl",children:e.title}),(0,r.jsx)("p",{className:"text-muted-foreground md:text-lg",children:e.description}),e.buttons&&(0,r.jsx)("div",{className:"mt-8 flex flex-col justify-center gap-4 sm:flex-row",children:e.buttons.map((e,t)=>(0,r.jsx)(l.$,{variant:e.variant||"default",children:(0,r.jsxs)(n(),{href:e.url||"",target:e.target,className:"flex items-center justify-center gap-1",children:[e.title,e.icon&&(0,r.jsx)(i.default,{name:e.icon,className:"size-6"})]})},t))})]})})})})}},46893:(e,t,s)=>{s.d(t,{default:()=>j});var r=s(25323),l=s(7892),i=s(48973),a=s(18542),n=s(22935),d=s(81974),o=s(91145);let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.bL,{className:(0,o.cn)("grid gap-2",e),...t,ref:s}));c.displayName=n.bL.displayName;let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.q7,{ref:s,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));m.displayName=n.q7.displayName;var u=s(26709),x=s(52510),f=s(13872),p=s(99110),h=s(90225),b=s(95234),g=s(3124);function j({pricing:e}){if(e.disabled)return null;let{user:t,setShowSignModal:s}=(0,g.U)(),[n,d]=(0,a.useState)(e.groups?.[0]?.name||"yearly"),[o,j]=(0,a.useState)(!1),[N,v]=(0,a.useState)(null),y=async e=>{try{if(!t)return void s(!0);let r={product_id:e.product_id,product_name:e.product_name,credits:e.credits,interval:e.interval,amount:e.amount,currency:e.currency,valid_months:e.valid_months};j(!0),v(e.product_id);let l=await fetch("/api/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(401===l.status){j(!1),v(null),s(!0);return}let{code:i,message:a,data:n}=await l.json();if(0!==i)return void b.oR.error(a);let{public_key:d,session_id:o}=n,c=await (0,h.c)(d);if(!c)return void b.oR.error("checkout failed");let m=await c.redirectToCheckout({sessionId:o});m.error&&b.oR.error(m.error.message)}catch(e){console.log("checkout failed: ",e),b.oR.error("checkout failed")}finally{j(!1),v(null)}};return(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,r.jsx)("h2",{className:"mb-4 text-4xl font-semibold lg:text-5xl",children:e.title}),(0,r.jsx)("p",{className:"text-muted-foreground lg:text-lg",children:e.description})]}),(0,r.jsxs)("div",{className:"w-full flex flex-col items-center gap-2",children:[e.groups&&e.groups.length>0&&(0,r.jsx)("div",{className:"flex h-12 mb-12 items-center rounded-md bg-muted p-1 text-lg",children:(0,r.jsx)(c,{value:n,className:`h-full grid-cols-${e.groups.length}`,onValueChange:e=>{d(e)},children:e.groups.map((e,t)=>(0,r.jsxs)("div",{className:'h-full rounded-md transition-all border-2 border-transparent has-[button[data-state="checked"]]:border-primary has-[button[data-state="checked"]]:bg-transparent',children:[(0,r.jsx)(m,{value:e.name||"",id:e.name,className:"peer sr-only"}),(0,r.jsxs)(p.J,{htmlFor:e.name,className:"flex h-full cursor-pointer items-center justify-center px-7 font-semibold text-muted-foreground peer-data-[state=checked]:text-primary",children:[e.title,e.label&&(0,r.jsx)(u.E,{variant:"outline",className:"border-primary bg-primary px-1.5 ml-1 text-primary-foreground",children:e.label})]})]},t))})}),(0,r.jsx)("div",{className:`w-full mt-0 grid gap-6 md:grid-cols-${e.items?.filter(e=>!e.group||e.group===n)?.length}`,children:e.items?.map((e,t)=>e.group&&e.group!==n?null:(0,r.jsx)("div",{className:`rounded-lg p-6 ${e.is_featured?"border-primary border-2 bg-card text-card-foreground":"border-muted border"}`,children:(0,r.jsxs)("div",{className:"flex h-full flex-col justify-between gap-5",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[e.title&&(0,r.jsx)("h3",{className:"text-xl font-semibold",children:e.title}),(0,r.jsx)("div",{className:"flex-1"}),e.label&&(0,r.jsx)(u.E,{variant:"outline",className:"border-primary bg-primary px-1.5 text-primary-foreground",children:e.label})]}),(0,r.jsxs)("div",{className:"flex items-end gap-2 mb-4",children:[e.original_price&&(0,r.jsx)("span",{className:"text-xl text-muted-foreground font-semibold line-through",children:e.original_price}),e.price&&(0,r.jsx)("span",{className:"text-5xl font-semibold",children:e.price}),e.unit&&(0,r.jsx)("span",{className:"block font-semibold",dangerouslySetInnerHTML:{__html:e.unit}})]}),e.description&&(0,r.jsx)("p",{className:"text-muted-foreground",children:e.description}),e.features_title&&(0,r.jsx)("p",{className:"mb-3 mt-6 font-semibold",children:e.features_title}),e.features&&(0,r.jsx)("ul",{className:"flex flex-col gap-3",children:e.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex gap-2",children:[(0,r.jsx)(l.A,{className:"mt-1 size-4 shrink-0"}),e]},`feature-${t}`))})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[e.button&&(0,r.jsxs)(x.$,{className:"w-full flex items-center justify-center gap-2 font-semibold",disabled:o,onClick:()=>{o||y(e)},children:[(!o||o&&N!==e.product_id)&&(0,r.jsx)("p",{children:e.button.title}),o&&N===e.product_id&&(0,r.jsx)("p",{children:e.button.title}),o&&N===e.product_id&&(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),e.button.icon&&(0,r.jsx)(f.default,{name:e.button.icon,className:"size-4"})]}),e.tip&&(0,r.jsx)("p",{className:"text-muted-foreground text-sm mt-2",children:e.tip})]})]})},t))})]})]})})}},51201:(e,t,s)=>{s.d(t,{default:()=>r});let r=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/pricing/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/pricing/index.tsx","default")},76999:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(96081),l=s(42711);function i({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:"text-center",children:[e.label&&(0,r.jsx)(l.E,{className:"text-xs font-medium",children:e.label}),(0,r.jsx)("h2",{className:"mt-4 text-4xl font-semibold",children:e.title}),(0,r.jsx)("p",{className:"mt-6 font-medium text-muted-foreground",children:e.description})]}),(0,r.jsx)("div",{className:"mx-auto mt-14 grid gap-8 md:grid-cols-2 md:gap-12",children:e.items?.map((e,t)=>(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("span",{className:"flex size-6 shrink-0 items-center justify-center rounded-sm border border-primary font-mono text-xs text-primary",children:t+1}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-2 flex items-center justify-between",children:(0,r.jsx)("h3",{className:"font-semibold",children:e.title})}),(0,r.jsx)("p",{className:"text-md text-muted-foreground",children:e.description})]})]},t))})]})})}}};