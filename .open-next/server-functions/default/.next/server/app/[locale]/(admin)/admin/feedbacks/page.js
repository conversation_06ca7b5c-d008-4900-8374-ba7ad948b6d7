"use strict";(()=>{var e={};e.id=6747,e.ids=[6747],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},9129:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var a=t(94331),n=t(78740),s=t(87638),i=t.n(s),o=t(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["feedbacks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68780)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/feedbacks/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/feedbacks/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/feedbacks/page",pathname:"/[locale]/admin/feedbacks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68780:(e,r,t)=>{t.r(r),t.d(r,{default:()=>o});var a=t(96081),n=t(19511),s=t(69354),i=t(64694);async function o(){let e=await (0,s.NW)(1,50),r=[{title:"User",name:"user",callback:e=>e.user&&e.user.avatar_url?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("img",{src:e.user?.avatar_url||"",className:"w-8 h-8 rounded-full"}),(0,a.jsx)("span",{children:e.user?.nickname})]}):"-"},{name:"content",title:"Content",callback:e=>e.content},{name:"rating",title:"Rating",callback:e=>e.rating},{name:"created_at",title:"Created At",callback:e=>(0,i.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")},{name:"actions",title:"Actions",callback:e=>(0,a.jsx)("a",{href:`/admin/users?user_uuid=${e.user_uuid}`,target:"_blank",children:"View user"})}];return(0,a.jsx)(n.A,{title:"Feedbacks",columns:r,data:e})}},69354:(e,r,t)=>{t.d(r,{$0:()=>s,NW:()=>i,lU:()=>o});var a=t(7279),n=t(33102);async function s(e){let r=(0,a.A)(),{data:t,error:n}=await r.from("feedbacks").insert(e);if(n)throw n;return t}async function i(e=1,r=50){e<1&&(e=1),r<=0&&(r=50);let t=(e-1)*r,s=(0,a.A)(),{data:o,error:l}=await s.from("feedbacks").select("*").order("created_at",{ascending:!1}).range(t,t+r-1);if(l||!o||0===o.length)return[];let d=Array.from(new Set(o.map(e=>e.user_uuid))),u=await (0,n.QZ)(d);return o.map(e=>{let r=u.find(r=>r.uuid===e.user_uuid);return{...e,user:r}})}async function o(){let e=(0,a.A)(),{data:r,error:t}=await e.from("feedbacks").select("count",{count:"exact"});if(!t)return r[0].count}},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4622,4552,5327,6854,7125,2742,1311],()=>t(9129));module.exports=a})();