(()=>{var e={};e.id=3196,e.ids=[3196],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19511:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(96081),o=r(38071),n=r(47944),a=r(21490),i=r(16710);function l({...e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{crumb:e.crumb}),(0,s.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.description}),e.tip&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.tip.description||e.tip.title}),e.toolbar&&(0,s.jsx)(a.A,{items:e.toolbar.items}),(0,s.jsx)(i.Zp,{className:"overflow-x-auto px-6",children:(0,s.jsx)(n.A,{columns:e.columns??[],data:e.data??[]})})]})]})}},21490:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(96081),o=r(47720),n=r(26343),a=r(29753);function i({items:e}){return(0,s.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?(0,s.jsx)(a.N_,{href:e.url,children:(0,s.jsxs)(o.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,s.jsx)(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):(0,s.jsxs)(o.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,s.jsx)(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},23044:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx","default")},26343:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/icon/index.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34874:(e,t,r)=>{Promise.resolve().then(r.bind(r,23044)),Promise.resolve().then(r.bind(r,38292)),Promise.resolve().then(r.bind(r,26343)),Promise.resolve().then(r.bind(r,24747)),Promise.resolve().then(r.bind(r,21902)),Promise.resolve().then(r.bind(r,79654)),Promise.resolve().then(r.bind(r,76735)),Promise.resolve().then(r.t.bind(r,58044,23))},38292:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/dropdown.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/dropdown.tsx","default")},41167:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>n,aV:()=>i,gg:()=>a,gx:()=>u,j7:()=>d,nz:()=>o,zX:()=>c});var s=r(7279),o=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function n(e){let t=(0,s.A)(),{data:r,error:o}=await t.from("posts").insert(e);if(o)throw o;return r}async function a(e,t){let r=(0,s.A)(),{data:o,error:n}=await r.from("posts").update(t).eq("uuid",e);if(n)throw n;return o}async function i(e){let t=(0,s.A)(),{data:r,error:o}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!o)return r}async function l(e,t){let r=(0,s.A)(),{data:o,error:n}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!n)return o}async function c(e=1,t=50){let r=(0,s.A)(),{data:o,error:n}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return n?[]:o}async function d(e,t=1,r=50){let o=(0,s.A)(),{data:n,error:a}=await o.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return a?[]:n}async function u(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},41475:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(94331),o=r(78740),n=r(87638),a=r.n(n),i=r(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56543)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/posts/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/posts/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/posts/page",pathname:"/[locale]/admin/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(96081);r(73636);var o=r(21280),n=r(80436),a=r(73515);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?o.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(i({variant:t}),e),...n})}},47720:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(96081);r(73636);var o=r(21280),n=r(80436),a=r(73515);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let c=n?o.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...l})}},47944:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(96081),o=r(79654);function n({value:e,options:t,className:r}){return(0,s.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var a=r(42711);function i({value:e,options:t,className:r}){return(0,s.jsx)(a.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(64694),c=r(3116);function d({value:e,options:t,className:r}){return(0,s.jsx)("div",{className:r,children:t?.format?(0,l.GP)(new Date(e),t?.format):(0,c.m)(new Date(e),{addSuffix:!0})})}var u=r(23044);function p({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,s.jsxs)(o.Table,{className:"w-full",children:[(0,s.jsx)(o.TableHeader,{className:"",children:(0,s.jsx)(o.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,s.jsx)(o.TableHead,{className:e.className,children:e.title},t))})}),(0,s.jsx)(o.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,s.jsx)(o.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let a=t[e.name],l=e.callback?e.callback(t):a,c=l;return"image"===e.type?c=(0,s.jsx)(n,{value:a,options:e.options,className:e.className}):"time"===e.type?c=(0,s.jsx)(d,{value:a,options:e.options,className:e.className}):"label"===e.type?c=(0,s.jsx)(i,{value:a,options:e.options,className:e.className}):"copy"===e.type&&a&&(c=(0,s.jsx)(u.default,{text:a,children:l})),(0,s.jsx)(o.TableCell,{className:e.className,children:c},r)})},r)):(0,s.jsx)(o.TableRow,{className:"",children:(0,s.jsx)(o.TableCell,{colSpan:e.length,children:(0,s.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,s.jsx)("p",{children:r})})})})})]})}},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(96081),o=r(38292),n=r(19511),a=r(41167),i=r(64694);async function l(){let e=await (0,a.zX)();return(0,s.jsx)(n.A,{title:"Posts",toolbar:{items:[{title:"Add Post",icon:"RiAddLine",url:"/admin/posts/add"}]},columns:[{name:"title",title:"Title"},{name:"description",title:"Description"},{name:"slug",title:"Slug"},{name:"locale",title:"Locale"},{name:"status",title:"Status"},{name:"created_at",title:"Created At",callback:e=>(0,i.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")},{callback:e=>{let t=[{title:"Edit",icon:"RiEditLine",url:`/admin/posts/${e.uuid}/edit`},{title:"View",icon:"RiEyeLine",url:`/${e.locale}/posts/${e.slug}`,target:"_blank"}];return(0,s.jsx)(o.default,{items:t})}}],data:e,empty_message:"No posts found"})}},57975:e=>{"use strict";e.exports=require("node:util")},59656:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>a,TableRow:()=>l});var s=r(25323);r(18542);var o=r(91145);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})})}function a({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75042:(e,t,r)=>{Promise.resolve().then(r.bind(r,94658)),Promise.resolve().then(r.bind(r,90626)),Promise.resolve().then(r.bind(r,13872)),Promise.resolve().then(r.bind(r,17121)),Promise.resolve().then(r.bind(r,77679)),Promise.resolve().then(r.bind(r,59656)),Promise.resolve().then(r.bind(r,93497)),Promise.resolve().then(r.t.bind(r,63962,23))},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79654:(e,t,r)=>{"use strict";r.d(t,{Table:()=>o,TableBody:()=>a,TableCell:()=>c,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var s=r(20263);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHeader"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableFooter");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHead"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableRow"),c=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCaption")},81630:e=>{"use strict";e.exports=require("http")},90626:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(25323),o=r(69685),n=r(52510),a=r(13872),i=r(63962),l=r.n(i);let c=(0,r(78108).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);function d({items:e}){return(0,s.jsxs)(o.rI,{children:[(0,s.jsx)(o.ty,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(c,{}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsx)(o.SQ,{align:"end",className:"w-[160px]",children:e.map(e=>(0,s.jsx)(o._2,{children:(0,s.jsxs)(l(),{href:e.url||"",target:e.target||"_self",className:"flex items-center gap-2",children:[e.icon&&(0,s.jsx)(a.default,{name:e.icon,className:"w-4 h-4"}),e.title]})},e.title))})]})}},91645:e=>{"use strict";e.exports=require("net")},94658:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(25323),o=r(13180),n=r(95234);function a({text:e,children:t}){return(0,s.jsx)(o.CopyToClipboard,{text:e,onCopy:()=>n.oR.success("Copied"),children:(0,s.jsx)("div",{className:"cursor-pointer",children:t})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4622,4552,5327,6854,7125,2742],()=>r(41475));module.exports=s})();