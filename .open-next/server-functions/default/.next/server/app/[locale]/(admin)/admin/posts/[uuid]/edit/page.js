(()=>{var e={};e.id=2273,e.ids=[2273],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10926:(e,t,r)=>{"use strict";r.d(t,{bq:()=>c,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>u});var s=r(25323);r(18542);var a=r(24504),i=r(20856),n=r(7892),o=r(1053),l=r(91145);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function u({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function c({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...i}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(f,{})]})})}function m({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}function f({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},11032:(e,t,r)=>{Promise.resolve().then(r.bind(r,41267)),Promise.resolve().then(r.bind(r,24747)),Promise.resolve().then(r.bind(r,21902)),Promise.resolve().then(r.t.bind(r,58044,23))},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20039:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(96081),a=r(41267),i=r(38071),n=r(16710);function o({...e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{crumb:e.crumb}),(0,s.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),(0,s.jsx)(n.Zp,{className:"overflow-x-auto px-6",children:(0,s.jsx)(a.default,{fields:e.fields,data:e.data,passby:e.passby,submit:e.submit,loading:e.loading})})]})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41167:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>i,aV:()=>o,gg:()=>n,gx:()=>c,j7:()=>u,nz:()=>a,zX:()=>d});var s=r(7279),a=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function i(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").insert(e);if(a)throw a;return r}async function n(e,t){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").update(t).eq("uuid",e);if(i)throw i;return a}async function o(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!a)return r}async function l(e,t){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!i)return a}async function d(e=1,t=50){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return i?[]:a}async function u(e,t=1,r=50){let a=(0,s.A)(),{data:i,error:n}=await a.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return n?[]:i}async function c(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},53749:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(94331),a=r(78740),i=r(87638),n=r.n(i),o=r(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["posts",{children:["[uuid]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96374)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/posts/[uuid]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/posts/[uuid]/edit/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/posts/[uuid]/edit/page",pathname:"/[locale]/admin/posts/[uuid]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66068:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T,"60954d7e37e63e5c10b396e9d10bab59e3c10d0a81":()=>a.$$RSC_SERVER_ACTION_0});var s=r(10061),a=r(96374)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95352:(e,t,r)=>{Promise.resolve().then(r.bind(r,69741)),Promise.resolve().then(r.bind(r,17121)),Promise.resolve().then(r.bind(r,77679)),Promise.resolve().then(r.t.bind(r,63962,23))},96374:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>c,default:()=>p});var s=r(96081),a=r(16454);r(15358);var i=r(41167),n=r(39353),o=r(72160),l=r(20039),d=r(88661),u=r(53908);let c=async function(e,t){let{user:r,post:s}=t;if(!r||!s||!s.uuid)throw Error("invalid params");let a=e.get("title"),n=e.get("slug"),o=e.get("locale"),l=e.get("status"),u=e.get("description"),c=e.get("cover_url"),p=e.get("author_name"),m=e.get("author_avatar_url"),x=e.get("content");if(!a||!a.trim()||!n||!n.trim()||!o||!o.trim())throw Error("invalid form data");let f=await (0,i.St)(n,o);if(f&&f.uuid!==s.uuid)throw Error("post with same slug already exists");let h={updated_at:(0,d.iq)(),status:l,title:a,slug:n,locale:o,description:u,cover_url:c,author_name:p,author_avatar_url:m,content:x};try{return await (0,i.gg)(s.uuid,h),{status:"success",message:"Post updated",redirect_url:"/admin/posts"}}catch(e){throw Error(e.message)}};async function p({params:e}){let{uuid:t}=await e,r=await (0,u.ug)();if(!r||!r.uuid)return(0,s.jsx)(o.A,{message:"no auth"});let d=await (0,i.aV)(t);if(!d)return(0,s.jsx)(o.A,{message:"post not found"});let p={title:"Edit Post",crumb:{items:[{title:"Posts",url:"/admin/posts"},{title:"Edit Post",is_active:!0}]},fields:[{name:"title",title:"Title",type:"text",placeholder:"Post Title",validation:{required:!0}},{name:"slug",title:"Slug",type:"text",placeholder:"what-is-shipany",validation:{required:!0},tip:"post slug should be unique, visit like: /blog/what-is-shipany"},{name:"locale",title:"Locale",type:"select",options:n.IB.map(e=>({title:n.L$[e],value:e})),value:"en",validation:{required:!0}},{name:"status",title:"Status",type:"select",options:Object.values(i.nz).map(e=>({title:e,value:e})),value:i.nz.Created},{name:"description",title:"Description",type:"textarea",placeholder:"Post Description"},{name:"cover_url",title:"Cover URL",type:"url",placeholder:"Post Cover Image URL"},{name:"author_name",title:"Author Name",type:"text",placeholder:"Author Name"},{name:"author_avatar_url",title:"Author Avatar URL",type:"url",placeholder:"Author Avatar Image URL"},{name:"content",title:"Content",type:"editor",placeholder:"Post Content"}],data:d,passby:{user:r,post:d},submit:{button:{title:"Submit"},handler:(0,a.A)(c,"60954d7e37e63e5c10b396e9d10bab59e3c10d0a81",null)}};return(0,s.jsx)(l.A,{...p})}}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,4622,5327,8395,7231,6854,7125,2742,2767],()=>r(53749));module.exports=s})();