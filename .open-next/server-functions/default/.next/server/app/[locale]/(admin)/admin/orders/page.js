"use strict";(()=>{var e={};e.id=6380,e.ids=[6380],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20783:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var a=t(96081),o=t(19511),n=t(87009),s=t(64694);async function i(){let e=await (0,n.jE)(1,50),r=[{name:"order_no",title:"Order No"},{name:"paid_email",title:"Paid Email"},{name:"product_name",title:"Product Name"},{name:"amount",title:"Amount"},{name:"created_at",title:"Created At",callback:e=>(0,s.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")}];return(0,a.jsx)(o.A,{title:"Paid Orders",columns:r,data:e})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},54649:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>x,tree:()=>l});var a=t(94331),o=t(78740),n=t(87638),s=t.n(n),i=t(51681),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20783)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/orders/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/orders/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/orders/page",pathname:"/[locale]/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4622,4552,5327,6854,7125,2742,1311],()=>t(54649));module.exports=a})();