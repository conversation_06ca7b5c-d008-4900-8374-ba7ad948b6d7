"use strict";(()=>{var e={};e.id=6323,e.ids=[6323],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},35157:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>x,tree:()=>d});var a=t(94331),s=t(78740),n=t(87638),o=t.n(n),i=t(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96404)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/users/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/users/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/users/page",pathname:"/[locale]/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96404:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var a=t(96081),s=t(19511),n=t(33102),o=t(64694);async function i(){let e=await (0,n.lo)(1,50),r=[{name:"uuid",title:"UUID"},{name:"email",title:"Email"},{name:"nickname",title:"Name"},{name:"avatar_url",title:"Avatar",callback:e=>(0,a.jsx)("img",{src:e.avatar_url,className:"w-10 h-10 rounded-full"})},{name:"created_at",title:"Created At",callback:e=>(0,o.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")}];return(0,a.jsx)(s.A,{title:"All Users",columns:r,data:e})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4622,4552,5327,6854,7125,2742,1311],()=>t(35157));module.exports=a})();