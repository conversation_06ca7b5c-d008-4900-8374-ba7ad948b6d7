(()=>{var e={};e.id=3616,e.ids=[3616],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10926:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(25323);r(18542);var a=r(24504),n=r(20856),i=r(7892),o=r(1053),l=r(91145);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...i}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(m,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(x,{})]})})}function f({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function m({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}function x({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21280:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i});var s=r(73636);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var n=r(96081),i=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,l):l),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,o=s.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}("Slot"),o=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35860:(e,t,r)=>{Promise.resolve().then(r.bind(r,51629)),Promise.resolve().then(r.bind(r,24747)),Promise.resolve().then(r.bind(r,21902)),Promise.resolve().then(r.t.bind(r,58044,23))},41167:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>n,aV:()=>o,gg:()=>i,gx:()=>u,j7:()=>c,nz:()=>a,zX:()=>d});var s=r(7279),a=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function n(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").insert(e);if(a)throw a;return r}async function i(e,t){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").update(t).eq("uuid",e);if(n)throw n;return a}async function o(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!a)return r}async function l(e,t){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!n)return a}async function d(e=1,t=50){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return n?[]:a}async function c(e,t=1,r=50){let a=(0,s.A)(),{data:n,error:i}=await a.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return i?[]:n}async function u(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(96081);r(73636);var a=r(21280),n=r(80436),i=r(73515);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},51629:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(20263);(0,s.registerClientReference)(function(){throw Error("Attempted to call description() from the server but description is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/data-charts/index.tsx","description");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/data-charts/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/data-charts/index.tsx","default")},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69354:(e,t,r)=>{"use strict";r.d(t,{$0:()=>n,NW:()=>i,lU:()=>o});var s=r(7279),a=r(33102);async function n(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("feedbacks").insert(e);if(a)throw a;return r}async function i(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,n=(0,s.A)(),{data:o,error:l}=await n.from("feedbacks").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(l||!o||0===o.length)return[];let d=Array.from(new Set(o.map(e=>e.user_uuid))),c=await (0,a.QZ)(d);return o.map(e=>{let t=c.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function o(){let e=(0,s.A)(),{data:t,error:r}=await e.from("feedbacks").select("count",{count:"exact"});if(!r)return t[0].count}},72244:(e,t,r)=>{Promise.resolve().then(r.bind(r,9901)),Promise.resolve().then(r.bind(r,17121)),Promise.resolve().then(r.bind(r,77679)),Promise.resolve().then(r.t.bind(r,63962,23))},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79643:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(94331),a=r(78740),n=r(87638),i=r.n(n),o=r(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92580)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,27659)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(admin)/admin/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/page",pathname:"/[locale]/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},80436:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(64556);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let n=a(t)||a(s);return i[e][n]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92580:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(96081),a=r(42711),n=r(16710);function i({dataCards:e}){return(0,s.jsx)("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4",children:e.map((e,t)=>(0,s.jsxs)(n.Zp,{className:"@container/card",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.BT,{children:e.title}),(0,s.jsx)(n.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:e.value}),(0,s.jsx)(n.X9,{children:e.label&&(0,s.jsx)(a.E,{variant:"outline",children:e.label})})]}),(0,s.jsxs)(n.wL,{className:"flex-col items-start gap-1.5 text-sm",children:[(0,s.jsx)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:e.description}),(0,s.jsx)("div",{className:"text-muted-foreground",children:e.tip})]})]},t))})}var o=r(51629),l=r(38071),d=r(87009),c=r(33102),u=r(69354),p=r(41167);async function f(){let e=await (0,d.Yc)(),t=await (0,c.PD)(),r=await (0,u.lU)(),a=await (0,p.gx)(),n=[{title:"Total Users",label:"",value:(t||0).toString(),description:"Total users registered in the system"},{title:"Paid Orders",label:"",value:(e||0).toString(),description:"User Paid Orders in total"},{title:"System Posts",label:"",value:(a||0).toString(),description:"Posts in total"},{title:"User Feedbacks",label:"",value:(r||0).toString(),description:"Feedbacks in total"}],f=new Date;f.setDate(f.getDate()-90);let m=await (0,d.nz)(f.toISOString(),"paid"),x=await (0,c.in)(f.toISOString()),v=Array.from(new Set([...m?Array.from(m.keys()):[],...x?Array.from(x.keys()):[]])).sort().map(e=>({date:e,users:x?.get(e)||0,orders:m?.get(e)||0}));return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("div",{className:"flex flex-1 flex-col",children:(0,s.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-2",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4 py-4 md:gap-6 md:py-6",children:[(0,s.jsx)(i,{dataCards:n}),(0,s.jsx)("div",{className:"px-4 lg:px-6",children:(0,s.jsx)(o.default,{data:v,fields:[{key:"users",label:"Users",color:"var(--primary)"},{key:"orders",label:"Orders",color:"var(--secondary)"}],title:"Users and Orders Overview",description:"Daily users and orders data",defaultTimeRange:"90d"})})]})})})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,4622,5327,682,6854,7125,2742,9901],()=>r(79643));module.exports=s})();