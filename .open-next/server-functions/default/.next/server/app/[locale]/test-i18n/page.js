(()=>{var e={};e.id=7317,e.ids=[7317],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4830:(e,t,r)=>{Promise.resolve().then(r.bind(r,9814))},9392:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,X9:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>o,wL:()=>u});var s=r(25323);r(18542);var n=r(91145);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},9814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(25323),n=r(18542),a=r(50533),o=r(52510),i=r(9392),l=r(93540);let c=(e,t)=>{let r="http://localhost:3000";return{"@type":"BreadcrumbList","@id":`${e}#breadcrumb`,itemListElement:[{"@type":"ListItem",position:1,name:"首页",item:r||""},...t.map((e,t)=>({"@type":"ListItem",position:t+2,name:e.name,item:`${r}${e.url}`}))]}},d=async e=>{let{title:t,description:r,image:s,url:n,schemaType:a="SoftwareApplication",datePublished:o,dateModified:i,applicationCategory:l="GameApplication",operatingSystem:c,ratingValue:d,ratingCount:u,authorName:p,wordCount:m,readingTime:h,tags:g}=e,x="http://localhost:3000",j="KREA FLUX";x&&j||console.warn("Schema: Missing required environment variables NEXT_PUBLIC_WEB_URL or NEXT_PUBLIC_PROJECT_NAME");let f={"@context":"https://schema.org","@id":n,description:r,image:{"@type":"ImageObject",url:s,contentUrl:s},url:n,author:{"@type":p?"Person":"Organization",name:p||j||"QQ AI",...p?{}:{url:x||"",logo:{"@type":"ImageObject",url:`${x||""}/logo.png`}}},publisher:{"@type":"Organization",name:j||"QQ AI",url:x||"",logo:{"@type":"ImageObject",url:`${x||""}/logo.png`}},mainEntityOfPage:n,...o&&{datePublished:o},...i&&{dateModified:i},...d&&u&&{aggregateRating:{"@type":"AggregateRating",ratingValue:d,ratingCount:u}}};switch(a){case"SoftwareApplication":return{...f,"@type":"SoftwareApplication",name:t,applicationCategory:l,...c&&{operatingSystem:c}};case"Article":return{...f,"@type":"Article",headline:t,...m&&{wordCount:m},...h&&{timeRequired:`PT${h}M`},...g&&g.length>0&&{keywords:g.join(", ")}};case"WebPage":return{...f,"@type":"WebPage",name:t};default:throw Error(`Unsupported schema type: ${a}`)}};async function u(e){try{let t=await d(e),r=c(e.url,e.breadcrumb);return(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify([t,r],null,0)}})}catch(e){return console.error("Schema generation error:",e),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:"{}"}})}}function p(){let e=(0,a.c3)("language_switch"),[t,r]=(0,n.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u,{title:"超级马里奥兄弟",description:"经典的平台跳跃游戏，与马里奥一起冒险拯救公主",image:"https://example.com/mario.jpg",url:"https://example.com/games/super-mario",schemaType:"SoftwareApplication",applicationCategory:"GameApplication",operatingSystem:"Nintendo Switch, PC",ratingValue:4.8,ratingCount:250,breadcrumb:[{name:"游戏",url:"/games"},{name:"平台游戏",url:"/games/platform"}]}),(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"国际化文本测试"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"测试 useTranslations 钩子是否正常工作"})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"翻译文本测试"}),(0,s.jsx)(i.BT,{children:"直接使用 useTranslations 获取的文本"})]}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"title:"})," ",e("title")]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"description (with params):"})," ",e("description",{currentLanguage:"English",suggestedLanguage:"中文"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"switch_button (with params):"})," ",e("switch_button",{suggestedLanguage:"中文"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"cancel_button (with params):"})," ",e("cancel_button",{currentLanguage:"English"})]}),(0,s.jsxs)("div",{className:"mt-4 p-4 bg-muted rounded",children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"原始翻译文本（不带参数）："}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"description:"})," ",e.raw("description")]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"switch_button:"})," ",e.raw("switch_button")]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"cancel_button:"})," ",e.raw("cancel_button")]})]})]})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"弹框组件测试"}),(0,s.jsx)(i.BT,{children:"测试语言切换弹框组件"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)(o.$,{onClick:()=>r(!0),children:"显示语言切换弹框"})})]}),(0,s.jsx)(l.A,{open:t,currentLanguage:"en",suggestedLanguage:"zh",onSwitch:()=>{console.log("用户选择切换语言"),r(!1)},onCancel:()=>{console.log("用户选择保持当前语言"),r(!1)},onClose:()=>r(!1)})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},91234:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-i18n/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-i18n/page.tsx","default")},93206:(e,t,r)=>{Promise.resolve().then(r.bind(r,91234))},96069:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(94331),n=r(78740),a=r(87638),o=r.n(a),i=r(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["test-i18n",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,91234)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-i18n/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-i18n/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/test-i18n/page",pathname:"/[locale]/test-i18n",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,9229,8530,9058,6854],()=>r(96069));module.exports=s})();