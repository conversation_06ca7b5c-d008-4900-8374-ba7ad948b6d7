(()=>{var e={};e.id=7080,e.ids=[7080],e.modules={164:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(25323);t(18542);var s=t(91145);function o({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9392:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>n,Zp:()=>o,aR:()=>i,wL:()=>u});var a=t(25323);t(18542);var s=t(91145);function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...r})}function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...r})}function c({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function u({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68890:(e,r,t)=>{"use strict";t.d(r,{b:()=>n});var a=t(18542),s=t(34757),o=t(25323),i=a.forwardRef((e,r)=>(0,o.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var n=i},71921:(e,r,t)=>{Promise.resolve().then(t.bind(t,84253))},81649:(e,r,t)=>{Promise.resolve().then(t.bind(t,90072))},84253:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(25323),s=t(18542),o=t(52510),i=t(164),n=t(99110),l=t(9392),d=t(8894),c=t(95234);function u(){let[e,r]=(0,s.useState)("a korean woman pale skin, only wearing lace underware, front side camera"),[t,u]=(0,s.useState)(""),[p,m]=(0,s.useState)(!1),[x,f]=(0,s.useState)(null),h=async()=>{if(!e.trim())return void c.oR.error("请输入提示词");m(!0),f(null);try{let r=await fetch("/api/test/replicate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e})}),t=await r.json();0===t.code?(f(t.data),c.oR.success("测试成功！")):c.oR.error(t.msg||"测试失败")}catch(e){console.error("Test failed:",e),c.oR.error("网络错误")}finally{m(!1)}},g=async()=>{if(!e.trim())return void c.oR.error("请输入提示词");if(!t.trim())return void c.oR.error("请输入 API Key");m(!0),f(null);try{let r=await fetch("/api/ai/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({model:"black-forest-labs/flux-krea-dev",type:"image",prompt:e,options:{output_quality:90,output_format:"webp",disable_safety_checker:!0}})}),a=await r.json();0===a.code?(f(a.data),c.oR.success("统一API测试成功！")):c.oR.error(a.msg||"统一API测试失败")}catch(e){console.error("Unified API test failed:",e),c.oR.error("网络错误")}finally{m(!1)}},v=async()=>{if(!e.trim())return void c.oR.error("请输入提示词");m(!0),f(null);try{let r=await fetch("/api/test/replicate-unified",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e,options:{output_quality:90,output_format:"webp"}})}),t=await r.json();0===t.code?(f(t.data),c.oR.success("统一API测试成功（无认证）！")):c.oR.error(t.msg||"统一API测试失败（无认证）")}catch(e){console.error("Unified API test (no auth) failed:",e),c.oR.error("网络错误")}finally{m(!1)}};return(0,a.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Replicate Flux Krea Dev 模型测试"})}),(0,a.jsxs)(l.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"prompt",children:"提示词"}),(0,a.jsx)(i.p,{id:"prompt",value:e,onChange:e=>r(e.target.value),placeholder:"输入图像生成提示词..."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"apiKey",children:"API Key (用于统一API测试)"}),(0,a.jsx)(i.p,{id:"apiKey",type:"password",value:t,onChange:e=>u(e.target.value),placeholder:"输入你的 API Key..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(o.$,{onClick:h,disabled:p,children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试 Replicate Provider"}),(0,a.jsx)(o.$,{onClick:g,disabled:p,variant:"outline",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试统一 AI API"}),(0,a.jsx)(o.$,{onClick:v,disabled:p,variant:"secondary",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):"测试统一 API (无认证)"})]}),x&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"测试结果"})}),(0,a.jsxs)(l.Wu,{children:[(0,a.jsx)("pre",{className:"bg-muted p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(x,null,2)}),(x.response?.urls||x.urls)&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:"生成的图片："}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(x.response?.urls||x.urls||[]).map((e,r)=>(0,a.jsx)("img",{src:e,alt:`Generated image ${r+1}`,className:"w-full h-auto rounded-md border"},r))})]}),x.result?.images&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:"生成的图片："}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:x.result.images.map((e,r)=>(0,a.jsx)("img",{src:e.url,alt:`Generated image ${r+1}`,className:"w-full h-auto rounded-md border"},r))})]})]})]})]})]})})}},90072:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-replicate/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-replicate/page.tsx","default")},92525:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(94331),s=t(78740),o=t(87638),i=t.n(o),n=t(51681),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["test-replicate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90072)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-replicate/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-replicate/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/test-replicate/page",pathname:"/[locale]/test-replicate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99110:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var a=t(25323);t(18542);var s=t(68890),o=t(91145);function i({className:e,...r}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1179,3057,5474,9229,8530,9058,6854],()=>t(92525));module.exports=a})();