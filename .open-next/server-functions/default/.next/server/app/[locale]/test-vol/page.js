(()=>{var e={};e.id=2958,e.ids=[2958],e.modules={164:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(25323);s(18542);var r=s(91145);function n({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7330:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>d,TN:()=>o});var a=s(25323),r=s(18542),n=s(51238),i=s(91145);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:t,...s},r)=>(0,a.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:t}),e),...s}));d.displayName="Alert",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));o.displayName="AlertDescription"},9392:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,X9:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var a=s(25323);s(18542);var r=s(91145);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,r.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10926:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>o,yv:()=>c});var a=s(25323);s(18542);var r=s(24504),n=s(20856),i=s(7892),l=s(1053),d=s(91145);function o({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},19087:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(78108).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26709:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(25323);s(18542);var r=s(48771),n=s(51238),i=s(91145);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...n}){let d=s?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),e),...n})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31266:(e,t,s)=>{Promise.resolve().then(s.bind(s,50360))},33873:e=>{"use strict";e.exports=require("path")},36405:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(94331),r=s(78740),n=s(87638),i=s.n(n),l=s(51681),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["[locale]",{children:["test-vol",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56813)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-vol/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-vol/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/test-vol/page",pathname:"/[locale]/test-vol",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},37399:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-vol/volcengine-test-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/test-vol/volcengine-test-page.tsx","default")},38846:(e,t,s)=>{"use strict";s.d(t,{B8:()=>T,Tabs:()=>j,TabsList:()=>y,TabsTrigger:()=>w,UC:()=>P,bL:()=>A,l9:()=>F});var a=s(18542),r=s(16760),n=s(20849),i=s(95783),l=s(78869),d=s(34757),o=s(2286),c=s(6772),u=s(13788),x=s(25323),m="Tabs",[h,p]=(0,n.A)(m,[i.RG]),v=(0,i.RG)(),[g,f]=h(m),j=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:r,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:h="automatic",...p}=e,v=(0,o.jH)(l),[f,j]=(0,c.i)({prop:a,onChange:r,defaultProp:n??"",caller:m});return(0,x.jsx)(g,{scope:s,baseId:(0,u.B)(),value:f,onValueChange:j,orientation:i,dir:v,activationMode:h,children:(0,x.jsx)(d.sG.div,{dir:v,"data-orientation":i,...p,ref:t})})});j.displayName=m;var b="TabsList",y=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...r}=e,n=f(b,s),l=v(s);return(0,x.jsx)(i.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:a,children:(0,x.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:t})})});y.displayName=b;var N="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:n=!1,...l}=e,o=f(N,s),c=v(s),u=C(o.baseId,a),m=S(o.baseId,a),h=a===o.value;return(0,x.jsx)(i.q7,{asChild:!0,...c,focusable:!n,active:h,children:(0,x.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||n||!e||o.onValueChange(a)})})})});w.displayName=N;var _="TabsContent",k=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:n,children:i,...o}=e,c=f(_,s),u=C(c.baseId,r),m=S(c.baseId,r),h=r===c.value,p=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(l.C,{present:n||h,children:({present:s})=>(0,x.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&i})})});function C(e,t){return`${e}-trigger-${t}`}function S(e,t){return`${e}-content-${t}`}k.displayName=_;var A=j,T=y,F=w,P=k},50360:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var a=s(25323),r=s(18542),n=s(9392),i=s(52510),l=s(164),d=s(99110),o=s(91624),c=s(10926),u=s(94670),x=s(7330),m=s(26709),h=s(78108);let p=(0,h.A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),v=(0,h.A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);var g=s(19087),f=s(8894);let j=(0,h.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);function b(){let[e,t]=(0,r.useState)("你好，这是火山引擎语音合成测试。"),[s,h]=(0,r.useState)("BV700_streaming"),[b,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[_,k]=(0,r.useState)("这是一段较长的文本，用于测试火山引擎的异步语音合成功能。异步合成适合处理长文本内容，可以生成高质量的音频文件。"),[C,S]=(0,r.useState)("BV701_streaming"),[A,T]=(0,r.useState)(!1),[F,P]=(0,r.useState)(null),[B,R]=(0,r.useState)(""),[V,z]=(0,r.useState)(!1),[q,D]=(0,r.useState)(""),[M,E]=(0,r.useState)("fast"),[$,L]=(0,r.useState)("zh"),[U,I]=(0,r.useState)(!1),[J,Z]=(0,r.useState)(null),[G,O]=(0,r.useState)(""),[X,W]=(0,r.useState)(!1),[K,H]=(0,r.useState)(""),[Q,Y]=(0,r.useState)(null),[ee,et]=(0,r.useState)(""),[es,ea]=(0,r.useState)("1"),[er,en]=(0,r.useState)(!1),[ei,el]=(0,r.useState)(null),[ed,eo]=(0,r.useState)(""),ec=async()=>{if(!e.trim())return void eo("请输入要合成的文本");y(!0),eo("");try{let t=await fetch("/api/volcengine/tts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,voice_type:s,options:{encoding:"mp3",rate:24e3}})}),a=await t.json();0===a.code?w(a.data):eo(a.message||"语音合成失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{y(!1)}},eu=async()=>{if(!_.trim())return void eo("请输入要合成的文本");T(!0),eo("");try{let e=await fetch("/api/volcengine/tts-async/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:_,voice_type:C,options:{format:"mp3",sample_rate:24e3}})}),t=await e.json();0===t.code?(R(t.data.task_id),P({...t.data,status:"submitted"}),em(t.data.task_id)):eo(t.message||"异步任务提交失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{T(!1)}},ex=async()=>{if(!B)return void eo("请先提交异步任务");T(!0),eo("");try{let e=await fetch(`/api/volcengine/tts-async/query?task_id=${B}`),t=await e.json();0===t.code?P(t.data):eo(t.message||"查询失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{T(!1)}},em=async e=>{z(!0);let t=0,s=async()=>{if(t>=60){z(!1),eo("任务超时，请手动查询结果");return}try{let a=await fetch(`/api/volcengine/tts-async/query?task_id=${e}`),r=await a.json();if(0===r.code&&(P(r.data),"success"===r.data.status||"failed"===r.data.status))return void z(!1);t++,setTimeout(s,5e3)}catch(e){console.error("轮询错误:",e),t++,setTimeout(s,5e3)}};s()},eh=async()=>{if(!q.trim())return void eo("请输入音频文件URL");I(!0),eo("");try{let e=await fetch("/api/volcengine/asr/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audio_url:q,model_type:M,language:$})}),t=await e.json();0===t.code?(O(t.data.task_id),Z({...t.data,status:"submitted"}),ev(t.data.task_id,M)):eo(t.message||"ASR任务提交失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{I(!1)}},ep=async()=>{if(!G)return void eo("请先提交ASR任务");I(!0),eo("");try{let e=await fetch(`/api/volcengine/asr/query?task_id=${G}&model_type=${M}`),t=await e.json();0===t.code?Z(t.data):eo(t.message||"查询失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{I(!1)}},ev=async(e,t)=>{W(!0);let s=0,a=async()=>{if(s>=120){W(!1),eo("任务超时，请手动查询结果");return}try{let r=await fetch(`/api/volcengine/asr/query?task_id=${e}&model_type=${t}`),n=await r.json();if(0===n.code&&(Z(n.data),"success"===n.data.status||"failed"===n.data.status))return void W(!1);s++,setTimeout(a,5e3)}catch(e){console.error("轮询错误:",e),s++,setTimeout(a,5e3)}};a()},eg=async()=>{if(!K.trim()||!Q)return void eo("请输入Speaker ID并选择音频文件");en(!0),eo("");try{let e=new FormData;e.append("audio",Q),e.append("speaker_id",K),e.append("reference_text",ee),e.append("language","0"),e.append("model_type",es);let t=await fetch("/api/volcengine/voice-clone/upload",{method:"POST",body:e}),s=await t.json();0===s.code?el(s.data):eo(s.message||"声音复刻上传失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{en(!1)}},ef=async()=>{if(!K.trim())return void eo("请输入Speaker ID");en(!0),eo("");try{let e=await fetch(`/api/volcengine/voice-clone/status?speaker_id=${K}`),t=await e.json();0===t.code?el(t.data):eo(t.message||"状态查询失败")}catch(e){eo("请求失败: "+(e instanceof Error?e.message:"未知错误"))}finally{en(!1)}},ej=(e,t="mp3")=>{try{let s=new Blob([Uint8Array.from(atob(e),e=>e.charCodeAt(0))],{type:`audio/${t}`}),a=URL.createObjectURL(s);new Audio(a).play()}catch(e){eo("音频播放失败")}};return(0,a.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"火山引擎语音服务测试"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"测试火山引擎豆包语音服务的各项功能"})]}),ed&&(0,a.jsx)(x.Fc,{className:"mb-6 border-red-200 bg-red-50",children:(0,a.jsx)(x.TN,{className:"text-red-800",children:ed})}),(0,a.jsxs)(u.tU,{defaultValue:"tts",className:"space-y-6",children:[(0,a.jsxs)(u.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(u.Xi,{value:"tts",className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"w-4 h-4"}),"短文本合成"]}),(0,a.jsxs)(u.Xi,{value:"async-tts",className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"w-4 h-4"}),"长文本合成"]}),(0,a.jsxs)(u.Xi,{value:"asr",className:"flex items-center gap-2",children:[(0,a.jsx)(v,{className:"w-4 h-4"}),"语音识别"]}),(0,a.jsxs)(u.Xi,{value:"voice-clone",className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),"声音复刻"]})]}),(0,a.jsx)(u.av,{value:"tts",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"短文本语音合成"}),(0,a.jsx)(n.BT,{children:"实时语音合成，适合短文本（最大1024字节）"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"tts-text",children:"合成文本"}),(0,a.jsx)(o.T,{id:"tts-text",value:e,onChange:e=>t(e.target.value),placeholder:"输入要合成的文本...",className:"mt-1",maxLength:1024}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[e.length,"/1024 字符"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"tts-voice",children:"音色选择"}),(0,a.jsxs)(c.l6,{value:s,onValueChange:h,children:[(0,a.jsx)(c.bq,{className:"mt-1",children:(0,a.jsx)(c.yv,{})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"BV700_streaming",children:"灿灿 (支持情感)"}),(0,a.jsx)(c.eb,{value:"BV701_streaming",children:"擎苍 (有声阅读)"}),(0,a.jsx)(c.eb,{value:"BV001_streaming",children:"通用女声"}),(0,a.jsx)(c.eb,{value:"BV002_streaming",children:"通用男声"}),(0,a.jsx)(c.eb,{value:"BV421_streaming",children:"天才少女 (多语种)"}),(0,a.jsx)(c.eb,{value:"BV503_streaming",children:"Ariana (英语)"})]})]})]}),(0,a.jsx)(i.$,{onClick:ec,disabled:b||!e.trim(),className:"w-full",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"合成中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{className:"w-4 h-4 mr-2"}),"开始合成"]})}),N&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"合成成功"}),(0,a.jsxs)(m.E,{variant:"secondary",children:["时长: ",N.duration,"ms"]})]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 mb-3",children:["请求ID: ",N.reqid]}),(0,a.jsxs)(i.$,{onClick:()=>ej(N.audio_data,N.format),size:"sm",variant:"outline",children:[(0,a.jsx)(j,{className:"w-4 h-4 mr-2"}),"播放音频"]})]})]})]})}),(0,a.jsx)(u.av,{value:"async-tts",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"长文本异步合成"}),(0,a.jsx)(n.BT,{children:"异步处理长文本（最大10万字符），支持回调和轮询"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"async-tts-text",children:"合成文本"}),(0,a.jsx)(o.T,{id:"async-tts-text",value:_,onChange:e=>k(e.target.value),placeholder:"输入要合成的长文本...",className:"mt-1 min-h-[120px]",maxLength:1e5}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[_.length,"/100,000 字符"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"async-tts-voice",children:"音色选择"}),(0,a.jsxs)(c.l6,{value:C,onValueChange:S,children:[(0,a.jsx)(c.bq,{className:"mt-1",children:(0,a.jsx)(c.yv,{})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"BV701_streaming",children:"擎苍 (推荐长文本)"}),(0,a.jsx)(c.eb,{value:"BV700_streaming",children:"灿灿"}),(0,a.jsx)(c.eb,{value:"BV001_streaming",children:"通用女声"}),(0,a.jsx)(c.eb,{value:"BV002_streaming",children:"通用男声"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:eu,disabled:A||V||!_.trim(),className:"flex-1",children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"提交中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"提交任务"]})}),(0,a.jsx)(i.$,{onClick:ex,disabled:A||V||!B,variant:"outline",className:"flex-1",children:V?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"轮询中..."]}):"手动查询"})]}),B&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["任务ID: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:B})]}),V&&(0,a.jsx)(m.E,{variant:"secondary",className:"animate-pulse",children:"自动轮询中..."})]})}),F&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"任务状态"}),(0,a.jsx)(m.E,{variant:"success"===F.status?"default":"secondary",children:F.status})]}),F.audio_url&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["音频文件: ",(0,a.jsx)("a",{href:F.audio_url,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"下载链接"})]}),F.subtitle_url&&(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["字幕文件: ",(0,a.jsx)("a",{href:F.subtitle_url,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"下载链接"})]}),(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["时长: ",F.duration,"ms | 文件大小: ",F.file_size," bytes"]})]})]})]})]})}),(0,a.jsx)(u.av,{value:"asr",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"语音识别"}),(0,a.jsx)(n.BT,{children:"支持多种语言和方言的语音转文字服务"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-url",children:"音频文件URL"}),(0,a.jsx)(l.p,{id:"asr-url",value:q,onChange:e=>D(e.target.value),placeholder:"输入公网可访问的音频文件URL...",className:"mt-1"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-model",children:"识别模型"}),(0,a.jsxs)(c.l6,{value:M,onValueChange:E,children:[(0,a.jsx)(c.bq,{className:"mt-1",children:(0,a.jsx)(c.yv,{})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"bigmodel",children:"大模型识别 (高精度)"}),(0,a.jsx)(c.eb,{value:"standard",children:"标准版识别"}),(0,a.jsx)(c.eb,{value:"fast",children:"极速版识别"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"asr-language",children:"语言"}),(0,a.jsxs)(c.l6,{value:$,onValueChange:L,children:[(0,a.jsx)(c.bq,{className:"mt-1",children:(0,a.jsx)(c.yv,{})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"zh",children:"中文普通话"}),(0,a.jsx)(c.eb,{value:"yue",children:"粤语"}),(0,a.jsx)(c.eb,{value:"en",children:"英语"}),(0,a.jsx)(c.eb,{value:"ja",children:"日语"}),(0,a.jsx)(c.eb,{value:"ko",children:"韩语"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:eh,disabled:U||X||!q.trim(),className:"flex-1",children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"提交中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v,{className:"w-4 h-4 mr-2"}),"提交识别"]})}),(0,a.jsx)(i.$,{onClick:ep,disabled:U||X||!G,variant:"outline",className:"flex-1",children:X?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"轮询中..."]}):"手动查询"})]}),G&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["任务ID: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:G})]}),X&&(0,a.jsx)(m.E,{variant:"secondary",className:"animate-pulse",children:"自动轮询中..."})]})}),J&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"识别结果"}),(0,a.jsx)(m.E,{variant:"success"===J.status?"default":"secondary",children:J.status})]}),J.result&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-green-800 mb-1",children:"完整文本:"}),(0,a.jsx)("p",{className:"text-green-700 bg-white p-3 rounded border",children:J.result.text})]}),J.result.utterances&&J.result.utterances.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-green-800 mb-2",children:"分段结果:"}),(0,a.jsx)("div",{className:"space-y-2",children:J.result.utterances.map((e,t)=>(0,a.jsx)("div",{className:"bg-white p-2 rounded border text-sm",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,a.jsx)("span",{className:"text-green-700",children:e.text}),(0,a.jsxs)("span",{className:"text-green-600 text-xs",children:[e.start_time,"s - ",e.end_time,"s"]})]})},t))})]})]})]})]})]})}),(0,a.jsx)(u.av,{value:"voice-clone",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"声音复刻"}),(0,a.jsx)(n.BT,{children:"上传音频文件训练个性化音色"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-speaker-id",children:"Speaker ID"}),(0,a.jsx)(l.p,{id:"clone-speaker-id",value:K,onChange:e=>H(e.target.value),placeholder:"输入唯一的音色标识符...",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-file",children:"音频文件"}),(0,a.jsx)(l.p,{id:"clone-file",type:"file",accept:".wav,.mp3,.ogg,.m4a,.aac,.pcm",onChange:e=>Y(e.target.files?.[0]||null),className:"mt-1"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"支持格式: wav, mp3, ogg, m4a, aac, pcm (最大10MB)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-text",children:"参考文本 (可选)"}),(0,a.jsx)(o.T,{id:"clone-text",value:ee,onChange:e=>et(e.target.value),placeholder:"输入音频对应的文本内容，用于提高复刻质量...",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"clone-model-type",children:"复刻版本"}),(0,a.jsxs)(c.l6,{value:es,onValueChange:ea,children:[(0,a.jsx)(c.bq,{className:"mt-1",children:(0,a.jsx)(c.yv,{})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"0",children:"复刻1.0 (基础版)"}),(0,a.jsx)(c.eb,{value:"1",children:"复刻2.0 (增强版)"}),(0,a.jsx)(c.eb,{value:"2",children:"DiT标准版 (音色复刻)"}),(0,a.jsx)(c.eb,{value:"3",children:"DiT还原版 (音色+风格)"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:eg,disabled:er||!K.trim()||!Q,className:"flex-1",children:er?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"上传中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"上传训练"]})}),(0,a.jsx)(i.$,{onClick:ef,disabled:er||!K.trim(),variant:"outline",className:"flex-1",children:"查询状态"})]}),ei&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800",children:"复刻状态"}),(0,a.jsx)(m.E,{variant:ei.ready_for_use?"default":"secondary",children:ei.status})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-green-700",children:[(0,a.jsxs)("p",{children:["Speaker ID: ",ei.speaker_id]}),ei.version&&(0,a.jsxs)("p",{children:["版本: ",ei.version]}),ei.create_time&&(0,a.jsxs)("p",{children:["创建时间: ",new Date(ei.create_time).toLocaleString()]}),ei.demo_audio&&(0,a.jsxs)("p",{children:["试听音频: ",(0,a.jsx)("a",{href:ei.demo_audio,target:"_blank",rel:"noopener noreferrer",className:"underline",children:"播放"})]}),ei.ready_for_use&&(0,a.jsx)("p",{className:"text-green-800 font-medium",children:"✅ 可用于语音合成"})]})]})]})]})})]})]})}},56813:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>n});var a=s(96081),r=s(37399);let n={title:"火山引擎语音服务测试",description:"测试火山引擎豆包语音服务的各项功能"};function i(){return(0,a.jsx)(r.default,{})}},62810:(e,t,s)=>{Promise.resolve().then(s.bind(s,37399))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68890:(e,t,s)=>{"use strict";s.d(t,{b:()=>l});var a=s(18542),r=s(34757),n=s(25323),i=a.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},91624:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(25323),r=s(18542),n=s(91145);let i=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));i.displayName="Textarea"},94670:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>o,j7:()=>l,tU:()=>i});var a=s(25323);s(18542);var r=s(38846),n=s(91145);function i({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",e),...t})}function l({className:e,...t}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted/50 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-xl p-1 backdrop-blur-sm border border-border/30",e),...t})}function d({className:e,...t}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-md focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-9 flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-background/50 data-[state=active]:scale-[1.02] [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function o({className:e,...t}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",e),...t})}},99110:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(25323);s(18542);var r=s(68890),n=s(91145);function i({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1179,3057,5474,9229,8530,9058,1098,4626,6854],()=>s(36405));module.exports=a})();