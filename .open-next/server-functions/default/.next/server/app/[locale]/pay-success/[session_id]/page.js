"use strict";(()=>{var e={};e.id=599,e.ids=[599],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},45048:(e,r,s)=>{s.r(r),s.d(r,{default:()=>n});var t=s(69595),o=s(24608),i=s(17984);async function n({params:e}){try{let{session_id:r}=await e,s=new t.A(process.env.STRIPE_PRIVATE_KEY||""),i=await s.checkout.sessions.retrieve(r);await (0,o.J)(i)}catch(e){(0,i.redirect)("http://localhost:3000/#pricing")}(0,i.redirect)("http://localhost:3000/my-orders")}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99967:(e,r,s)=>{s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>l});var t=s(94331),o=s(78740),i=s(87638),n=s.n(i),a=s(51681),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);s.d(r,p);let l={children:["",{children:["[locale]",{children:["pay-success",{children:["[session_id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,45048)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/pay-success/[session_id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/pay-success/[session_id]/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},c=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/pay-success/[session_id]/page",pathname:"/[locale]/pay-success/[session_id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[1179,3057,5474,4209,9229,8530,9058,9595,6854,4305],()=>s(99967));module.exports=t})();