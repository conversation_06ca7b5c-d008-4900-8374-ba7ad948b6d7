(()=>{var e={};e.id=3951,e.ids=[3951],e.modules={164:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(25323);r(18542);var a=r(91145);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4850:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(73636),a=r(47830),n=r(13266),i=(0,s.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:s,namespace:a,onError:i=n.g,...o}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...o,onError:i,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),o=(0,s.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),i(await (0,a.A)(r),t)})},9392:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,X9:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var s=r(25323);r(18542);var a=r(91145);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12808:(e,t,r)=>{Promise.resolve().then(r.bind(r,94658)),Promise.resolve().then(r.bind(r,97455)),Promise.resolve().then(r.bind(r,59656)),Promise.resolve().then(r.bind(r,93497)),Promise.resolve().then(r.t.bind(r,63962,23))},14743:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(73636),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),i=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var s,a,n;s=e,a=t,n=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:n,title:l}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,i),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&s.createElement("title",null,l),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(a)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21956:(e,t,r)=>{Promise.resolve().then(r.bind(r,51419)),Promise.resolve().then(r.bind(r,93497))},23044:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx","default")},25960:(e,t,r)=>{Promise.resolve().then(r.bind(r,23044)),Promise.resolve().then(r.bind(r,67611)),Promise.resolve().then(r.bind(r,79654)),Promise.resolve().then(r.bind(r,76735)),Promise.resolve().then(r.t.bind(r,58044,23))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35482:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,Ex:()=>i,N$:()=>l,Rt:()=>n});var s=r(7279),a=r(33102);async function n(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("affiliates").insert({user_uuid:e.user_uuid,invited_by:e.invited_by,created_at:e.created_at,status:e.status,paid_order_no:e.paid_order_no,paid_amount:e.paid_amount,reward_percent:e.reward_percent,reward_amount:e.reward_amount});if(a)throw a;return r}async function i(e,t=1,r=50){let n=(0,s.A)(),{data:o,error:l}=await n.from("affiliates").select("*").eq("invited_by",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r);if(l)return console.error("Error fetching user invites:",l),[];if(!o||0===o.length)return;let c=Array.from(new Set(o.map(e=>e.user_uuid))),d=await (0,a.QZ)(c);return o.map(e=>{let t=d.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function o(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("affiliates").select("*").eq("invited_by",e),n={total_invited:0,total_paid:0,total_reward:0};if(a)return n;let i=new Set,o=new Set;return r.forEach(e=>{i.add(e.user_uuid),e.paid_amount>0&&(o.add(e.user_uuid),n.total_reward+=e.reward_amount)}),n.total_invited=i.size,n.total_paid=o.size,n}async function l(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("affiliates").select("*").eq("paid_order_no",e).single();if(!a)return r}},38391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(94331),a=r(78740),n=r(87638),i=r.n(n),o=r(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["my-invites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83329)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/my-invites/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51406)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/my-invites/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/my-invites/page",pathname:"/[locale]/my-invites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(96081);r(73636);var a=r(21280),n=r(80436),i=r(73515);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},47944:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(96081),a=r(79654);function n({value:e,options:t,className:r}){return(0,s.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var i=r(42711);function o({value:e,options:t,className:r}){return(0,s.jsx)(i.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(64694),c=r(3116);function d({value:e,options:t,className:r}){return(0,s.jsx)("div",{className:r,children:t?.format?(0,l.GP)(new Date(e),t?.format):(0,c.m)(new Date(e),{addSuffix:!0})})}var u=r(23044);function m({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,s.jsxs)(a.Table,{className:"w-full",children:[(0,s.jsx)(a.TableHeader,{className:"",children:(0,s.jsx)(a.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,s.jsx)(a.TableHead,{className:e.className,children:e.title},t))})}),(0,s.jsx)(a.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,s.jsx)(a.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let i=t[e.name],l=e.callback?e.callback(t):i,c=l;return"image"===e.type?c=(0,s.jsx)(n,{value:i,options:e.options,className:e.className}):"time"===e.type?c=(0,s.jsx)(d,{value:i,options:e.options,className:e.className}):"label"===e.type?c=(0,s.jsx)(o,{value:i,options:e.options,className:e.className}):"copy"===e.type&&i&&(c=(0,s.jsx)(u.default,{text:i,children:l})),(0,s.jsx)(a.TableCell,{className:e.className,children:c},r)})},r)):(0,s.jsx)(a.TableRow,{className:"",children:(0,s.jsx)(a.TableCell,{colSpan:e.length,children:(0,s.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,s.jsx)("p",{children:r})})})})})]})}},51406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(96081),a=r(74405);async function n({children:e,sidebar:t}){return(0,s.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,s.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[t?.nav?.items&&(0,s.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,s.jsx)(a.default,{items:t.nav?.items})}),(0,s.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var i=r(4850),o=r(53908),l=r(17984);async function c({children:e}){let t=await (0,o.ug)();t&&t.email||(0,l.redirect)("/auth/signin");let r=await (0,i.A)(),a={nav:{items:[{title:r("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:r("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:r("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:r("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,s.jsx)(n,{sidebar:a,children:e})}},51419:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(25323),a=r(13872),n=r(2783),i=r(52510),o=r(91145),l=r(41025);function c({className:e,items:t,...r}){let c=(0,l.usePathname)();return console.log(c),(0,s.jsx)("nav",{className:(0,o.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...r,children:t.map((e,t)=>(0,s.jsxs)(n.N_,{href:e.url,className:(0,o.cn)((0,i.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,s.jsx)(a.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58044:(e,t,r)=>{let{createProxy:s}=r(44856);e.exports=s("/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js")},59656:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>i,TableRow:()=>l});var s=r(25323);r(18542);var a=r(91145);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67611:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/invite/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/invite/index.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},74405:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx","default")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79654:(e,t,r)=>{"use strict";r.d(t,{Table:()=>a,TableBody:()=>i,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>n,TableRow:()=>l});var s=r(20263);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHeader"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableFooter");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHead"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableRow"),c=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCaption")},81630:e=>{"use strict";e.exports=require("http")},83329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(96081),a=r(14743);function n(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M19.3034 5.33716C17.9344 4.71103 16.4805 4.2547 14.9629 4C14.7719 4.32899 14.5596 4.77471 14.411 5.12492C12.7969 4.89144 11.1944 4.89144 9.60255 5.12492C9.45397 4.77471 9.2311 4.32899 9.05068 4C7.52251 4.2547 6.06861 4.71103 4.70915 5.33716C1.96053 9.39111 1.21766 13.3495 1.5891 17.2549C3.41443 18.5815 5.17612 19.388 6.90701 19.9187C7.33151 19.3456 7.71356 18.73 8.04255 18.0827C7.41641 17.8492 6.82211 17.5627 6.24904 17.2231C6.39762 17.117 6.5462 17.0003 6.68416 16.8835C10.1438 18.4648 13.8911 18.4648 17.3082 16.8835C17.4568 17.0003 17.5948 17.117 17.7434 17.2231C17.1703 17.5627 16.576 17.8492 15.9499 18.0827C16.2789 18.73 16.6609 19.3456 17.0854 19.9187C18.8152 19.388 20.5875 18.5815 22.4033 17.2549C22.8596 12.7341 21.6806 8.80747 19.3034 5.33716ZM8.5201 14.8459C7.48007 14.8459 6.63107 13.9014 6.63107 12.7447C6.63107 11.5879 7.45884 10.6434 8.5201 10.6434C9.57071 10.6434 10.4303 11.5879 10.4091 12.7447C10.4091 13.9014 9.57071 14.8459 8.5201 14.8459ZM15.4936 14.8459C14.4535 14.8459 13.6034 13.9014 13.6034 12.7447C13.6034 11.5879 14.4323 10.6434 15.4936 10.6434C16.5442 10.6434 17.4038 11.5879 17.3825 12.7447C17.3825 13.9014 16.5548 14.8459 15.4936 14.8459Z"},child:[]}]})(e)}function i(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 2C17.5228 2 22 6.47715 22 12C22 13.6169 21.6162 15.1442 20.9348 16.4958C20.8633 16.2175 20.7307 15.9523 20.5374 15.7206L20.4142 15.5858L19 14.1716L17.5858 15.5858L17.469 15.713C16.8069 16.4988 16.8458 17.6743 17.5858 18.4142C18.014 18.8424 18.588 19.0358 19.148 18.9946C17.3323 20.8487 14.8006 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12 15C10.6199 15 9.37036 15.5592 8.46564 16.4633L8.30009 16.6368L9.24506 17.4961C10.035 17.1825 10.982 17 12 17C12.9049 17 13.7537 17.1442 14.4859 17.3965L14.7549 17.4961L15.6999 16.6368C14.7853 15.6312 13.4664 15 12 15ZM8.5 10C7.67157 10 7 10.6716 7 11.5C7 12.3284 7.67157 13 8.5 13C9.32843 13 10 12.3284 10 11.5C10 10.6716 9.32843 10 8.5 10ZM15.5 10C14.6716 10 14 10.6716 14 11.5C14 12.3284 14.6716 13 15.5 13C16.3284 13 17 12.3284 17 11.5C17 10.6716 16.3284 10 15.5 10Z"},child:[]}]})(e)}var o=r(35482),l=r(87009),c=r(53908),d=r(67611),u=r(58044),m=r.n(u),p=r(47944),f=r(33102),x=r(4850),v=r(64694),b=r(17984);async function h(){let e=await (0,x.A)(),t=await (0,c.TG)(),r=await (0,c.qo)(),a="http://localhost:3000/my-invites";t||(0,b.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent(a)}`);let u=await (0,f.pX)(t);u||(0,b.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent(a)}`);let h=await (0,l.BJ)(t);if(h&&0!==h.length||(h=await (0,l.PG)(r)),u.is_affiliate=!0,h&&0!==h.length){let t=!1;for(let e of h)if("premium"===e.product_id){t=!0;break}if(!t&&!u.is_affiliate)return(0,s.jsxs)("div",{className:"text-center flex flex-col items-center justify-center h-full py-16 gap-4",children:[(0,s.jsx)(i,{className:"w-8 h-8"}),(0,s.jsx)("span",{children:e("my_invites.no_affiliates")}),(0,s.jsxs)(m(),{href:"https://discord.gg/HQNnrzjZQS",target:"_blank",className:"flex items-center gap-1 font-semibold text-sm text-primary border border-primary rounded-md px-4 py-2",children:[(0,s.jsx)(n,{className:"text-xl"}),"Discord"]})]})}else if(!u.is_affiliate)return(0,s.jsxs)("div",{className:"text-center flex flex-col items-center justify-center h-full py-16 gap-4",children:[(0,s.jsx)(i,{className:"w-8 h-8"}),(0,s.jsx)("span",{children:e("my_invites.no_orders")})]});let y=await (0,o.Ex)(t),g=await (0,o.A)(t),j=[{name:"created_at",title:e("my_invites.table.invite_time"),callback:e=>(0,v.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm:ss")},{name:"user",title:e("my_invites.table.invite_user"),callback:e=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e?.user?.avatar_url&&(0,s.jsx)("img",{src:e.user?.avatar_url||"",className:"w-8 h-8 rounded-full"}),(0,s.jsx)("span",{children:e.user?.nickname})]})},{name:"status",title:e("my_invites.table.status"),callback:t=>"pending"===t.status?e("my_invites.table.pending"):e("my_invites.table.completed")},{name:"reward_amount",title:e("my_invites.table.reward_amount"),callback:e=>`$${e.reward_amount/100}`}],_={title:e("my_invites.title"),description:e("my_invites.description"),tip:{description:e("my_invites.my_invite_link")},columns:j,data:y,empty_message:e("my_invites.no_invites")};return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)(d.default,{summary:g}),(0,s.jsx)(p.A,{..._})]})}},91645:e=>{"use strict";e.exports=require("net")},94658:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(25323),a=r(13180),n=r(95234);function i({text:e,children:t}){return(0,s.jsx)(a.CopyToClipboard,{text:e,onCopy:()=>n.oR.success("Copied"),children:(0,s.jsx)("div",{className:"cursor-pointer",children:t})})}},94735:e=>{"use strict";e.exports=require("events")},97455:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(25323),a=r(9392),n=r(18542),i=r(52510),o=r(13180),l=r(13872),c=r(41418),d=r(164),u=r(50533);function m({open:e,setOpen:t,username:r,initInviteCode:a,updateInviteCode:o,loading:l}){let m=(0,u.c3)(),[p,f]=(0,n.useState)(a);return(0,s.jsx)(c.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(c.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(c.c7,{children:[(0,s.jsx)(c.L3,{children:m("my_invites.update_invite_code")}),(0,s.jsx)(c.rr,{children:m("my_invites.update_invite_code_tip")})]}),(0,s.jsx)("div",{className:"grid gap-4 py-4",children:(0,s.jsx)("div",{className:"grid grid-cols-1 items-center gap-4",children:(0,s.jsx)(d.p,{placeholder:`${r}`,value:p,onChange:e=>f(e.target.value),className:"w-full"})})}),(0,s.jsx)(c.Es,{children:(0,s.jsx)(i.$,{onClick:()=>o(p),disabled:l,children:m("my_invites.update_invite_button")})})]})})}var p=r(95234),f=r(3124);function x({summary:e}){let t=(0,u.c3)(),[r,c]=(0,n.useState)(!1),{user:d,setUser:x}=(0,f.U)(),[v,b]=(0,n.useState)(!1),h=async function(e){try{if(!(e=e.trim()))return void p.oR.error("invite code is required");b(!0);let t={invite_code:e},r=await fetch("/api/update-invite-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error("update invite code faild with status "+r.status);let{code:s,message:a,data:n}=await r.json();if(0!==s)return void p.oR.error(a);x(n),p.oR.success("set invite code success"),c(!1)}catch(e){console.log("update invite code failed",e),p.oR.error("set invite code failed")}finally{b(!1)}};return(0,s.jsxs)("div",{className:"flex flex-wrap gap-6",children:[(0,s.jsxs)(a.Zp,{className:"flex-1 p-6",children:[(0,s.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:t("my_invites.invite_code")}),d&&d.uuid&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(m,{open:r,setOpen:c,username:d.nickname,initInviteCode:d.invite_code,updateInviteCode:h,loading:v}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-3xl font-bold",children:d.invite_code||"NOT SET"}),(0,s.jsx)(l.default,{name:"RiEditLine",className:"text-primary text-xl cursor-pointer",onClick:()=>c(!0)})]}),d.invite_code&&(0,s.jsx)(o.CopyToClipboard,{text:`http://localhost:3000/i/${d?.invite_code}`,onCopy:()=>p.oR.success("copied"),children:(0,s.jsx)(i.$,{size:"sm",children:t("my_invites.copy_invite_link")})})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.invite_tip")})]}),(0,s.jsxs)(a.Zp,{className:"flex-1 p-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-end mb-8",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:t("my_invites.invite_balance")}),(0,s.jsxs)("p",{className:"text-4xl font-bold",children:["$",e.total_reward/100]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold",children:e.total_invited}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_invite_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold",children:e.total_paid}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_paid_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-2xl font-bold",children:["$",e.total_reward/100]}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_award_amount")})]})]})]})]})}},97988:(e,t,r)=>{Promise.resolve().then(r.bind(r,74405)),Promise.resolve().then(r.bind(r,76735))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,3487,4552,6854,7125,484],()=>r(38391));module.exports=s})();