"use strict";(()=>{var e={};e.id=6914,e.ids=[6914],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21833:(e,r,t)=>{t.r(r),t.d(r,{default:()=>p});var o=t(96081),a=t(87009),s=t(53908),n=t(48468),l=t(4850),i=t(64694),d=t(17984);async function p(){let e=await (0,l.A)(),r=await (0,s.TG)(),t=await (0,s.qo)();r||(0,d.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent("http://localhost:3000/my-orders")}`);let p=await (0,a.BJ)(r);p&&0!==p.length||(p=await (0,a.PG)(t));let u=[{name:"order_no",title:e("my_orders.table.order_no")},{name:"paid_email",title:e("my_orders.table.email")},{name:"product_name",title:e("my_orders.table.product_name")},{name:"amount",title:e("my_orders.table.amount"),callback:e=>`${"CNY"===e.currency.toUpperCase()?"\xa5":"$"} ${e.amount/100}`},{name:"paid_at",title:e("my_orders.table.paid_at"),callback:e=>(0,i.GP)(new Date(e.paid_at),"yyyy-MM-dd HH:mm:ss")}],c={title:e("my_orders.title"),columns:u,data:p,empty_message:e("my_orders.no_orders")};return(0,o.jsx)(n.A,{...c})}},27910:e=>{e.exports=require("stream")},29227:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var o=t(94331),a=t(78740),s=t(87638),n=t.n(s),l=t(51681),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["my-orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21833)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/my-orders/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51406)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/my-orders/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/my-orders/page",pathname:"/[locale]/my-orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,3487,4552,6854,7125,484,693],()=>t(29227));module.exports=o})();