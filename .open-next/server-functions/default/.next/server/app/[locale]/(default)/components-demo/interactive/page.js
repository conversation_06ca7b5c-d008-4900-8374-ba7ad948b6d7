(()=>{var e={};e.id=246,e.ids=[246],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16710:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,X9:()=>c,ZB:()=>i,Zp:()=>s,aR:()=>o,wL:()=>u});var a=r(96081);r(73636);var n=r(73515);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19420:(e,t,r)=>{Promise.resolve().then(r.bind(r,41267)),Promise.resolve().then(r.bind(r,51201)),Promise.resolve().then(r.bind(r,23044)),Promise.resolve().then(r.bind(r,26343)),Promise.resolve().then(r.bind(r,79654)),Promise.resolve().then(r.bind(r,76735)),Promise.resolve().then(r.t.bind(r,58044,23))},22935:(e,t,r)=>{"use strict";r.d(t,{C1:()=>G,bL:()=>U,q7:()=>I});var a=r(18542),n=r(16760),s=r(30061),o=r(20849),i=r(34757),l=r(95783),c=r(6772),d=r(2286),u=r(40484),p=r(73724),m=r(78869),h=r(25323),f="Radio",[v,b]=(0,o.A)(f),[x,g]=v(f),y=a.forwardRef((e,t)=>{let{__scopeRadio:r,name:o,checked:l=!1,required:c,disabled:d,value:u="on",onCheck:p,form:m,...f}=e,[v,b]=a.useState(null),g=(0,s.s)(t,e=>b(e)),y=a.useRef(!1),j=!v||m||!!v.closest("form");return(0,h.jsxs)(x,{scope:r,checked:l,disabled:d,children:[(0,h.jsx)(i.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":C(l),"data-disabled":d?"":void 0,disabled:d,value:u,...f,ref:g,onClick:(0,n.m)(e.onClick,e=>{l||p?.(),j&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),j&&(0,h.jsx)(k,{control:v,bubbles:!y.current,name:o,value:u,checked:l,required:c,disabled:d,form:m,style:{transform:"translateX(-100%)"}})]})});y.displayName=f;var j="RadioIndicator",w=a.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:a,...n}=e,s=g(j,r);return(0,h.jsx)(m.C,{present:a||s.checked,children:(0,h.jsx)(i.sG.span,{"data-state":C(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})})});w.displayName=j;var k=a.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:n=!0,...o},l)=>{let c=a.useRef(null),d=(0,s.s)(c,l),m=(0,p.Z)(r),f=(0,u.X)(t);return a.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==r&&t){let a=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(a)}},[m,r,n]),(0,h.jsx)(i.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:d,style:{...o.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}k.displayName="RadioBubbleInput";var N=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],P="RadioGroup",[T,R]=(0,o.A)(P,[l.RG,b]),E=(0,l.RG)(),A=b(),[_,S]=T(P),B=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:a,defaultValue:n,value:s,required:o=!1,disabled:u=!1,orientation:p,dir:m,loop:f=!0,onValueChange:v,...b}=e,x=E(r),g=(0,d.jH)(m),[y,j]=(0,c.i)({prop:s,defaultProp:n??null,onChange:v,caller:P});return(0,h.jsx)(_,{scope:r,name:a,required:o,disabled:u,value:y,onValueChange:j,children:(0,h.jsx)(l.bL,{asChild:!0,...x,orientation:p,dir:g,loop:f,children:(0,h.jsx)(i.sG.div,{role:"radiogroup","aria-required":o,"aria-orientation":p,"data-disabled":u?"":void 0,dir:g,...b,ref:t})})})});B.displayName=P;var q="RadioGroupItem",L=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:o,...i}=e,c=S(q,r),d=c.disabled||o,u=E(r),p=A(r),m=a.useRef(null),f=(0,s.s)(t,m),v=c.value===i.value,b=a.useRef(!1);return a.useEffect(()=>{let e=e=>{N.includes(e.key)&&(b.current=!0)},t=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(l.q7,{asChild:!0,...u,focusable:!d,active:v,children:(0,h.jsx)(y,{disabled:d,required:c.required,checked:v,...p,...i,name:c.name,ref:f,onCheck:()=>c.onValueChange(i.value),onKeyDown:(0,n.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.m)(i.onFocus,()=>{b.current&&m.current?.click()})})})});L.displayName=q;var M=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...a}=e,n=A(r);return(0,h.jsx)(w,{...n,...a,ref:t})});M.displayName="RadioGroupIndicator";var U=B,I=L,G=M},23044:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/table/copy.tsx","default")},26709:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(25323);r(18542);var n=r(48771),s=r(51238),o=r(91145);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...s}){let l=r?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...s})}},29148:(e,t,r)=>{Promise.resolve().then(r.bind(r,69741)),Promise.resolve().then(r.bind(r,46893)),Promise.resolve().then(r.bind(r,94658)),Promise.resolve().then(r.bind(r,13872)),Promise.resolve().then(r.bind(r,59656)),Promise.resolve().then(r.bind(r,93497)),Promise.resolve().then(r.t.bind(r,63962,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36005:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(96081),n=r(31645),s=r(76999),o=r(51201),i=r(41267),l=r(47944),c=r(42711),d=r(16710),u=r(29753),p=r(26343);function m(){return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(c.E,{variant:"outline",className:"mb-4",children:"Interactive Components"}),(0,a.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Interactive Components Demo"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Engage your users with these interactive components: CTAs, FAQs, pricing tables, forms, and data tables."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(u.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,a.jsx)(p.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.default,{name:"RiMegaphoneLine",className:"w-5 h-5"}),"Call-to-Action Component"]}),(0,a.jsx)(d.BT,{children:"Encourage user action with compelling CTAs featuring background graphics and multiple buttons."})]})})}),(0,a.jsx)("div",{className:"border-y",children:(0,a.jsx)(n.A,{section:{name:"cta",title:"Ready to Get Started?",description:"Join thousands of developers who are already building amazing applications with our platform.",buttons:[{title:"Start Free Trial",url:"/signup",variant:"default",icon:"RiRocketLine"},{title:"Contact Sales",url:"/contact",variant:"outline",icon:"RiPhoneLine"}]}})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.default,{name:"RiQuestionLine",className:"w-5 h-5"}),"FAQ Component"]}),(0,a.jsx)(d.BT,{children:"Display frequently asked questions in a clean, numbered format."})]})})}),(0,a.jsx)("div",{className:"border-y bg-muted/30",children:(0,a.jsx)(s.A,{section:{name:"faq",label:"FAQ",title:"Frequently Asked Questions",description:"Find answers to common questions about our platform and services.",items:[{title:"How do I get started?",description:"Getting started is easy! Simply sign up for a free account, choose your plan, and follow our quick setup guide. You'll be up and running in minutes."},{title:"What technologies do you support?",description:"We support all major web technologies including React, Vue, Angular, Node.js, Python, and more. Our platform is designed to work with your existing tech stack."},{title:"Is there a free trial available?",description:"Yes! We offer a 14-day free trial with full access to all features. No credit card required to get started."},{title:"How does pricing work?",description:"Our pricing is based on usage and features. We offer flexible plans for individuals, teams, and enterprises. Check our pricing page for detailed information."},{title:"Do you offer customer support?",description:"Absolutely! We provide 24/7 customer support via email, chat, and phone. Our team is always ready to help you succeed."},{title:"Can I cancel my subscription anytime?",description:"Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees."}]}})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.default,{name:"RiMoneyDollarCircleLine",className:"w-5 h-5"}),"Pricing Component"]}),(0,a.jsx)(d.BT,{children:"Showcase pricing plans with features, billing toggles, and call-to-action buttons."})]})})}),(0,a.jsx)("div",{className:"border-y",children:(0,a.jsx)(o.default,{pricing:{name:"pricing",title:"Choose Your Plan",description:"Select the perfect plan for your needs. Upgrade or downgrade at any time.",groups:[{name:"monthly",title:"Monthly"},{name:"yearly",title:"Yearly"}],items:[{title:"Starter",description:"Perfect for individuals and small projects",price:"$9",original_price:"$19",unit:"/month",label:"Most Popular",is_featured:!0,features:["Up to 5 projects","10GB storage","Basic support","Community access","SSL certificates"],button:{title:"Get Started",url:"/signup?plan=starter"},interval:"month",product_id:"starter",amount:900,currency:"USD"},{title:"Professional",description:"Ideal for growing teams and businesses",price:"$29",unit:"/month",features:["Unlimited projects","100GB storage","Priority support","Advanced analytics","Custom domains","Team collaboration"],button:{title:"Get Started",url:"/signup?plan=pro"},interval:"month",product_id:"professional",amount:2900,currency:"USD"},{title:"Enterprise",description:"For large organizations with custom needs",price:"Custom",unit:"",features:["Everything in Professional","Unlimited storage","24/7 phone support","Custom integrations","SLA guarantee","Dedicated account manager"],button:{title:"Contact Sales",url:"/contact"},interval:"one-time",product_id:"enterprise",amount:0,currency:"USD"}]}})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.default,{name:"RiFileTextLine",className:"w-5 h-5"}),"Form Component"]}),(0,a.jsx)(d.BT,{children:"Create dynamic forms with various field types, validation, and submission handling."})]})})}),(0,a.jsx)("div",{className:"border-y bg-muted/30",children:(0,a.jsx)("div",{className:"container mx-auto py-16",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Contact Us"}),(0,a.jsx)(d.BT,{children:"Get in touch with our team. We'd love to hear from you!"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)(i.default,{fields:[{name:"name",title:"Full Name",type:"text",placeholder:"Enter your full name",validation:{required:!0}},{name:"email",title:"Email Address",type:"email",placeholder:"Enter your email address",validation:{required:!0,email:!0}},{name:"company",title:"Company",type:"text",placeholder:"Enter your company name"},{name:"subject",title:"Subject",type:"select",placeholder:"Select a subject",validation:{required:!0},options:[{title:"General Inquiry",value:"general"},{title:"Technical Support",value:"support"},{title:"Sales Question",value:"sales"},{title:"Partnership",value:"partnership"}]},{name:"message",title:"Message",type:"textarea",placeholder:"Tell us how we can help you...",validation:{required:!0}}]})})]})})})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(d.Zp,{children:(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.default,{name:"RiTableLine",className:"w-5 h-5"}),"Table Component"]}),(0,a.jsx)(d.BT,{children:"Display data in sortable tables with action buttons and status indicators."})]})})}),(0,a.jsx)("div",{className:"border-y bg-muted/30",children:(0,a.jsx)("div",{className:"container mx-auto py-16",children:(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"Recent Projects"}),(0,a.jsx)(d.BT,{children:"Overview of your recent projects and their status"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)(l.A,{columns:[{name:"name",title:"Project Name",type:"text"},{name:"status",title:"Status",type:"label"},{name:"created",title:"Created",type:"time"},{name:"updated",title:"Last Updated",type:"time"},{name:"actions",title:"Actions",type:"actions"}],data:[{id:"1",name:"E-commerce Platform",status:"Active",created:"2025-01-15",updated:"2025-01-19"},{id:"2",name:"Mobile App Backend",status:"In Progress",created:"2025-01-10",updated:"2025-01-18"},{id:"3",name:"Analytics Dashboard",status:"Completed",created:"2025-01-05",updated:"2025-01-17"},{id:"4",name:"API Gateway",status:"Paused",created:"2025-01-01",updated:"2025-01-16"}]})})]})})})]})]})}},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(96081);r(73636);var n=r(21280),s=r(80436),o=r(73515);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...s}){let l=r?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...s})}},47720:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(96081);r(73636);var n=r(21280),s=r(80436),o=r(73515);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let c=s?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}},47944:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var a=r(96081),n=r(79654);function s({value:e,options:t,className:r}){return(0,a.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var o=r(42711);function i({value:e,options:t,className:r}){return(0,a.jsx)(o.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(64694),c=r(3116);function d({value:e,options:t,className:r}){return(0,a.jsx)("div",{className:r,children:t?.format?(0,l.GP)(new Date(e),t?.format):(0,c.m)(new Date(e),{addSuffix:!0})})}var u=r(23044);function p({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,a.jsxs)(n.Table,{className:"w-full",children:[(0,a.jsx)(n.TableHeader,{className:"",children:(0,a.jsx)(n.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,a.jsx)(n.TableHead,{className:e.className,children:e.title},t))})}),(0,a.jsx)(n.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,a.jsx)(n.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let o=t[e.name],l=e.callback?e.callback(t):o,c=l;return"image"===e.type?c=(0,a.jsx)(s,{value:o,options:e.options,className:e.className}):"time"===e.type?c=(0,a.jsx)(d,{value:o,options:e.options,className:e.className}):"label"===e.type?c=(0,a.jsx)(i,{value:o,options:e.options,className:e.className}):"copy"===e.type&&o&&(c=(0,a.jsx)(u.default,{text:o,children:l})),(0,a.jsx)(n.TableCell,{className:e.className,children:c},r)})},r)):(0,a.jsx)(n.TableRow,{className:"",children:(0,a.jsx)(n.TableCell,{colSpan:e.length,children:(0,a.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:r})})})})})]})}},58044:(e,t,r)=>{let{createProxy:a}=r(44856);e.exports=a("/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js")},59656:(e,t,r)=>{"use strict";r.d(t,{Table:()=>s,TableBody:()=>i,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>o,TableRow:()=>l});var a=r(25323);r(18542);var n=r(91145);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79654:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableHead:()=>i,TableHeader:()=>s,TableRow:()=>l});var a=r(20263);let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","Table"),s=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHeader"),o=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableBody");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableFooter");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableHead"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableRow"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCell");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/table.tsx","TableCaption")},80359:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(94331),n=r(78740),s=r(87638),o=r.n(s),i=r(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["components-demo",{children:["interactive",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36005)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/interactive/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/interactive/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(default)/components-demo/interactive/page",pathname:"/[locale]/components-demo/interactive",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81974:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(78108).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},90225:(e,t,r)=>{"use strict";r.d(t,{c:()=>b});var a,n="https://js.stripe.com",s="".concat(n,"/v3"),o=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,i=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(n,'"]')),t=0;t<e.length;t++){var r,a=e[t];if(r=a.src,o.test(r)||i.test(r))return a}return null},c=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(s).concat(t);var a=document.head||document.body;if(!a)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return a.appendChild(r),r},d=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.10.0",startTime:t})},u=null,p=null,m=null,h=function(e,t,r){if(null===e)return null;var a,n=t[0].match(/^pk_test/),s=3===(a=e.version)?"v3":a;n&&"v3"!==s&&console.warn("Stripe.js@".concat(s," was loaded on the page, but @stripe/stripe-js@").concat("5.10.0"," expected Stripe.js@").concat("v3",". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var o=e.apply(void 0,t);return d(o,r),o},f=!1,v=function(){return a?a:a=(null!==u?u:(u=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document)return void e(null);if(window.Stripe,window.Stripe)return void e(window.Stripe);try{var r,a=l();a?a&&null!==m&&null!==p&&(a.removeEventListener("load",m),a.removeEventListener("error",p),null==(r=a.parentNode)||r.removeChild(a),a=c(null)):a=c(null),m=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},p=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},a.addEventListener("load",m),a.addEventListener("error",p)}catch(e){t(e);return}})).catch(function(e){return u=null,Promise.reject(e)})).catch(function(e){return a=null,Promise.reject(e)})};Promise.resolve().then(function(){return v()}).catch(function(e){f||console.warn(e)});var b=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];f=!0;var a=Date.now();return v().then(function(e){return h(e,t,a)})}},94658:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(25323),n=r(13180),s=r(95234);function o({text:e,children:t}){return(0,a.jsx)(n.CopyToClipboard,{text:e,onCopy:()=>s.oR.success("Copied"),children:(0,a.jsx)("div",{className:"cursor-pointer",children:t})})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,4552,8395,7231,6854,484,2767,3644],()=>r(80359));module.exports=a})();