"use strict";(()=>{var e={};e.id=4598,e.ids=[4598],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14949:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y,generateMetadata:()=>j});var a=r(96081),s=r(89831),o=r(56668),n=r(31645),i=r(76999),l=r(16545),d=r(2974),c=r(53617),p=r(73212),u=r(835),x=r(51201),f=r(3658),m=r(62774),h=r(60752),g=r(36151);async function j({params:e}){let{locale:t}=await e,r=await (0,h.JE)(t),a="http://localhost:3000/";return"en"!==t&&(a=`http://localhost:3000/${t}/`),{title:{template:"%s",default:r.metadata.title||""},description:r.metadata.description||"",keywords:r.metadata.keywords||"",alternates:{canonical:a}}}async function y({params:e}){let{locale:t}=await e,r=await (0,h.JE)(t),j="http://localhost:3000",y="en"===t?`${j}`:`${j}/${t}`,b={title:r.hero?.title||"KREA FLUX - Advanced Image to Image AI Platform",description:r.hero?.description?.replace(/<br\s*\/?>/gi," ")||r.feature?.description||"Revolutionary image to image AI technology that transforms, enhances, and converts images with precision. Professional AI image transformation tools.",image:`${j}/imgs/features/ai_dashboard_interface.png`,url:y,schemaType:"SoftwareApplication",applicationCategory:"WebApplication",operatingSystem:"Web Browser, iOS, Android",datePublished:"2024-01-01T00:00:00Z",dateModified:new Date().toISOString(),breadcrumb:[]};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.Ay,{...b}),r.hero&&(0,a.jsx)(u.A,{hero:r.hero}),(0,a.jsx)(s.WorkspaceLayout,{}),r.branding&&(0,a.jsx)(o.A,{section:r.branding}),r.introduce&&(0,a.jsx)(d.A,{section:r.introduce}),r.benefit&&(0,a.jsx)(c.default,{section:r.benefit}),r.usage&&(0,a.jsx)(p.A,{section:r.usage}),r.feature&&(0,a.jsx)(l.A,{section:r.feature}),r.showcase&&(0,a.jsx)(f.Ay,{section:r.showcase}),r.stats&&(0,a.jsx)(m.A,{section:r.stats}),r.pricing&&(0,a.jsx)(x.default,{pricing:r.pricing}),r.faq&&(0,a.jsx)(i.A,{section:r.faq}),r.cta&&(0,a.jsx)(n.A,{section:r.cta})]})}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},48679:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(94331),s=r(78740),o=r(87638),n=r.n(o),i=r(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,14949)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,4622,8534,8525,7051,6854,484,4882,8742,3644,631],()=>r(48679));module.exports=a})();