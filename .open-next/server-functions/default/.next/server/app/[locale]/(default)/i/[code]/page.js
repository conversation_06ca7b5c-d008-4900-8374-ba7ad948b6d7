(()=>{var e={};e.id=9240,e.ids=[9240],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28859:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/i/[code]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/i/[code]/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40533:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(94331),o=t(78740),a=t(87638),n=t.n(a),i=t(51681),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["i",{children:["[code]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28859)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/i/[code]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/i/[code]/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(default)/i/[code]/page",pathname:"/[locale]/i/[code]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40551:(e,r,t)=>{Promise.resolve().then(t.bind(t,28859))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64929:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(25323);t(5261),t(57566),t(31179),t(18542);var o=t(41025);function a(){return(0,o.useParams)().code,(0,s.jsx)("div",{className:"w-screen h-screen flex items-center justify-center",children:"loading..."})}},70815:(e,r,t)=>{Promise.resolve().then(t.bind(t,64929))}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,6854,484],()=>t(40533));module.exports=s})();