(()=>{var e={};e.id=4106,e.ids=[4106],e.modules={164:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(25323);t(18542);var i=t(91145);function a({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4850:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(73636),i=t(47830),a=t(13266),n=(0,s.cache)(function(e,r){return function({_cache:e=(0,a.d)(),_formatters:r=(0,a.b)(e),getMessageFallback:t=a.f,messages:s,namespace:i,onError:n=a.g,...o}){return function({messages:e,namespace:r,...t},s){return e=e["!"],r=(0,a.r)(r,"!"),(0,a.e)({...t,messages:e,namespace:r})}({...o,onError:n,cache:e,formatters:r,getMessageFallback:t,messages:{"!":s},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:r})}),o=(0,s.cache)(async function(e){let r,t;return"string"==typeof e?r=e:e&&(t=e.locale,r=e.namespace),n(await (0,i.A)(t),r)})},6385:(e,r,t)=>{"use strict";t.d(r,{b:()=>c});var s=t(18542),i=t(34757),a=t(25323),n="horizontal",o=["horizontal","vertical"],l=s.forwardRef((e,r)=>{var t;let{decorative:s,orientation:l=n,...c}=e,d=(t=l,o.includes(t))?l:n;return(0,a.jsx)(i.sG.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:r})});l.displayName="Separator";var c=l},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11983:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(94331),i=t(78740),a=t(87638),n=t.n(a),o=t(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["api-keys",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34797)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/api-keys/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51406)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/api-keys/create/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/api-keys/create/page",pathname:"/[locale]/api-keys/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},11997:e=>{"use strict";e.exports=require("punycode")},16175:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T,"6009653ed25d200730328701064706d3ea339da438":()=>i.$$RSC_SERVER_ACTION_0});var s=t(10061),i=t(34797)},17121:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>n});var s=t(25323);t(18542);var i=t(6385),a=t(91145);function n({className:e,orientation:r="horizontal",decorative:t=!0,...n}){return(0,s.jsx)(i.b,{"data-slot":"separator-root",decorative:t,orientation:r,className:(0,a.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21280:(e,r,t)=>{"use strict";t.d(r,{DX:()=>n});var s=t(73636);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=t(96081),n=function(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){var n;let e,o,l=(n=t,(o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),c=function(e,r){let t={...r};for(let s in r){let i=e[s],a=r[s];/^on[A-Z]/.test(s)?i&&a?t[s]=(...e)=>{let r=a(...e);return i(...e),r}:i&&(t[s]=i):"style"===s?t[s]={...i,...a}:"className"===s&&(t[s]=[i,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(c.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=i(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():i(e[r],null)}}}}(r,l):l),s.cloneElement(t,c)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:i,...n}=e,o=s.Children.toArray(i),c=o.find(l);if(c){let e=c.props.children,i=o.map(r=>r!==c?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...n,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,a.jsx)(r,{...n,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},21490:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(96081),i=t(47720),a=t(26343),n=t(29753);function o({items:e}){return(0,s.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,r)=>e.url?(0,s.jsx)(n.N_,{href:e.url,children:(0,s.jsxs)(i.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,s.jsx)(a.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},r):(0,s.jsxs)(i.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&(0,s.jsx)(a.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},r))})}},21956:(e,r,t)=>{Promise.resolve().then(t.bind(t,51419)),Promise.resolve().then(t.bind(t,93497))},24747:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>s});let s=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/ui/separator.tsx","Separator")},25160:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(73636);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ");var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:i,className:o="",children:l,iconNode:c,...d},u)=>(0,s.createElement)("svg",{ref:u,...n,width:r,height:r,stroke:e,strokeWidth:i?24*Number(t)/Number(r):t,className:a("lucide",o),...d},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(l)?l:[l]])),l=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...n},l)=>(0,s.createElement)(o,{ref:l,iconNode:r,className:a(`lucide-${i(e)}`,t),...n}));return t.displayName=`${e}`,t}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32110:(e,r,t)=>{Promise.resolve().then(t.bind(t,69741)),Promise.resolve().then(t.bind(t,13872)),Promise.resolve().then(t.bind(t,17121)),Promise.resolve().then(t.bind(t,93497)),Promise.resolve().then(t.t.bind(t,63962,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34797:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$RSC_SERVER_ACTION_0:()=>h,default:()=>x});var s=t(96081),i=t(16454);t(15358);var a=t(44962),n=t(72160),o=t(40070),l=t(41267),c=t(24747),d=t(21490);function u({...e}){return(0,s.jsxs)("div",{className:"space-y-6",children:[e.crumb?.items&&(0,s.jsx)(o.A,{items:e.crumb.items}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.tip&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.tip.description||e.tip.title}),e.toolbar&&(0,s.jsx)(d.A,{items:e.toolbar.items}),(0,s.jsx)(c.Separator,{}),(0,s.jsx)(l.default,{...e})]})}var p=t(88661),m=t(14584),f=t(4850),v=t(53908);let h=async function(e,r){let{user_uuid:t}=r;if(!t)throw Error("no auth");let s=e.get("title");if(!s||!s.trim())throw Error("invalid params");let i={user_uuid:t,api_key:`sk-${(0,m.f1)(32)}`,title:s,created_at:(0,p.iq)(),status:a.ai.Created};try{return await (0,a.N1)(i),{status:"success",message:"apikey created",redirect_url:"/api-keys"}}catch(e){throw console.error(e),Error("create api key failed: "+e.message)}};async function x(){let e=await (0,f.A)(),r=await (0,v.TG)();if(!r)return(0,s.jsx)(n.A,{message:"no auth"});let t={title:e("api_keys.create_api_key"),crumb:{items:[{title:e("api_keys.title"),url:"/api-keys"},{title:e("api_keys.create_api_key"),is_active:!0}]},fields:[{title:e("api_keys.form.name"),name:"title",type:"text",placeholder:e("api_keys.form.name_placeholder"),validation:{required:!0}}],passby:{user_uuid:r},submit:{button:{title:e("api_keys.form.submit"),icon:"RiCheckLine"},handler:(0,i.A)(h,"6009653ed25d200730328701064706d3ea339da438",null)}};return(0,s.jsx)(u,{...t})}},40070:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(96081),i=t(82646),a=t(58044),n=t.n(a);function o({items:e}){return(0,s.jsx)("nav",{className:"flex items-center text-sm text-muted-foreground",children:e.map((e,r)=>{let t=e.is_active;return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n(),{href:e.url||"",className:`hover:text-foreground transition-colors ${t?"text-primary font-medium hover:text-primary":""}`,children:e.title}),!t&&(0,s.jsx)(i.A,{className:"h-4 w-4 mx-2 text-muted-foreground/40"})]},r)})})}},47720:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(96081);t(73636);var i=t(21280),a=t(80436),n=t(73515);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:a=!1,...l}){let c=a?i.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...l})}},51406:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(96081),i=t(74405);async function a({children:e,sidebar:r}){return(0,s.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,s.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[r?.nav?.items&&(0,s.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,s.jsx)(i.default,{items:r.nav?.items})}),(0,s.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var n=t(4850),o=t(53908),l=t(17984);async function c({children:e}){let r=await (0,o.ug)();r&&r.email||(0,l.redirect)("/auth/signin");let t=await (0,n.A)(),i={nav:{items:[{title:t("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:t("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:t("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:t("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,s.jsx)(a,{sidebar:i,children:e})}},51419:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var s=t(25323),i=t(13872),a=t(2783),n=t(52510),o=t(91145),l=t(41025);function c({className:e,items:r,...t}){let c=(0,l.usePathname)();return console.log(c),(0,s.jsx)("nav",{className:(0,o.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...t,children:r.map((e,r)=>(0,s.jsxs)(a.N_,{href:e.url,className:(0,o.cn)((0,n.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,s.jsx)(i.default,{name:e.icon,className:"w-4 h-4"}),e.title]},r))})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58044:(e,r,t)=>{let{createProxy:s}=t(44856);e.exports=s("/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js")},62374:(e,r,t)=>{Promise.resolve().then(t.bind(t,41267)),Promise.resolve().then(t.bind(t,26343)),Promise.resolve().then(t.bind(t,24747)),Promise.resolve().then(t.bind(t,76735)),Promise.resolve().then(t.t.bind(t,58044,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72160:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(96081);function i({message:e}){return(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,s.jsx)("p",{children:e})})}},74075:e=>{"use strict";e.exports=require("zlib")},74405:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/console/sidebar/nav.tsx","default")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80436:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var s=t(64556);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.$,n=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:o}=r,l=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],s=null==o?void 0:o[e];if(null===r)return null;let a=i(r)||i(s);return n[e][a]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return a(e,l,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...c}[r]):({...o,...c})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},81630:e=>{"use strict";e.exports=require("http")},82646:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(25160).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97988:(e,r,t)=>{Promise.resolve().then(t.bind(t,74405)),Promise.resolve().then(t.bind(t,76735))}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,3487,8395,7231,6854,7125,484,2767],()=>t(11983));module.exports=s})();