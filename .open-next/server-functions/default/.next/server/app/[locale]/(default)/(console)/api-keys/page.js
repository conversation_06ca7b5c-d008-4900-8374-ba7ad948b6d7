"use strict";(()=>{var e={};e.id=8653,e.ids=[8653],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72160:(e,r,t)=>{t.d(r,{A:()=>s});var a=t(96081);function s({message:e}){return(0,a.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,a.jsx)("p",{children:e})})}},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},81771:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>c,tree:()=>p});var a=t(94331),s=t(78740),o=t(87638),i=t.n(o),l=t(51681),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let p={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["api-keys",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,82754)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/api-keys/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51406)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/(console)/api-keys/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/api-keys/page",pathname:"/[locale]/api-keys",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},82754:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var a=t(96081),s=t(72160),o=t(48468),i=t(4850),l=t(44962),n=t(53908),p=t(3116);async function u(){let e=await (0,i.A)(),r=await (0,n.TG)();if(!r)return(0,a.jsx)(s.A,{message:"no auth"});let t=await (0,l.ox)(r),u={title:e("api_keys.title"),tip:{title:e("api_keys.tip")},toolbar:{items:[{title:e("api_keys.create_api_key"),url:"/api-keys/create",icon:"RiAddLine"}]},columns:[{title:e("api_keys.table.name"),name:"title"},{title:e("api_keys.table.key"),name:"api_key",type:"copy",callback:e=>e.api_key.slice(0,4)+"..."+e.api_key.slice(-4)},{title:e("api_keys.table.created_at"),name:"created_at",callback:e=>(0,p.m)(new Date(e.created_at),{addSuffix:!0})}],data:t,empty_message:e("api_keys.no_api_keys")};return(0,a.jsx)(o.A,{...u})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,1098,8723,4626,3487,4552,6854,7125,484,693],()=>t(81771));module.exports=a})();