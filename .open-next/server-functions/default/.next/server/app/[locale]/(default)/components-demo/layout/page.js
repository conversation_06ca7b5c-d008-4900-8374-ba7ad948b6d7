(()=>{var e={};e.id=6776,e.ids=[6776],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16710:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>a,Zp:()=>s,aR:()=>o,wL:()=>u});var n=r(96081);r(73636);var i=r(73515);function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-action",className:(0,i.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21280:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o});var n=r(73636);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var s=r(96081),o=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var o;let e,a,l=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,l):l),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...o}=e,a=n.Children.toArray(i),d=a.find(l);if(d){let e=d.props.children,i=a.map(t=>t!==d?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...o,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}("Slot"),a=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},25160:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(73636);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:a="",children:l,iconNode:d,...c},u)=>(0,n.createElement)("svg",{ref:u,...o,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:s("lucide",a),...c},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},l)=>(0,n.createElement)(a,{ref:l,iconNode:t,className:s(`lucide-${i(e)}`,r),...o}));return r.displayName=`${e}`,r}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40070:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(96081),i=r(82646),s=r(58044),o=r.n(s);function a({items:e}){return(0,n.jsx)("nav",{className:"flex items-center text-sm text-muted-foreground",children:e.map((e,t)=>{let r=e.is_active;return(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(o(),{href:e.url||"",className:`hover:text-foreground transition-colors ${r?"text-primary font-medium hover:text-primary":""}`,children:e.title}),!r&&(0,n.jsx)(i.A,{className:"h-4 w-4 mx-2 text-muted-foreground/40"})]},t)})})}},42711:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(96081);r(73636);var i=r(21280),s=r(80436),o=r(73515);let a=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...s}){let l=r?i.DX:"span";return(0,n.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(a({variant:t}),e),...s})}},47403:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(96081),i=r(75114),s=r(54596),o=r(40070),a=r(42711),l=r(16710),d=r(29753),c=r(26343);function u(){let e={brand:{title:"DemoApp",logo:{src:"/logo.png",alt:"DemoApp"},url:"/"},nav:{items:[{title:"Features",url:"/#features",icon:"RiSparkling2Line"},{title:"Pricing",url:"/pricing",icon:"RiMoneyDollarCircleLine"},{title:"About",url:"/about",icon:"RiInformationLine"},{title:"Resources",url:"#",icon:"RiBookLine",children:[{title:"Documentation",url:"/docs",icon:"RiFileTextLine"},{title:"API Reference",url:"/api",icon:"RiCodeLine"},{title:"Tutorials",url:"/tutorials",icon:"RiPlayLine"}]}]},buttons:[{title:"Get Started",url:"/signup",variant:"default",icon:"RiRocketLine"},{title:"Login",url:"/login",variant:"outline"}],show_sign:!0,show_theme:!0,show_locale:!0},t={brand:{title:"DemoApp",description:"A powerful platform for building amazing applications with modern tools and best practices.",logo:{src:"/logo.png",alt:"DemoApp"},url:"/"},nav:{items:[{title:"Product",children:[{title:"Features",url:"/features"},{title:"Pricing",url:"/pricing"},{title:"API",url:"/api"},{title:"Integrations",url:"/integrations"}]},{title:"Company",children:[{title:"About",url:"/about"},{title:"Blog",url:"/blog"},{title:"Careers",url:"/careers"},{title:"Contact",url:"/contact"}]},{title:"Resources",children:[{title:"Documentation",url:"/docs"},{title:"Help Center",url:"/help"},{title:"Community",url:"/community"},{title:"Status",url:"/status"}]}]},social:{items:[{title:"Twitter",icon:"RiTwitterXLine",url:"https://twitter.com/demoapp"},{title:"GitHub",icon:"RiGithubLine",url:"https://github.com/demoapp"},{title:"Discord",icon:"RiDiscordLine",url:"https://discord.gg/demoapp"}]},copyright:"\xa9 2025 DemoApp. All rights reserved.",agreement:{items:[{title:"Privacy Policy",url:"/privacy"},{title:"Terms of Service",url:"/terms"},{title:"Cookie Policy",url:"/cookies"}]}};return(0,n.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(a.E,{variant:"outline",className:"mb-4",children:"Layout Components"}),(0,n.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Layout Components Demo"}),(0,n.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Essential layout components for structuring your application: headers, footers, and navigation elements."})]}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)(d.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,n.jsx)(c.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,n.jsxs)("section",{className:"mb-16",children:[(0,n.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,n.jsx)(l.Zp,{children:(0,n.jsxs)(l.aR,{children:[(0,n.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.default,{name:"RiLayoutLine",className:"w-5 h-5"}),"Header Component"]}),(0,n.jsx)(l.BT,{children:"Navigation header with brand, menu items, buttons, and utility toggles. Supports responsive design with mobile menu."})]})})}),(0,n.jsx)("div",{className:"border-y bg-card",children:(0,n.jsx)(i.default,{header:e})})]}),(0,n.jsx)("section",{className:"mb-16",children:(0,n.jsx)("div",{className:"container mx-auto px-4",children:(0,n.jsxs)(l.Zp,{className:"mb-8",children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.default,{name:"RiNavigationLine",className:"w-5 h-5"}),"Breadcrumb Component"]}),(0,n.jsx)(l.BT,{children:"Navigation breadcrumbs to show the current page location within the site hierarchy."})]}),(0,n.jsx)(l.Wu,{children:(0,n.jsx)("div",{className:"p-4 border rounded-lg bg-muted/30",children:(0,n.jsx)(o.A,{items:[{title:"Home",url:"/"},{title:"Components",url:"/components-demo"},{title:"Layout",url:"/components-demo/layout"}]})})})]})})}),(0,n.jsxs)("section",{className:"mb-16",children:[(0,n.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,n.jsx)(l.Zp,{children:(0,n.jsxs)(l.aR,{children:[(0,n.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.default,{name:"RiLayoutBottomLine",className:"w-5 h-5"}),"Footer Component"]}),(0,n.jsx)(l.BT,{children:"Site footer with brand information, navigation links, social media, and legal links."})]})})}),(0,n.jsx)("div",{className:"border-y bg-card",children:(0,n.jsx)(s.A,{footer:t})})]}),(0,n.jsx)("div",{className:"container mx-auto px-4 pb-16",children:(0,n.jsxs)(l.Zp,{children:[(0,n.jsx)(l.aR,{children:(0,n.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.default,{name:"RiCodeLine",className:"w-5 h-5"}),"Usage Examples"]})}),(0,n.jsxs)(l.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"Header Component"}),(0,n.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<Header header={headerData} />"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"Footer Component"}),(0,n.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<Footer footer={footerData} />"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"Breadcrumb Component"}),(0,n.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<Crumb items={breadcrumbItems} />"})]})]})]})})]})}},51540:(e,t,r)=>{Promise.resolve().then(r.bind(r,75114)),Promise.resolve().then(r.bind(r,26343)),Promise.resolve().then(r.bind(r,76735)),Promise.resolve().then(r.t.bind(r,58044,23))},58044:(e,t,r)=>{let{createProxy:n}=r(44856);e.exports=n("/Users/<USER>/My-Project/fluxkrea/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70100:(e,t,r)=>{Promise.resolve().then(r.bind(r,62230)),Promise.resolve().then(r.bind(r,13872)),Promise.resolve().then(r.bind(r,93497)),Promise.resolve().then(r.t.bind(r,63962,23))},80436:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(64556);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let s=i(t)||i(n);return o[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...d}[t]):({...a,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},82646:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(25160).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},89985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(94331),i=r(78740),s=r(87638),o=r.n(s),a=r(51681),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["components-demo",{children:["layout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47403)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/layout/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/layout/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/(default)/components-demo/layout/page",pathname:"/[locale]/components-demo/layout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,6854,484],()=>r(89985));module.exports=n})();