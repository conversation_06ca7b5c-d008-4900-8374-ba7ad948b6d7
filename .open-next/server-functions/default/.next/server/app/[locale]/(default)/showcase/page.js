(()=>{var e={};e.id=6294,e.ids=[6294],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3658:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var s=r(96081),o=r(16710),a=r(31884),n=r(58044),l=r.n(n);function i({section:e}){return e.disabled?null:(0,s.jsxs)("section",{className:"container py-16",children:[(0,s.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,s.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,s.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.items?.map((e,t)=>(0,s.jsx)(l(),{href:e.url||"",target:e.target,children:(0,s.jsx)(o.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 p-0",children:(0,s.jsxs)(o.Wu,{className:"p-0",children:[(0,s.jsx)("div",{className:"relative aspect-[16/10] w-full overflow-hidden",children:(0,s.jsx)(a.default,{src:e.image?.src||"",alt:e.image?.alt||e.title||"",fill:!0,className:"object-cover rounded-t-lg transition-transform duration-300 hover:scale-110"})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 line-clamp-1",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-3",children:e.description})]})]})})},t))})]})}r(90810),r(55233)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16710:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,X9:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>n,wL:()=>u});var s=r(96081);r(73636);var o=r(73515);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,o.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55233:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/showcase/comparison-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/showcase/comparison-showcase.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72365:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(96081),o=r(3658),a=r(60752);async function n({params:e}){let{locale:t}=await e,r=await (0,a.DN)(t);return(0,s.jsx)(s.Fragment,{children:r.showcase&&(0,s.jsx)(o.Ay,{section:r.showcase})})}},80936:(e,t,r)=>{Promise.resolve().then(r.bind(r,73555)),Promise.resolve().then(r.bind(r,84828)),Promise.resolve().then(r.t.bind(r,63962,23)),Promise.resolve().then(r.t.bind(r,60961,23))},90810:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/showcase/prompt-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/showcase/prompt-showcase.tsx","default")},94088:(e,t,r)=>{Promise.resolve().then(r.bind(r,55233)),Promise.resolve().then(r.bind(r,90810)),Promise.resolve().then(r.t.bind(r,58044,23)),Promise.resolve().then(r.t.bind(r,22559,23))},96553:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(94331),o=r(78740),a=r(87638),n=r.n(a),l=r(51681),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["[locale]",{children:["(default)",{children:["showcase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72365)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/showcase/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/showcase/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(default)/showcase/page",pathname:"/[locale]/showcase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,4622,8534,6854,484,4882],()=>r(96553));module.exports=s})();