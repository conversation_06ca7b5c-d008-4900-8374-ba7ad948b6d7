(()=>{var e={};e.id=3423,e.ids=[3423],e.modules={164:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var r=t(25323);t(18542);var s=t(91145);function n({className:e,type:a,...t}){return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5155:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(94331),s=t(78740),n=t(87638),i=t.n(n),l=t(51681),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(a,o);let d={children:["",{children:["[locale]",{children:["(default)",{children:["admin",{children:["translations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88136)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/admin/translations/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/admin/translations/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/admin/translations/page",pathname:"/[locale]/admin/translations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7330:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>o,TN:()=>d});var r=t(25323),s=t(18542),n=t(51238),i=t(91145);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef(({className:e,variant:a,...t},s)=>(0,r.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(l({variant:a}),e),...t}));o.displayName="Alert",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...a})).displayName="AlertTitle";let d=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...a}));d.displayName="AlertDescription"},9392:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>c,X9:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var r=t(25323);t(18542);var s=t(91145);function n({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...a})}function i({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...a})}function c({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...a})}function u({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12272:(e,a,t)=>{"use strict";t.d(a,{ModelTranslationManager:()=>r});let r=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call ModelTranslationManager() from the server but ModelTranslationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/admin/ModelTranslationManager.tsx","ModelTranslationManager")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26709:(e,a,t)=>{"use strict";t.d(a,{E:()=>o});var r=t(25323);t(18542);var s=t(48771),n=t(51238),i=t(91145);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:t=!1,...n}){let o=t?s.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(l({variant:a}),e),...n})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38846:(e,a,t)=>{"use strict";t.d(a,{B8:()=>R,Tabs:()=>b,TabsList:()=>y,TabsTrigger:()=>w,UC:()=>P,bL:()=>C,l9:()=>A});var r=t(18542),s=t(16760),n=t(20849),i=t(95783),l=t(78869),o=t(34757),d=t(2286),c=t(6772),u=t(13788),m=t(25323),p="Tabs",[x,f]=(0,n.A)(p,[i.RG]),g=(0,i.RG)(),[v,h]=x(p),b=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:s,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:x="automatic",...f}=e,g=(0,d.jH)(l),[h,b]=(0,c.i)({prop:r,onChange:s,defaultProp:n??"",caller:p});return(0,m.jsx)(v,{scope:t,baseId:(0,u.B)(),value:h,onValueChange:b,orientation:i,dir:g,activationMode:x,children:(0,m.jsx)(o.sG.div,{dir:g,"data-orientation":i,...f,ref:a})})});b.displayName=p;var j="TabsList",y=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...s}=e,n=h(j,t),l=g(t);return(0,m.jsx)(i.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:r,children:(0,m.jsx)(o.sG.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:a})})});y.displayName=j;var N="TabsTrigger",w=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:n=!1,...l}=e,d=h(N,t),c=g(t),u=T(d.baseId,r),p=_(d.baseId,r),x=r===d.value;return(0,m.jsx)(i.q7,{asChild:!0,...c,focusable:!n,active:x,children:(0,m.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":p,"data-state":x?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:a,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,s.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;x||n||!e||d.onValueChange(r)})})})});w.displayName=N;var k="TabsContent",M=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:s,forceMount:n,children:i,...d}=e,c=h(k,t),u=T(c.baseId,s),p=_(c.baseId,s),x=s===c.value,f=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(l.C,{present:n||x,children:({present:t})=>(0,m.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:p,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&i})})});function T(e,a){return`${e}-trigger-${a}`}function _(e,a){return`${e}-content-${a}`}M.displayName=k;var C=b,R=y,A=w,P=M},40829:(e,a,t)=>{Promise.resolve().then(t.bind(t,55398))},55398:(e,a,t)=>{"use strict";t.d(a,{ModelTranslationManager:()=>N});var r=t(25323),s=t(18542),n=t(9392),i=t(52510),l=t(164),o=t(99110),d=t(91624),c=t(26709),u=t(94670),m=t(7330),p=t(78108);let x=(0,p.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),f=(0,p.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var g=t(93408),v=t(56421);let h=(0,p.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var b=t(95234);function j(e,a=["en","zh"]){return e&&"object"==typeof e?a.filter(a=>!e[a]||""===e[a].trim()):a}let y=[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"zh",name:"中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"ko",name:"한국어",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"fr",name:"Fran\xe7ais",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"de",name:"Deutsch",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"es",name:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"it",name:"Italiano",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"pt",name:"Portugu\xeas",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"ru",name:"Русский",flag:"\uD83C\uDDF7\uD83C\uDDFA"}];function N({className:e}){let[a,t]=(0,s.useState)([]),[p,N]=(0,s.useState)([]),[w,k]=(0,s.useState)(null),[M,T]=(0,s.useState)({name:{},description:{}}),[_,C]=(0,s.useState)(!1),[R,A]=(0,s.useState)(!1),P=async()=>{C(!0);try{let e=await fetch("/api/ai/models?locale=en"),a=await e.json();0===a.code&&(t(a.data.models),I(a.data.models))}catch(e){b.oR.error("Failed to fetch models")}finally{C(!1)}},I=e=>{N(e.map(e=>{let a=e.model_name_i18n||{},t=e.description_i18n||{},r=y.map(e=>e.code),s=j(a,r),n=j(t,r),i=2*r.length,l=Math.round((i-(s.length+n.length))/i*100);return{modelId:e.model_id,modelName:e.model_name,missingTranslations:{name:s,description:n},completeness:l}}))},F=e=>{k(e),T({name:{...e.model_name_i18n||{}},description:{...e.description_i18n||{}}})},D=(e,a,t)=>{T(r=>({...r,[e]:{...r[e],[a]:t}}))},B=async()=>{if(w){A(!0);try{let e=await fetch("/api/admin/models/translations",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({modelId:w.model_id,translations:M})}),a=await e.json();0===a.code?(b.oR.success("Translations saved successfully"),P()):b.oR.error(a.msg||"Failed to save translations")}catch(e){b.oR.error("Failed to save translations")}finally{A(!1)}}};return(0,r.jsxs)("div",{className:`space-y-6 ${e}`,children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Models"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:a.length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Fully Translated"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:p.filter(e=>100===e.completeness).length})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{className:"pb-2",children:(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Needs Translation"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:p.filter(e=>e.completeness<100).length})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(x,{className:"w-5 h-5"}),"Models Translation Status"]}),(0,r.jsx)(n.BT,{children:"Click on a model to edit its translations"})]}),(0,r.jsx)(n.Wu,{className:"space-y-2",children:_?(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsx)(f,{className:"w-6 h-6 animate-spin"})}):p.map(e=>(0,r.jsx)("div",{className:`p-3 rounded-lg border cursor-pointer transition-colors ${w?.model_id===e.modelId?"border-primary bg-primary/5":"border-border hover:bg-muted/50"}`,onClick:()=>{let t=a.find(a=>a.model_id===e.modelId);t&&F(t)},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.modelName}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:e.modelId})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(c.E,{variant:100===e.completeness?"default":"secondary",children:[e.completeness,"%"]}),100===e.completeness?(0,r.jsx)(g.A,{className:"w-4 h-4 text-green-600"}):(0,r.jsx)(v.A,{className:"w-4 h-4 text-orange-600"})]})]})},e.modelId))})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Translation Editor"}),(0,r.jsx)(n.BT,{children:w?`Editing: ${w.model_name}`:"Select a model to edit translations"})]}),(0,r.jsx)(n.Wu,{children:w?(0,r.jsxs)(u.tU,{defaultValue:"name",className:"space-y-4",children:[(0,r.jsxs)(u.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(u.Xi,{value:"name",children:"Model Name"}),(0,r.jsx)(u.Xi,{value:"description",children:"Description"})]}),(0,r.jsx)(u.av,{value:"name",className:"space-y-4",children:y.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(o.J,{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.flag}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)(l.p,{value:M.name[e.code]||"",onChange:a=>D("name",e.code,a.target.value),placeholder:`Model name in ${e.name}`})]},e.code))}),(0,r.jsx)(u.av,{value:"description",className:"space-y-4",children:y.map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(o.J,{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.flag}),(0,r.jsx)("span",{children:e.name})]}),(0,r.jsx)(d.T,{value:M.description[e.code]||"",onChange:a=>D("description",e.code,a.target.value),placeholder:`Description in ${e.name}`,rows:3})]},e.code))}),(0,r.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,r.jsx)(i.$,{onClick:B,disabled:R,children:R?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f,{className:"w-4 h-4 mr-2 animate-spin"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h,{className:"w-4 h-4 mr-2"}),"Save Translations"]})}),(0,r.jsxs)(i.$,{variant:"outline",onClick:P,children:[(0,r.jsx)(f,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}):(0,r.jsxs)(m.Fc,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)(m.TN,{children:"Select a model from the list to start editing translations."})]})})]})]})]})}},56421:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(78108).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68890:(e,a,t)=>{"use strict";t.d(a,{b:()=>l});var r=t(18542),s=t(34757),n=t(25323),i=r.forwardRef((e,a)=>(0,n.jsx)(s.sG.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));i.displayName="Label";var l=i},88136:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i,metadata:()=>n});var r=t(96081),s=t(12272);let n={title:"Model Translations Management",description:"Manage multilingual content for AI models"};function i(){return(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Model Translations"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2",children:"Manage multilingual content for AI models. Add and edit translations for model names and descriptions."})]}),(0,r.jsx)(s.ModelTranslationManager,{})]})}},91624:(e,a,t)=>{"use strict";t.d(a,{T:()=>i});var r=t(25323),s=t(18542),n=t(91145);let i=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));i.displayName="Textarea"},93408:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(78108).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94670:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>o,av:()=>d,j7:()=>l,tU:()=>i});var r=t(25323);t(18542);var s=t(38846),n=t(91145);function i({className:e,...a}){return(0,r.jsx)(s.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",e),...a})}function l({className:e,...a}){return(0,r.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted/50 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-xl p-1 backdrop-blur-sm border border-border/30",e),...a})}function o({className:e,...a}){return(0,r.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-md focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-9 flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-background/50 data-[state=active]:scale-[1.02] [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function d({className:e,...a}){return(0,r.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",e),...a})}},96861:(e,a,t)=>{Promise.resolve().then(t.bind(t,12272))},99110:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var r=t(25323);t(18542);var s=t(68890),n=t(91145);function i({className:e,...a}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,6854,484],()=>t(5155));module.exports=r})();