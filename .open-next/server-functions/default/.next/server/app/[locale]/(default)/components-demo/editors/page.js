(()=>{var e={};e.id=8272,e.ids=[8272],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9392:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>a,wL:()=>u});var r=s(25323);s(18542);var n=s(91145);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22801:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(25323),n=s(37350),i=s(57871),a=s(26709),o=s(9392),l=s(2783),d=s(13872),c=s(18542);function u(){let[e,t]=(0,c.useState)(`
    <h2>Welcome to the Rich Text Editor</h2>
    <p>This is a powerful rich text editor built with <strong>TipTap</strong>. You can:</p>
    <ul>
      <li>Format text with <strong>bold</strong>, <em>italic</em>, and <u>underline</u></li>
      <li>Create lists and organize content</li>
      <li>Add links and images</li>
      <li>Use headings and paragraphs</li>
    </ul>
    <blockquote>
      <p>The editor supports many advanced features for content creation.</p>
    </blockquote>
    <p>Try editing this content to see the editor in action!</p>
  `),[s,u]=(0,c.useState)(`# Markdown Editor Demo

Welcome to the **Markdown Editor**! This editor provides a clean interface for writing in Markdown.

## Features

- **Live Preview**: See your formatted content as you type
- **Syntax Highlighting**: Code blocks are highlighted for better readability
- **Toolbar**: Quick access to common formatting options
- **Split View**: Edit and preview side by side

### Code Example

\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet("World"));
\`\`\`

### Lists and Links

1. **Ordered lists** work great
2. You can also create [links](https://example.com)
3. And add \`inline code\`

- Unordered lists too
- With multiple items
- Easy to create

> Blockquotes are perfect for highlighting important information or quotes.

### Tables

| Feature | Rich Text Editor | Markdown Editor |
|---------|------------------|-----------------|
| WYSIWYG | ✅ | ❌ |
| Markdown | ❌ | ✅ |
| Live Preview | ✅ | ✅ |
| Code Highlighting | ❌ | ✅ |

Try editing this content to explore the Markdown editor features!
`);return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(a.E,{variant:"outline",className:"mb-4",children:"Editor Components"}),(0,r.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Editor Components Demo"}),(0,r.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Powerful editing experiences with rich text and Markdown editors for content creation."})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)(l.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,r.jsx)(d.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiEditLine",className:"w-5 h-5"}),"Rich Text Editor (TipTap)"]}),(0,r.jsx)(o.BT,{children:"WYSIWYG editor with formatting toolbar, perfect for creating rich content with visual feedback."})]})})}),(0,r.jsx)("div",{className:"border-y bg-muted/30",children:(0,r.jsx)("div",{className:"container mx-auto py-16",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Rich Text Editor Example"}),(0,r.jsx)(o.BT,{children:"Edit the content below to see the rich text editor in action"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(n.A,{value:e,onChange:t})})]})})})})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,r.jsx)(o.Zp,{children:(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiMarkdownLine",className:"w-5 h-5"}),"Markdown Editor"]}),(0,r.jsx)(o.BT,{children:"Markdown editor with live preview, syntax highlighting, and split-view support."})]})})}),(0,r.jsx)("div",{className:"border-y",children:(0,r.jsx)("div",{className:"container mx-auto py-16",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Markdown Editor Example"}),(0,r.jsx)(o.BT,{children:"Write in Markdown and see the live preview on the right"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(i.A,{value:s,onChange:u})})]})})})})]}),(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiCompareLineIcon",className:"w-5 h-5"}),"Editor Features Comparison"]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiEditLine",className:"w-4 h-4"}),"Rich Text Editor (TipTap)"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,r.jsx)("li",{children:"WYSIWYG (What You See Is What You Get)"}),(0,r.jsx)("li",{children:"Visual formatting toolbar"}),(0,r.jsx)("li",{children:"Real-time formatting preview"}),(0,r.jsx)("li",{children:"Support for images, links, and media"}),(0,r.jsx)("li",{children:"Collaborative editing capabilities"}),(0,r.jsx)("li",{children:"Custom extensions and plugins"}),(0,r.jsx)("li",{children:"Accessibility features built-in"}),(0,r.jsx)("li",{children:"Mobile-friendly interface"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiMarkdownLine",className:"w-4 h-4"}),"Markdown Editor"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,r.jsx)("li",{children:"Markdown syntax support"}),(0,r.jsx)("li",{children:"Live preview with split view"}),(0,r.jsx)("li",{children:"Syntax highlighting for code blocks"}),(0,r.jsx)("li",{children:"Table editing support"}),(0,r.jsx)("li",{children:"Math equation rendering"}),(0,r.jsx)("li",{children:"Export to various formats"}),(0,r.jsx)("li",{children:"Keyboard shortcuts for efficiency"}),(0,r.jsx)("li",{children:"Version control friendly"})]})]})]})})]})})}),(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(d.default,{name:"RiCodeLine",className:"w-5 h-5"}),"Usage Examples"]})}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"Rich Text Editor Implementation:"}),(0,r.jsx)("code",{className:"text-sm bg-muted p-4 rounded block whitespace-pre",children:`import Editor from "@/components/blocks/editor";

function MyComponent() {
  const [content, setContent] = useState("");
  
  return (
    <Editor
      content={content}
      onChange={setContent}
      placeholder="Start writing..."
    />
  );
}`})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"Markdown Editor Implementation:"}),(0,r.jsx)("code",{className:"text-sm bg-muted p-4 rounded block whitespace-pre",children:`import MDEditor from "@/components/blocks/mdeditor";

function MyComponent() {
  const [markdown, setMarkdown] = useState("");
  
  return (
    <MDEditor
      value={markdown}
      onChange={setMarkdown}
      height={400}
    />
  );
}`})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3",children:"When to Use Each Editor:"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(o.Zp,{className:"p-4",children:[(0,r.jsx)("h5",{className:"font-medium mb-2",children:"Rich Text Editor"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Blog posts and articles"}),(0,r.jsx)("li",{children:"• Email composition"}),(0,r.jsx)("li",{children:"• Content management systems"}),(0,r.jsx)("li",{children:"• User-generated content"}),(0,r.jsx)("li",{children:"• Non-technical users"})]})]}),(0,r.jsxs)(o.Zp,{className:"p-4",children:[(0,r.jsx)("h5",{className:"font-medium mb-2",children:"Markdown Editor"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,r.jsx)("li",{children:"• Documentation"}),(0,r.jsx)("li",{children:"• README files"}),(0,r.jsx)("li",{children:"• Technical writing"}),(0,r.jsx)("li",{children:"• Code comments"}),(0,r.jsx)("li",{children:"• Developer-focused content"})]})]})]})]})]})]})})})]})}},25675:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/editors/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/editors/page.tsx","default")},26709:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(25323);s(18542);var n=s(48771),i=s(51238),a=s(91145);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?n.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...i})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37350:(e,t,s)=>{"use strict";s.d(t,{A:()=>B});var r=s(25323);s(71327);var n=s(68132),i=s(75848),a=s(19428),o=s(1343),l=s(34962);s(18542);var d=s(64633),c=s(45754),u=s(25407),h=s(40191),m=s(89378),x=s(16042),g=s(78824),p=s(12377),b=s(89502),j=s(69375),f=s(90364),v=s(72477),y=s(63598),N=s(93354),w=s(19219),k=s(86748),A=s(4300),C=s(89900),E=s(26551),M=s(98741),T=s(58259);function P(){let{editor:e}=(0,o.aA)();return e?(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 p-2 border-b mb-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBold().run(),disabled:!e.can().chain().focus().toggleBold().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("bold")?"bg-gray-100":""}`,title:"Bold",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleItalic().run(),disabled:!e.can().chain().focus().toggleItalic().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("italic")?"bg-gray-100":""}`,title:"Italic",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleStrike().run(),disabled:!e.can().chain().focus().toggleStrike().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("strike")?"bg-gray-100":""}`,title:"Strike",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCode().run(),disabled:!e.can().chain().focus().toggleCode().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("code")?"bg-gray-100":""}`,title:"Code",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().unsetAllMarks().run(),className:"p-2 rounded hover:bg-gray-100",title:"Clear marks",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setParagraph().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("paragraph")?"bg-gray-100":""}`,title:"Paragraph",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:1})?"bg-gray-100":""}`,title:"Heading 1",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:2})?"bg-gray-100":""}`,title:"Heading 2",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:3})?"bg-gray-100":""}`,title:"Heading 3",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:4}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:4})?"bg-gray-100":""}`,title:"Heading 4",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:5}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:5})?"bg-gray-100":""}`,title:"Heading 5",children:(0,r.jsx)(f.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:6}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:6})?"bg-gray-100":""}`,title:"Heading 6",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBulletList().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("bulletList")?"bg-gray-100":""}`,title:"Bullet list",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleOrderedList().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("orderedList")?"bg-gray-100":""}`,title:"Ordered list",children:(0,r.jsx)(N.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCodeBlock().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("codeBlock")?"bg-gray-100":""}`,title:"Code block",children:(0,r.jsx)(w.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBlockquote().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("blockquote")?"bg-gray-100":""}`,title:"Blockquote",children:(0,r.jsx)(k.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHorizontalRule().run(),className:"p-2 rounded hover:bg-gray-100",title:"Horizontal rule",children:(0,r.jsx)(A.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHardBreak().run(),className:"p-2 rounded hover:bg-gray-100",title:"Hard break",children:(0,r.jsx)(C.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().undo().run(),disabled:!e.can().chain().focus().undo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Undo",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().redo().run(),disabled:!e.can().chain().focus().redo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Redo",children:(0,r.jsx)(M.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setColor("#958DF1").run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("textStyle",{color:"#958DF1"})?"bg-gray-100":""}`,title:"Purple",children:(0,r.jsx)(T.A,{className:"w-4 h-4"})})]}):null}let R=[n.Q.configure({types:[a.A.name,i.A.name]}),a.A,l.A.configure({bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}})];function B({value:e,onChange:t}){return(0,r.jsx)("div",{className:"border rounded-md bg-background",children:(0,r.jsx)(o.zz,{slotBefore:(0,r.jsx)(P,{}),extensions:R,content:e,onUpdate:({editor:e})=>{t(e.getHTML())}})})}},57871:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(25323);s(18542);let n=(0,s(58976).default)(async()=>{},{loadableGenerated:{modules:["components/blocks/mdeditor/index.tsx -> @uiw/react-md-editor"]},ssr:!1});function i({value:e,onChange:t}){return(0,r.jsx)("div",{className:"w-full md:w-[800px]",children:(0,r.jsx)(n,{value:e,onChange:e=>t(e||""),height:600})})}},61603:(e,t,s)=>{Promise.resolve().then(s.bind(s,22801))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63475:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(94331),n=s(78740),i=s(87638),a=s.n(i),o=s(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["components-demo",{children:["editors",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25675)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/editors/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/editors/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(default)/components-demo/editors/page",pathname:"/[locale]/components-demo/editors",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},71327:()=>{},78635:(e,t,s)=>{Promise.resolve().then(s.bind(s,25675))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,8395,6854,484],()=>s(63475));module.exports=r})();