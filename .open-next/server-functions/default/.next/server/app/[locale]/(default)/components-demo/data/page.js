(()=>{var e={};e.id=7130,e.ids=[7130],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14309:(e,s,t)=>{"use strict";t.d(s,{a:()=>r});var a=t(18542);function r(){let[e,s]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),t=()=>{s(window.innerWidth<768)};return e.addEventListener("change",t),s(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26709:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(25323);t(18542);var r=t(48771),i=t(51238),n=t(91145);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,asChild:t=!1,...i}){let l=t?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),e),...i})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42751:(e,s,t)=>{Promise.resolve().then(t.bind(t,64601))},43825:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(94331),r=t(78740),i=t(87638),n=t.n(i),d=t(51681),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let o={children:["",{children:["[locale]",{children:["(default)",{children:["components-demo",{children:["data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64601)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/data/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/data/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(default)/components-demo/data/page",pathname:"/[locale]/components-demo/data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64601:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/data/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/components-demo/data/page.tsx","default")},79703:(e,s,t)=>{Promise.resolve().then(t.bind(t,95100))},95100:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(25323),r=t(26709),i=t(9392);function n({dataCards:e}){return(0,a.jsx)("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4",children:e.map((e,s)=>(0,a.jsxs)(i.Zp,{className:"@container/card",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.BT,{children:e.title}),(0,a.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:e.value}),(0,a.jsx)(i.X9,{children:e.label&&(0,a.jsx)(r.E,{variant:"outline",children:e.label})})]}),(0,a.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm",children:[(0,a.jsx)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:e.description}),(0,a.jsx)("div",{className:"text-muted-foreground",children:e.tip})]})]},s))})}var d=t(9901),l=t(2783),o=t(13872);function c(){return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(r.E,{variant:"outline",className:"mb-4",children:"Data Visualization Components"}),(0,a.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Data Visualization Components Demo"}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Display your data beautifully with cards and interactive charts that provide insights at a glance."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(l.N_,{href:"/components-demo",className:"inline-flex items-center text-primary hover:underline",children:[(0,a.jsx)(o.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Components Demo"]})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.default,{name:"RiDashboardLine",className:"w-5 h-5"}),"Data Cards Component"]}),(0,a.jsx)(i.BT,{children:"Display key metrics and KPIs in visually appealing cards with icons, values, and trend indicators."})]})})}),(0,a.jsx)("div",{className:"border-y bg-muted/30",children:(0,a.jsx)("div",{className:"container mx-auto py-16",children:(0,a.jsx)(n,{dataCards:[{title:"Total Revenue",value:"$45,231.89",label:"+20.1%",description:"from last month",tip:"Revenue generated this month",icon:"RiMoneyDollarCircleLine"},{title:"Active Users",value:"2,350",label:"+180.1%",description:"from last month",tip:"Users who logged in this month",icon:"RiUserLine"},{title:"Total Orders",value:"12,234",label:"+19%",description:"from last month",tip:"Orders placed this month",icon:"RiShoppingCartLine"},{title:"Conversion Rate",value:"3.2%",label:"+0.5%",description:"from last month",tip:"Percentage of visitors who made a purchase",icon:"RiLineChartLine"}]})})})]}),(0,a.jsxs)("section",{className:"mb-16",children:[(0,a.jsx)("div",{className:"container mx-auto px-4 mb-8",children:(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.default,{name:"RiLineChartLine",className:"w-5 h-5"}),"Data Charts Component"]}),(0,a.jsx)(i.BT,{children:"Interactive area charts with time range selection and category filtering for data visualization."})]})})}),(0,a.jsx)("div",{className:"border-y",children:(0,a.jsx)("div",{className:"container mx-auto py-16",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)(d.default,{title:"Website Traffic",description:"Showing total visitors for the last 30 days",data:[{date:"2024-01-01",desktop:222,mobile:150},{date:"2024-01-02",desktop:97,mobile:180},{date:"2024-01-03",desktop:167,mobile:120},{date:"2024-01-04",desktop:242,mobile:260},{date:"2024-01-05",desktop:373,mobile:290},{date:"2024-01-06",desktop:301,mobile:340},{date:"2024-01-07",desktop:245,mobile:180},{date:"2024-01-08",desktop:409,mobile:320},{date:"2024-01-09",desktop:59,mobile:110},{date:"2024-01-10",desktop:261,mobile:190},{date:"2024-01-11",desktop:327,mobile:350},{date:"2024-01-12",desktop:292,mobile:210},{date:"2024-01-13",desktop:342,mobile:380},{date:"2024-01-14",desktop:137,mobile:220},{date:"2024-01-15",desktop:120,mobile:170},{date:"2024-01-16",desktop:138,mobile:190},{date:"2024-01-17",desktop:446,mobile:360},{date:"2024-01-18",desktop:364,mobile:410},{date:"2024-01-19",desktop:243,mobile:180},{date:"2024-01-20",desktop:89,mobile:150},{date:"2024-01-21",desktop:137,mobile:200},{date:"2024-01-22",desktop:224,mobile:170},{date:"2024-01-23",desktop:138,mobile:230},{date:"2024-01-24",desktop:387,mobile:290},{date:"2024-01-25",desktop:215,mobile:250},{date:"2024-01-26",desktop:75,mobile:130},{date:"2024-01-27",desktop:383,mobile:420},{date:"2024-01-28",desktop:122,mobile:180},{date:"2024-01-29",desktop:315,mobile:240},{date:"2024-01-30",desktop:454,mobile:380}],fields:[{key:"desktop",label:"Desktop",color:"hsl(var(--chart-1))"},{key:"mobile",label:"Mobile",color:"hsl(var(--chart-2))"}],defaultTimeRange:"30d"})})})})]}),(0,a.jsx)("section",{className:"mb-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.default,{name:"RiInformationLine",className:"w-5 h-5"}),"Data Component Features"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Data Cards Features:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,a.jsx)("li",{children:"Responsive grid layout that adapts to screen size"}),(0,a.jsx)("li",{children:"Support for icons, values, labels, and descriptions"}),(0,a.jsx)("li",{children:"Trend indicators with percentage changes"}),(0,a.jsx)("li",{children:"Tooltips for additional context"}),(0,a.jsx)("li",{children:"Gradient backgrounds and shadow effects"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Data Charts Features:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,a.jsx)("li",{children:"Interactive area charts built with Recharts"}),(0,a.jsx)("li",{children:"Time range selection (30d, 7d, 24h)"}),(0,a.jsx)("li",{children:"Category filtering and toggling"}),(0,a.jsx)("li",{children:"Responsive design for mobile and desktop"}),(0,a.jsx)("li",{children:"Customizable colors and styling"}),(0,a.jsx)("li",{children:"Tooltip with detailed data on hover"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Usage Examples:"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<DataCards dataCards={metricsData} />"}),(0,a.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"<DataCharts data={chartData} config={chartConfig} />"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Data Structure:"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Data Cards expect an array of objects with:"}),(0,a.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"{ title, value, label, description, tip, icon }"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Charts expect time-series data with:"}),(0,a.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"{ date, [category1], [category2], ... }"})]})]})]})]})})}),(0,a.jsx)("section",{className:"mb-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.default,{name:"RiSpeedUpLine",className:"w-5 h-5"}),"Performance Metrics Example"]}),(0,a.jsx)(i.BT,{children:"Another example showing different types of metrics and data visualization."})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"pb-2",children:[(0,a.jsx)(i.BT,{children:"Page Load Time"}),(0,a.jsx)(i.ZB,{className:"text-2xl",children:"1.2s"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(r.E,{variant:"outline",className:"bg-green-50 border-green-200 text-green-800",children:"-15%"})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"pb-2",children:[(0,a.jsx)(i.BT,{children:"Bounce Rate"}),(0,a.jsx)(i.ZB,{className:"text-2xl",children:"23.1%"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(r.E,{variant:"outline",className:"bg-red-50 border-red-200 text-red-800",children:"+2.1%"})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"pb-2",children:[(0,a.jsx)(i.BT,{children:"API Response"}),(0,a.jsx)(i.ZB,{className:"text-2xl",children:"245ms"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(r.E,{variant:"outline",className:"bg-blue-50 border-blue-200 text-blue-800",children:"-8%"})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"pb-2",children:[(0,a.jsx)(i.BT,{children:"Uptime"}),(0,a.jsx)(i.ZB,{className:"text-2xl",children:"99.9%"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(r.E,{variant:"outline",className:"bg-green-50 border-green-200 text-green-800",children:"Stable"})})]})]})})]})})})]})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1179,3057,5474,9229,8530,9058,1098,8723,4626,3487,682,6854,484,9901],()=>t(43825));module.exports=a})();