(()=>{var e={};e.id=8730,e.ids=[8730],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4850:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(73636),a=r(47830),l=r(13266),n=(0,s.cache)(function(e,t){return function({_cache:e=(0,l.d)(),_formatters:t=(0,l.b)(e),getMessageFallback:r=l.f,messages:s,namespace:a,onError:n=l.g,...o}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,l.r)(t,"!"),(0,l.e)({...r,messages:e,namespace:t})}({...o,onError:n,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),o=(0,s.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,a.A)(r),t)})},7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(54209);function a(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,s.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=r(94331),a=r(78740),l=r(87638),n=r.n(l),o=r(51681),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let c={children:["",{children:["[locale]",{children:["(default)",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86441)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/posts/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/posts/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/posts/page",pathname:"/[locale]/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25160:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(73636);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:i,iconNode:c,...u},d)=>(0,s.createElement)("svg",{ref:d,...n,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",o),...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),i=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},i)=>(0,s.createElement)(o,{ref:i,iconNode:t,className:l(`lucide-${a(e)}`,r),...n}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35560:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(96081);let a=(0,r(25160).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function l({blog:e}){return e.disabled?null:(0,s.jsx)("section",{className:"w-full py-16",children:(0,s.jsxs)("div",{className:"container flex flex-col items-center gap-8 lg:px-16",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"mb-6 text-xs font-medium uppercase tracking-wider",children:e.label}),(0,s.jsx)("h2",{className:"mb-3 text-pretty text-3xl font-semibold md:mb-4 md:text-4xl lg:mb-6 lg:max-w-3xl lg:text-5xl",children:e.title}),(0,s.jsx)("p",{className:"mb-8 text-muted-foreground md:text-base lg:max-w-2xl lg:text-lg",children:e.description})]}),(0,s.jsx)("div",{className:"w-full flex flex-wrap items-start",children:e.items?.map((t,r)=>(0,s.jsx)("a",{href:t.url||`/${t.locale}/posts/${t.slug}`,target:t.target||"_self",className:"w-full md:w-1/3 p-4",children:(0,s.jsxs)("div",{className:"flex flex-col overflow-clip rounded-xl border border-border",children:[t.cover_url&&(0,s.jsx)("div",{children:(0,s.jsx)("img",{src:t.cover_url,alt:t.title||"",className:"aspect-16/9 h-full w-full object-cover object-center"})}),(0,s.jsxs)("div",{className:"px-4 py-4 md:px-4 md:py-4 lg:px-4 lg:py-4",children:[(0,s.jsx)("h3",{className:"mb-3 text-lg font-semibold md:mb-4 md:text-xl lg:mb-6",children:t.title}),(0,s.jsx)("p",{className:"mb-3 text-muted-foreground md:mb-4 lg:mb-6",children:t.description}),e.read_more_text&&(0,s.jsxs)("p",{className:"flex items-center hover:underline",children:[e.read_more_text,(0,s.jsx)(a,{className:"ml-2 size-4"})]})]})]})},r))})]})})}},36277:(e,t,r)=>{Promise.resolve().then(r.bind(r,76735))},41167:(e,t,r)=>{"use strict";r.d(t,{St:()=>i,W5:()=>l,aV:()=>o,gg:()=>n,gx:()=>d,j7:()=>u,nz:()=>a,zX:()=>c});var s=r(7279),a=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function l(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").insert(e);if(a)throw a;return r}async function n(e,t){let r=(0,s.A)(),{data:a,error:l}=await r.from("posts").update(t).eq("uuid",e);if(l)throw l;return a}async function o(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!a)return r}async function i(e,t){let r=(0,s.A)(),{data:a,error:l}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!l)return a}async function c(e=1,t=50){let r=(0,s.A)(),{data:a,error:l}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return l?[]:a}async function u(e,t=1,r=50){let a=(0,s.A)(),{data:l,error:n}=await a.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return n?[]:l}async function d(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},45590:()=>{},52367:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78133:(e,t,r)=>{Promise.resolve().then(r.bind(r,93497))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,generateMetadata:()=>o});var s=r(96081),a=r(35560),l=r(41167),n=r(4850);async function o({params:e}){let{locale:t}=await e,r=await (0,n.A)(),s="http://localhost:3000/posts";return"en"!==t&&(s=`http://localhost:3000/${t}/posts`),{title:r("blog.title"),description:r("blog.description"),alternates:{canonical:s}}}async function i({params:e}){let{locale:t}=await e,r=await (0,n.A)(),o=await (0,l.j7)(t),i={title:r("blog.title"),description:r("blog.description"),items:o,read_more_text:r("blog.read_more_text")};return(0,s.jsx)(a.A,{blog:i})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,1098,8723,4626,3487,6854,484],()=>r(19079));module.exports=s})();