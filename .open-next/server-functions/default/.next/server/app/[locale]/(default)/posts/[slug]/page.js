(()=>{var e={};e.id=9482,e.ids=[9482],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4514:()=>{},7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(54209);function a(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,s.UU)(e,t)}},9392:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,X9:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>u});var s=r(25323);r(18542);var a=r(91145);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11849:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,generateMetadata:()=>l});var s=r(96081),a=r(41167),n=r(35065),o=r(72160);async function l({params:e}){let{locale:t,slug:r}=await e,s=await (0,a.St)(r,t),n=`http://localhost:3000/posts/${r}`;return"en"!==t&&(n=`http://localhost:3000/${t}/posts/${r}`),{title:s?.title,description:s?.description,alternates:{canonical:n}}}async function i({params:e}){let{locale:t,slug:r}=await e,l=await (0,a.St)(r,t);return l&&l.status===a.nz.Online?(0,s.jsx)(n.default,{post:l}):(0,s.jsx)(o.A,{message:"Post not found"})}},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24900:(e,t,r)=>{Promise.resolve().then(r.bind(r,35065))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35065:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/blocks/blog-detail/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/blocks/blog-detail/index.tsx","default")},41167:(e,t,r)=>{"use strict";r.d(t,{St:()=>i,W5:()=>n,aV:()=>l,gg:()=>o,gx:()=>u,j7:()=>d,nz:()=>a,zX:()=>c});var s=r(7279),a=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function n(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").insert(e);if(a)throw a;return r}async function o(e,t){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").update(t).eq("uuid",e);if(n)throw n;return a}async function l(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!a)return r}async function i(e,t){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!n)return a}async function c(e=1,t=50){let r=(0,s.A)(),{data:a,error:n}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return n?[]:a}async function d(e,t=1,r=50){let a=(0,s.A)(),{data:n,error:o}=await a.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return o?[]:n}async function u(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},45590:()=>{},52367:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61700:(e,t,r)=>{Promise.resolve().then(r.bind(r,64244))},62389:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(94331),a=r(78740),n=r(87638),o=r.n(n),l=r(51681),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c={children:["",{children:["[locale]",{children:["(default)",{children:["posts",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11849)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/posts/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,13950)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/(default)/posts/[slug]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/posts/[slug]/page",pathname:"/[locale]/posts/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64244:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var s=r(25323),a=r(42109);r(18542);var n=r(48771),o=r(656),l=r(91145);function i({...e}){return(0,s.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function c({className:e,...t}){return(0,s.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,l.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function d({className:e,...t}){return(0,s.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,l.cn)("inline-flex items-center gap-1.5",e),...t})}function u({asChild:e,className:t,...r}){let a=e?n.DX:"a";return(0,s.jsx)(a,{"data-slot":"breadcrumb-link",className:(0,l.cn)("hover:text-foreground transition-colors",t),...r})}function p({className:e,...t}){return(0,s.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("text-foreground font-normal",e),...t})}function x({children:e,className:t,...r}){return(0,s.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,s.jsx)(o.A,{})})}var m=r(98760),f=r(50533);function h({post:e}){let t=(0,f.c3)();return(0,s.jsx)(i,{children:(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)(u,{href:"en"===e.locale?"/":`/${e.locale}`,children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})}),(0,s.jsx)(x,{}),(0,s.jsx)(d,{children:(0,s.jsx)(u,{href:"en"===e.locale?"/posts":`/${e.locale}/posts`,children:t("blog.title")})}),(0,s.jsx)(x,{}),(0,s.jsx)(d,{children:(0,s.jsx)(p,{children:e.title})})]})})}r(4514);let g=(0,r(58976).default)(async()=>{},{loadableGenerated:{modules:["components/markdown/index.tsx -> @uiw/react-md-editor"]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"markdown bg-background",children:(0,s.jsx)("p",{children:"加载中..."})})});function j({content:e}){return(0,s.jsx)(g,{className:"markdown bg-background",source:e,components:{a:({children:e,...t})=>(0,s.jsx)("a",{...t,target:"_blank",rel:"noopener noreferrer",children:e})}})}var b=r(90200),v=r(9392);function w({post:e}){return(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsx)(h,{post:e}),(0,s.jsx)("h1",{className:"mb-7 mt-9 max-w-3xl text-2xl font-bold md:mb-10 md:text-4xl",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center gap-3 text-sm md:text-base bg-background",children:[e.author_avatar_url&&(0,s.jsx)(a.Avatar,{className:"h-8 w-8 border",children:(0,s.jsx)(a.AvatarImage,{src:e.author_avatar_url,alt:e.author_name})}),(0,s.jsxs)("div",{children:[e.author_name&&(0,s.jsx)("span",{className:"font-medium",children:e.author_name}),(0,s.jsxs)("span",{className:"ml-2 text-muted-foreground",children:["on ",e.created_at&&(0,b.m)(new Date(e.created_at),{addSuffix:!0})]})]})]}),(0,s.jsxs)("div",{className:"relative py-8 grid max-w-(--breakpoint-xl) gap-4 lg:mt-0 lg:grid lg:grid-cols-12 lg:gap-6",children:[e.content&&(0,s.jsx)(v.Zp,{className:"order-2 lg:order-none lg:col-span-8 px-4",children:(0,s.jsx)(j,{content:e.content})}),(0,s.jsx)("div",{className:"order-1 flex h-fit flex-col text-sm lg:sticky lg:top-8 lg:order-none lg:col-span-3 lg:col-start-10 lg:text-xs"})]})]})})}},72160:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(96081);function a({message:e}){return(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,s.jsx)("p",{children:e})})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,9229,8530,9058,1098,8723,4626,3487,141,6854,484],()=>r(62389));module.exports=s})();