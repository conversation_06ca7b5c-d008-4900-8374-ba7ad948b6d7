(()=>{var e={};e.id=4867,e.ids=[4867],e.modules={164:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(25323);t(18542);var a=t(91145);function n({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},9392:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var s=t(25323);t(18542);var a=t(91145);function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}function u({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17993:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(94331),a=t(78740),n=t(87638),i=t.n(n),o=t(51681),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47780)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71697)),"/Users/<USER>/My-Project/fluxkrea/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,68818,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,27539,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,7784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea/app/[locale]/auth/signin/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/auth/signin/page",pathname:"/[locale]/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28636:(e,r,t)=>{Promise.resolve().then(t.bind(t,61313))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43947:(e,r,t)=>{"use strict";t.d(r,{default:()=>p});var s=t(25323),a=t(18542),n=t(9392),i=t(52122),o=t(8894),l=t(52510);t(164),t(99110);var d=t(91145),c=t(68560),u=t(50533);function p({className:e,...r}){let t=(0,u.c3)(),[p,x]=a.useState(!1),[f,m]=a.useState(!1),g=async()=>{x(!0);try{await (0,c.Jv)("google")}catch(e){console.error("Google登录失败:",e),x(!1)}};return(0,s.jsxs)("div",{className:(0,d.cn)("flex flex-col gap-6",e),...r,children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{className:"text-center",children:[(0,s.jsx)(n.ZB,{className:"text-xl",children:t("sign_modal.sign_in_title")}),(0,s.jsx)(n.BT,{children:t("sign_modal.sign_in_description")})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(l.$,{variant:"outline",className:"w-full",onClick:g,disabled:p||f,children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"w-4 h-4 animate-spin mr-2"}),t("sign_modal.google_signing_in")]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.UKz,{className:"w-4 h-4 mr-2"}),t("sign_modal.google_sign_in")]})}),!1]}),!1]})})]}),(0,s.jsxs)("div",{className:"text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary  ",children:["By clicking continue, you agree to our"," ",(0,s.jsx)("a",{href:"/terms-of-service",target:"_blank",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"/privacy-policy",target:"_blank",children:"Privacy Policy"}),"."]})]})}},47780:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(96081),a=t(61313),n=t(11793),i=t(17984);async function o({searchParams:e}){let{callbackUrl:r}=await e;return await (0,n.j2)()?(0,i.redirect)(r||"/"):(0,s.jsx)("div",{className:"flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10",children:(0,s.jsxs)("div",{className:"flex w-full max-w-sm flex-col gap-6",children:[(0,s.jsxs)("a",{href:"/",className:"flex items-center gap-2 self-center font-medium",children:[(0,s.jsx)("div",{className:"flex h-6 w-6 items-center justify-center rounded-md border text-primary-foreground",children:(0,s.jsx)("img",{src:"/logo.png",alt:"logo",className:"size-4"})}),"KREA FLUX"]}),(0,s.jsx)(a.default,{})]})})}},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},61313:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(20263).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea/components/sign/form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea/components/sign/form.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68890:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var s=t(18542),a=t(34757),n=t(25323),i=s.forwardRef((e,r)=>(0,n.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86780:(e,r,t)=>{Promise.resolve().then(t.bind(t,43947))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99110:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var s=t(25323);t(18542);var a=t(68890),n=t(91145);function i({className:e,...r}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,9229,8530,9058,5397,6854,7125],()=>t(17993));module.exports=s})();