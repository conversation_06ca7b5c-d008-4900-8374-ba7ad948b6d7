(()=>{var e={};e.id=3966,e.ids=[3966],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},53879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(73675),u=t(78740),n=t(91179),o=t(30354),a=t(58246),p=t(53908);async function c(e){try{let{message:r}=await e.json();if(!r)return(0,a.YS)("invalid params");let t=await (0,p.TG)();if(!t)return(0,a.YS)("no auth");return await (0,o.nX)({user_uuid:t,trans_type:o.H3.Ping,credits:o.rN.PingCost}),(0,a.DQ)({pong:`received message: ${r}`})}catch(e){return console.log("test failed:",e),(0,a.YS)("test failed")}}let d=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/ping/route",pathname:"/api/ping",filename:"route",bundlePath:"app/api/ping/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ping/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:f}=d;function q(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,r,t)=>{"use strict";function s(e){return n(0,"ok",e||[])}function i(){return n(0,"ok")}function u(e){return n(-1,e)}function n(e,r,t){let s={code:e,message:r,data:t};return t&&(s.data=t),Response.json(s)}t.d(r,{DQ:()=>s,YS:()=>u,j7:()=>n,rn:()=>i})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,r,t)=>{"use strict";e.exports=t(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,7125],()=>t(53879));module.exports=s})();