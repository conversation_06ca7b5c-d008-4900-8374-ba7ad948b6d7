(()=>{var e={};e.id=9127,e.ids=[9127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,r,t)=>{"use strict";function s(e){return o(0,"ok",e||[])}function i(){return o(0,"ok")}function u(e){return o(-1,e)}function o(e,r,t){let s={code:e,message:r,data:t};return t&&(s.data=t),Response.json(s)}t.d(r,{DQ:()=>s,YS:()=>u,j7:()=>o,rn:()=>i})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68138:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>x,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>d});var i=t(73675),u=t(78740),o=t(91179),n=t(58246),a=t(33102),p=t(53908),c=t(30354);async function d(e){try{let e=await (0,p.TG)();if(!e)return(0,n.j7)(-2,"no auth");let r=await (0,a.pX)(e);if(!r)return(0,n.YS)("user not exist");return r.credits=await (0,c.mP)(e),(0,n.DQ)(r)}catch(e){return console.log("get user info failed: ",e),(0,n.YS)("get user info failed")}}let x=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/get-user-info/route",pathname:"/api/get-user-info",filename:"route",bundlePath:"app/api/get-user-info/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/get-user-info/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:q}=x;function g(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},73675:(e,r,t)=>{"use strict";e.exports=t(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,7125],()=>t(68138));module.exports=s})();