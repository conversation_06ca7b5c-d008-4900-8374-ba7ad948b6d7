(()=>{var e={};e.id=1736,e.ids=[1736],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,t,r)=>{"use strict";function s(e){return u(0,"ok",e||[])}function n(){return u(0,"ok")}function a(e){return u(-1,e)}function u(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>a,j7:()=>u,rn:()=>n})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,t,r)=>{"use strict";e.exports=r(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91095:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>l});var n=r(73675),a=r(78740),u=r(91179),i=r(58246),o=r(53908),c=r(94685),d=r(67332);async function l(e){try{let t=new URL(e.url),r=parseInt(t.searchParams.get("page")||"1"),s=parseInt(t.searchParams.get("limit")||"20"),n=t.searchParams.get("type"),a=await (0,o.TG)();if(!a)return(0,i.j7)(-2,"Authentication required");switch(n){case"usage":let u=await (0,c.oY)(a,r,s);return(0,i.DQ)({records:u,page:r,limit:s,total:u.length});case"credits":let l=await (0,d._3)(a,r,s);return(0,i.DQ)({records:l,page:r,limit:s,total:l?.length||0});case"stats":let p=await (0,c.c9)(a);return(0,i.DQ)({stats:p,summary:_(p)});default:let[m,f,g]=await Promise.all([(0,c.oY)(a,1,10),(0,d._3)(a,1,10),(0,c.c9)(a)]);return(0,i.DQ)({recent_usage:m,recent_credits:f,stats:g,summary:_(g)})}}catch(e){return console.error("Failed to fetch usage data:",e),(0,i.YS)("Failed to fetch usage data")}}function _(e){if(!e||0===e.length)return{total_usage_count:0,total_credits_consumed:0,most_used_model:null,success_rate:0,model_breakdown:[]};let t=e.reduce((e,t)=>e+(t.usage_count||0),0),r=e.reduce((e,t)=>e+(t.total_credits_consumed||0),0),s=e.reduce((e,t)=>e+(t.success_count||0),0),n=e.reduce((e,t)=>(e.usage_count||0)>(t.usage_count||0)?e:t);return{total_usage_count:t,total_credits_consumed:r,most_used_model:n&&n.model_id?{model_id:n.model_id,model_name:n.model_name,usage_count:n.usage_count}:null,success_rate:Math.round(100*(t>0?s/t*100:0))/100,model_breakdown:e.map(e=>({model_name:e.model_name||"",model_type:e.model_type||"",usage_count:e.usage_count||0,credits_consumed:e.total_credits_consumed||0}))}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai/usage/route",pathname:"/api/ai/usage",filename:"route",bundlePath:"app/api/ai/usage/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/usage/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:g}=p;function x(){return(0,u.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},91645:e=>{"use strict";e.exports=require("net")},94685:(e,t,r)=>{"use strict";r.d(t,{lC:()=>d,b:()=>l,om:()=>o,zQ:()=>c,kl:()=>p,Kp:()=>i,nv:()=>u,oY:()=>m,c9:()=>f,it:()=>_});var s=r(7279);function n(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let s=Object.values(e).filter(Boolean);if(s.length>0)return s[0]}return""}function a(e,t="en"){return{...e,model_name:n(e.model_name_i18n||e.model_name,t),description:n(e.description_i18n||e.description,t)}}async function u(e="en"){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(n)throw n;return(r||[]).map(t=>a(t,e))}async function i(e,t="en"){let r=(0,s.A)(),{data:n,error:u}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(u)throw u;return(n||[]).map(e=>a(e,t))}async function o(e,t="en"){let r=(0,s.A)(),{data:n,error:u}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return u?null:n?a(n,t):null}async function c(e,t="en"){let r=(0,s.A)(),{data:n,error:u}=await r.from("ai_models").select("*").eq("model_id",e).single();return u?null:n?a(n,t):null}async function d(e,t,r){let n=(0,s.A)(),{data:a,error:u}=await n.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(u)throw u;return a||1}async function l(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_model_usage").insert(e).select().single();if(n)throw n;return r}async function _(e,t){let r=(0,s.A)(),{data:n,error:a}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return a?null:n}async function p(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return n?null:r}async function m(e,t=1,r=50){let n=(0,s.A)(),{data:a,error:u}=await n.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(u)throw u;return a||[]}async function f(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(n)throw n;return r||[]}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,5397,7125],()=>r(91095));module.exports=s})();