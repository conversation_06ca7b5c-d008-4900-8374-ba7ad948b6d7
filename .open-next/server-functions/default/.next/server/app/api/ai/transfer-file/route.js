(()=>{var e={};e.id=2509,e.ids=[2509],e.modules={7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(54209);function s(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,i.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14584:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>n,ZK:()=>a,f1:()=>o});var i=r(25031),s=r(75184);function n(){return(0,s.A)()}function o(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",i=t.length;for(let s=0;s<e;s++)r+=t[Math.floor(Math.random()*i)];return r}function a(){return new i.F({workerId:1}).NextId().toString()}},21820:e=>{"use strict";e.exports=require("os")},25031:(e,t,r)=>{"use strict";t.F=void 0;let i=r(74698);Object.defineProperty(t,"F",{enumerable:!0,get:function(){return i.SnowflakeIdv1}})},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45590:()=>{},49241:(e,t,r)=>{"use strict";r.d(t,{XW:()=>a,fh:()=>o,xO:()=>u,yy:()=>n});var i=r(63363),s=r(14584);async function n(e,t="image",r){try{console.log(`[File Transfer] Starting transfer from: ${e}`);let n=r||(0,s.YJ)(),o=function(e){try{return new URL(e).pathname.split(".").pop()||"bin"}catch(e){return"bin"}}(e),a={png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",webp:"image/webp",gif:"image/gif",bmp:"image/bmp",svg:"image/svg+xml",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",webm:"video/webm",mkv:"video/x-matroska"}[o.toLowerCase()]||"application/octet-stream",u=process.env.STORAGE_BUCKET||"shipany-test",l=`ai-generated/${t}/${n}.${o}`;console.log(`[File Transfer] Target bucket: ${u}, key: ${l}, Content-Type: ${a}`);let c=(0,i.m)(),d=await c.downloadAndUpload({url:e,key:l,bucket:u,contentType:a,disposition:"inline"});return console.log("[File Transfer] Transfer successful:",d),{success:!0,url:d.url,key:d.key}}catch(e){return console.error("[File Transfer] Transfer failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function o(e,t="image",r){let i=[];for(let s=0;s<e.length;s++){let o=e[s],a=r?e.length>1?`${r}_${s}`:r:void 0,u=await n(o,t,a);i.push({originalUrl:o,...u})}return i}function a(e){let t=[];return e.url&&t.push(e.url),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&t.push(e.url)}),e.data&&e.data.url&&t.push(e.data.url),t}function u(e,t){let r=JSON.parse(JSON.stringify(e));return r.url&&t[r.url]&&(r.url=t[r.url]),r.results&&Array.isArray(r.results)&&r.results.forEach(e=>{e.url&&t[e.url]&&(e.url=t[e.url])}),r.data&&r.data.url&&t[r.data.url]&&(r.data.url=t[r.data.url]),r}},52367:()=>{},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},58246:(e,t,r)=>{"use strict";function i(e){return o(0,"ok",e||[])}function s(){return o(0,"ok")}function n(e){return o(-1,e)}function o(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>n,j7:()=>o,rn:()=>s})},59354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>p,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{POST:()=>c});var s=r(73675),n=r(78740),o=r(91179),a=r(58246),u=r(49241),l=r(94685);async function c(e){try{let t,{url:r,urls:i,request_id:s,model_type:n}=await e.json();if(console.log(`[Transfer API] Request - url: ${r}, urls: ${i}, request_id: ${s}, model_type: ${n}`),r){console.log(`[Transfer API] Transferring single file: ${r}`);let e=await (0,u.yy)(r,n||"image");t=[{originalUrl:r,...e}]}else if(i&&Array.isArray(i))console.log("[Transfer API] Transferring multiple files:",i),t=await (0,u.fh)(i,n||"image");else{if(!s)return(0,a.YS)("Missing required parameter: url, urls, or request_id");console.log(`[Transfer API] Transferring files from request: ${s}`);let e=await (0,l.kl)(s);if(!e)return(0,a.YS)("Request not found");if(!e.response_data)return(0,a.YS)("No response data found");let r=(0,u.XW)(e.response_data);if(console.log("[Transfer API] Extracted URLs from response:",r),0===r.length)return(0,a.YS)("No URLs found in response data");let i=e.model_id?.includes("veo")?"video":"image",n=(t=await (0,u.fh)(r,i,s)).filter(e=>e.success);if(n.length>0){let t={};n.forEach(e=>{e.url&&(t[e.originalUrl]=e.url)});let r=JSON.parse(JSON.stringify(e.response_data));r.url&&t[r.url]&&(r.url=t[r.url]),r.results&&Array.isArray(r.results)&&r.results.forEach(e=>{e.url&&t[e.url]&&(e.url=t[e.url])}),await (0,l.it)(s,{response_data:r}),console.log("[Transfer API] Updated database record with new URLs")}}let o=t.filter(e=>e.success).length,c=t.length-o;return console.log(`[Transfer API] Transfer completed - Success: ${o}, Failed: ${c}`),(0,a.DQ)({results:t,summary:{total:t.length,success:o,failed:c}})}catch(e){return console.error("[Transfer API] Request processing failed:",e),(0,a.YS)("File transfer failed")}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/transfer-file/route",pathname:"/api/ai/transfer-file",filename:"route",bundlePath:"app/api/ai/transfer-file/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/transfer-file/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=d;function p(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63363:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var i=r(91043),s=r(17492);function n(e){return new o(e)}class o{constructor(e){this.s3=new i.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:t,contentType:r,bucket:i,onProgress:n,disposition:o="inline"}){if(i||(i=process.env.STORAGE_BUCKET||""),!i)throw Error("Bucket is required");let a=new s._({client:this.s3,params:{Bucket:i,Key:t,Body:e,ContentDisposition:o,...r&&{ContentType:r}}});n&&a.on("httpUploadProgress",e=>{n((e.loaded||0)/(e.total||1)*100)});let u=await a.done();return{location:u.Location,bucket:u.Bucket,key:u.Key,filename:u.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${i}/${u.Key}`:u.Location}}async downloadAndUpload({url:e,key:t,bucket:r,contentType:i,disposition:s="inline"}){let n=await fetch(e);if(!n.ok)throw Error(`HTTP error! status: ${n.status}`);if(!n.body)throw Error("No body in response");let o=await n.arrayBuffer(),a=Buffer.from(o);return this.uploadFile({body:a,key:t,bucket:r,contentType:i,disposition:s})}}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74698:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SnowflakeIdv1=void 0;class r{constructor(e){if(void 0===e.workerId)throw Error("lost WorkerId");(!e.baseTime||e.baseTime<0)&&(e.baseTime=15778368e5),(!e.workerIdBitLength||e.workerIdBitLength<0)&&(e.workerIdBitLength=6),(!e.seqBitLength||e.seqBitLength<0)&&(e.seqBitLength=6),(void 0==e.maxSeqNumber||e.maxSeqNumber<=0)&&(e.maxSeqNumber=63),(void 0==e.minSeqNumber||e.minSeqNumber<0)&&(e.minSeqNumber=5),(void 0==e.topOverCostCount||e.topOverCostCount<0)&&(e.topOverCostCount=2e3),2!==e.method?e.method=1:e.method=2,this.Method=BigInt(e.method),this.BaseTime=BigInt(e.baseTime),this.WorkerId=BigInt(e.workerId),this.WorkerIdBitLength=BigInt(e.workerIdBitLength),this.SeqBitLength=BigInt(e.seqBitLength),this.MaxSeqNumber=BigInt(e.maxSeqNumber),this.MinSeqNumber=BigInt(e.minSeqNumber),this.TopOverCostCount=BigInt(e.topOverCostCount);let t=this.WorkerIdBitLength+this.SeqBitLength,r=this.MinSeqNumber;this._TimestampShift=t,this._CurrentSeqNumber=r,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(e){}EndOverCostAction(e){}BeginTurnBackAction(e){}EndTurnBackAction(e){}NextOverCostId(){let e=this.GetCurrentTimeTick();return e>this._LastTimeTick?(this.EndOverCostAction(e),this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(e),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let e=this.GetCurrentTimeTick();return e<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),e>this._LastTimeTick)?(this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(e),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,t}CalcTurnBackId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,t}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let e=this.GetCurrentTimeTick();for(;e<=this._LastTimeTick;)e=this.GetCurrentTimeTick();return e}NextNumber(){if(this._IsOverCost){let e=this.NextOverCostId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}{let e=this.NextNormalId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}}NextId(){if(this._IsOverCost){let e=this.NextOverCostId();return e>=9007199254740992n?e:parseInt(e.toString())}{let e=this.NextNormalId();return e>=9007199254740992n?e:parseInt(e.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}t.SnowflakeIdv1=r},75184:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(55511);let s={randomUUID:i.randomUUID},n=new Uint8Array(256),o=n.length,a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(s.randomUUID&&!t&&!e)return s.randomUUID();let u=(e=e||{}).random??e.rng?.()??(o>n.length-16&&((0,i.randomFillSync)(n),o=0),n.slice(o,o+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(u)}},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},91645:e=>{"use strict";e.exports=require("net")},94685:(e,t,r)=>{"use strict";r.d(t,{lC:()=>c,b:()=>d,om:()=>u,zQ:()=>l,kl:()=>h,Kp:()=>a,nv:()=>o,oY:()=>f,c9:()=>p,it:()=>m});var i=r(7279);function s(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let i=Object.values(e).filter(Boolean);if(i.length>0)return i[0]}return""}function n(e,t="en"){return{...e,model_name:s(e.model_name_i18n||e.model_name,t),description:s(e.description_i18n||e.description,t)}}async function o(e="en"){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(s)throw s;return(r||[]).map(t=>n(t,e))}async function a(e,t="en"){let r=(0,i.A)(),{data:s,error:o}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(o)throw o;return(s||[]).map(e=>n(e,t))}async function u(e,t="en"){let r=(0,i.A)(),{data:s,error:o}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return o?null:s?n(s,t):null}async function l(e,t="en"){let r=(0,i.A)(),{data:s,error:o}=await r.from("ai_models").select("*").eq("model_id",e).single();return o?null:s?n(s,t):null}async function c(e,t,r){let s=(0,i.A)(),{data:n,error:o}=await s.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(o)throw o;return n||1}async function d(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").insert(e).select().single();if(s)throw s;return r}async function m(e,t){let r=(0,i.A)(),{data:s,error:n}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return n?null:s}async function h(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return s?null:r}async function f(e,t=1,r=50){let s=(0,i.A)(),{data:n,error:o}=await s.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(o)throw o;return n||[]}async function p(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(s)throw s;return r||[]}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[1179,4209,964],()=>r(59354));module.exports=i})();