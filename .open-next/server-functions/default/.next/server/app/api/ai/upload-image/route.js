(()=>{var e={};e.id=7436,e.ids=[7436],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(73675),i=t(78740),a=t(91179),n=t(58246),u=t(53908),p=t(63363);async function c(e){try{let r=await (0,u.TG)();if(!r)return(0,n.YS)("Authentication required");console.log(`[Image Upload] User ${r} uploading image`);let t=(await e.formData()).get("file");if(!t)return(0,n.YS)("No file provided");if(console.log(`[Image Upload] File info: ${t.name}, size: ${t.size}, type: ${t.type}`),!t.type.startsWith("image/"))return(0,n.YS)("Only image files are allowed");if(t.size>0xa00000)return(0,n.YS)("File size too large. Maximum 10MB allowed");let s=Date.now(),o=Math.random().toString(36).substring(2,15),i=t.name.split(".").pop()||"jpg",a=`temp/ai-uploads/${r}/${s}-${o}.${i}`;try{let e=await t.arrayBuffer(),r=Buffer.from(e),s=(0,p.m)(),o=await s.uploadFile({body:r,key:a,contentType:t.type,disposition:"inline"});return console.log("[Image Upload] Upload successful:",o),(0,n.DQ)({url:o.url,filename:o.filename,key:o.key,size:t.size,type:t.type})}catch(e){return console.error("Upload failed:",e),(0,n.YS)("Failed to upload image")}}catch(e){return console.error("Image upload error:",e),(0,n.YS)("Image upload failed")}}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/upload-image/route",pathname:"/api/ai/upload-image",filename:"route",bundlePath:"app/api/ai/upload-image/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/upload-image/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:f,serverHooks:x}=l;function y(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:f})}},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,r,t)=>{"use strict";function s(e){return a(0,"ok",e||[])}function o(){return a(0,"ok")}function i(e){return a(-1,e)}function a(e,r,t){let s={code:e,message:r,data:t};return t&&(s.data=t),Response.json(s)}t.d(r,{DQ:()=>s,YS:()=>i,j7:()=>a,rn:()=>o})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63363:(e,r,t)=>{"use strict";t.d(r,{m:()=>i});var s=t(91043),o=t(17492);function i(e){return new a(e)}class a{constructor(e){this.s3=new s.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:r,contentType:t,bucket:s,onProgress:i,disposition:a="inline"}){if(s||(s=process.env.STORAGE_BUCKET||""),!s)throw Error("Bucket is required");let n=new o._({client:this.s3,params:{Bucket:s,Key:r,Body:e,ContentDisposition:a,...t&&{ContentType:t}}});i&&n.on("httpUploadProgress",e=>{i((e.loaded||0)/(e.total||1)*100)});let u=await n.done();return{location:u.Location,bucket:u.Bucket,key:u.Key,filename:u.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${s}/${u.Key}`:u.Location}}async downloadAndUpload({url:e,key:r,bucket:t,contentType:s,disposition:o="inline"}){let i=await fetch(e);if(!i.ok)throw Error(`HTTP error! status: ${i.status}`);if(!i.body)throw Error("No body in response");let a=await i.arrayBuffer(),n=Buffer.from(a);return this.uploadFile({body:n,key:r,bucket:t,contentType:s,disposition:o})}}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,964,7125],()=>t(46032));module.exports=s})();