(()=>{var e={};e.id=6898,e.ids=[6898],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5653:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>I,routeModule:()=>v,serverHooks:()=>P,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{POST:()=>R});var s=r(73675),i=r(78740),a=r(91179),n=r(58246),l=r(53908),u=r(56605),c=r(14584),p=r(88661),d=r(94685),g=r(46852),f=r(49241);class m{constructor(){this.replicateProvider=new g.N}async processRequest(e,t){let r=(0,c.YJ)();console.log(`[Replicate AI Service] Processing request for user: ${e}, model: ${t.model}`);let o=await (0,d.om)(t.model);if(!o)throw Error(`Model ${t.model} not found`);if("replicate"!==o.provider)throw Error(`Model ${t.model} is not a Replicate model`);let s=await this.estimateCost(t,o);console.log(`[Replicate AI Service] Estimated cost: ${s} credits`),await (0,d.b)({user_uuid:e,model_id:t.model,request_id:r,credits_consumed:s,status:"pending",request_params:t,started_at:(0,p.iq)()}),console.log(`[Replicate AI Service] Created usage record: ${r}`);try{let e;if("image"===t.type)e=await this.handleImageGeneration(t,r);else throw Error(`Unsupported request type: ${t.type} for Replicate provider`);return"success"===e.status&&(await (0,d.it)(r,{status:"success",output_size:this.calculateOutputSize(e),response_data:e,completed_at:(0,p.iq)()}),console.log("[Replicate AI Service] Updated usage record to success")),e}catch(e){throw console.error("[Replicate AI Service] Request failed:",e),await (0,d.it)(r,{status:"failed",error_reason:"error",error_detail:e instanceof Error?e.message:"Unknown error",completed_at:(0,p.iq)()}),e}}async handleImageGeneration(e,t){console.log(`[Replicate AI Service] Starting image generation for model: ${e.model}`);let r=await (0,d.om)(e.model);if(!r)throw Error(`Model ${e.model} not found`);let o={model:e.model,prompt:e.prompt,options:{aspect_ratio:e.options?.aspectRatio,num_outputs:e.options?.variants||1,output_format:e.options?.output_format||"webp",output_quality:e.options?.output_quality||90,guidance:e.options?.guidance,num_inference_steps:e.options?.num_inference_steps,prompt_strength:e.options?.prompt_strength,seed:e.options?.seed,disable_safety_checker:e.options?.disable_safety_checker,go_fast:e.options?.go_fast,megapixels:e.options?.megapixels,image:e.options?.uploadedImages?.[0]}};console.log("[Replicate AI Service] Calling Replicate API with request:",o);let s=await this.replicateProvider.generateImage(o);if(console.log("[Replicate AI Service] Replicate response:",s),"failed"===s.status)throw Error(s.error||"Replicate API failed");if("success"===s.status&&s.urls){console.log(`[Replicate AI Service] Transferring ${s.urls.length} files to our storage`);let o=await (0,f.fh)(s.urls,"image",t),i=o.filter(e=>e.success),a=o.filter(e=>!e.success);if(a.length>0&&console.warn("[Replicate AI Service] Some file transfers failed:",a),0===i.length)throw Error("All file transfers failed");let n=i.map(e=>({url:e.url,width:1024,height:1024}));return console.log(`[Replicate AI Service] Successfully transferred ${n.length} images`),{id:t,type:"image",status:"success",result:{images:n},usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}}return"pending"===s.status?{id:t,type:"image",status:"pending",usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}:{id:t,type:"image",status:"failed",error:{reason:"error",detail:"No images returned from Replicate API"},usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}}async estimateCost(e,t){if("image"!==e.type)return t.credits_per_unit;{let r=e.options?.variants||1;return t.credits_per_unit*r}}calculateOutputSize(e){return e.result?.images?e.result.images.length:0}isModelSupported(e){return this.replicateProvider.isModelSupported(e)}getSupportedModels(){return this.replicateProvider.getSupportedModels()}}new m;var _=r(30354);async function R(e){try{let t,r,{model:o,type:s,prompt:i,options:a}=await e.json();if(!o||!s||!i)return(0,n.YS)("Missing required parameters: model, type, prompt");let c=await (0,l.TG)();if(!c)return(0,n.j7)(-2,"Authentication required");let p=await (0,d.om)(o);if(p||(p=await (0,d.zQ)(o)),!p)return(0,n.YS)(`Model ${o} not found`);let g={model:o,type:s,prompt:i,options:{...a,uploadedImages:a?.uploadedImages||[],referenceImages:a?.referenceImages||[],firstFrameUrl:a?.firstFrameUrl}};try{switch(s){case"text":let e=function(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.ceil(t+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}(i),r=a?.max_tokens||1e3;t=await (0,d.lC)(o,e,r);break;case"image":let n=a?.variants||1;t=p.credits_per_unit*n;break;default:t=p.credits_per_unit}}catch(e){return(0,n.YS)("Failed to calculate cost")}if(!await (0,_.ll)(c,t))return(0,n.j7)(-3,"Insufficient credits",{required_credits:t,error_code:"INSUFFICIENT_CREDITS"});"replicate"===p.provider?(console.log(`[AI Generate] Using Replicate provider for model: ${o}`),r=new m):(console.log(`[AI Generate] Using GRSAI provider for model: ${o}`),r=new u.U);try{let e=await r.processRequest(c,g);if("text"===s&&a?.stream)return new Response(JSON.stringify(e),{headers:{"Content-Type":"application/json"}});return"success"===e.status&&await (0,_.Ty)({user_uuid:c,model_id:o,request_id:e.id,credits:e.usage?.credits_consumed||t}),(0,n.DQ)(e)}catch(t){console.error("AI generation failed:",t);let e=t instanceof Error?t.message:"Unknown error";return(0,n.YS)(`AI generation failed: ${e}`)}}catch(e){return console.error("Request processing failed:",e),(0,n.YS)("Request processing failed")}}let v=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/generate/route",pathname:"/api/ai/generate",filename:"route",bundlePath:"app/api/ai/generate/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/generate/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:P}=v;function I(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29345:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=29345,e.exports=t},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46852:(e,t,r)=>{"use strict";r.d(t,{N:()=>a,X:()=>n});var o=r(25698),s=r.n(o),i=r(14584);class a{constructor(){let e=process.env.REPLICATE_API_TOKEN;if(!e)throw Error("REPLICATE_API_TOKEN environment variable is required");this.client=new(s())({auth:e})}async generateImage(e){let t=(0,i.YJ)();try{console.log("[Replicate Provider] ========== STARTING IMAGE GENERATION =========="),console.log(`[Replicate Provider] Request ID: ${t}`),console.log("[Replicate Provider] Full request:",JSON.stringify(e,null,2));let r={prompt:e.prompt};e.options?.aspect_ratio&&(r.aspect_ratio=e.options.aspect_ratio),e.options?.image&&(r.image=e.options.image),e.options?.prompt_strength!==void 0&&(r.prompt_strength=e.options.prompt_strength),e.options?.num_outputs!==void 0&&(r.num_outputs=e.options.num_outputs),e.options?.num_inference_steps!==void 0&&(r.num_inference_steps=e.options.num_inference_steps),e.options?.guidance!==void 0&&(r.guidance=e.options.guidance),e.options?.seed!==void 0&&(r.seed=e.options.seed),e.options?.output_format&&(r.output_format=e.options.output_format),e.options?.output_quality!==void 0&&(r.output_quality=e.options.output_quality),e.options?.disable_safety_checker!==void 0&&(r.disable_safety_checker=e.options.disable_safety_checker),e.options?.go_fast!==void 0&&(r.go_fast=e.options.go_fast),e.options?.megapixels&&(r.megapixels=e.options.megapixels),console.log("[Replicate Provider] ========== CALLING REPLICATE API =========="),console.log(`[Replicate Provider] Model: ${e.model}`),console.log("[Replicate Provider] Input parameters:",JSON.stringify(r,null,2));let o=await this.client.run(e.model,{input:r});if(console.log("[Replicate Provider] ========== REPLICATE API RESPONSE =========="),console.log("[Replicate Provider] Raw output type:",typeof o),console.log("[Replicate Provider] Raw output:",JSON.stringify(o,null,2)),console.log("[Replicate Provider] ========== PROCESSING OUTPUT =========="),console.log("[Replicate Provider] Output is array:",Array.isArray(o)),console.log("[Replicate Provider] Output length:",Array.isArray(o)?o.length:"N/A"),Array.isArray(o)&&o.length>0){console.log("[Replicate Provider] Processing array output...");let r=o.map((e,t)=>{if(console.log(`[Replicate Provider] Item ${t}:`,typeof e,e),"string"==typeof e)return console.log(`[Replicate Provider] Item ${t} is string URL:`,e),e;if(e&&"object"==typeof e){if("function"==typeof e.url){let r=e.url();return console.log(`[Replicate Provider] Item ${t} has url() method, result:`,r),r.toString()}else if("url"in e&&"string"==typeof e.url)return console.log(`[Replicate Provider] Item ${t} is object with URL property:`,e.url),e.url}return console.log(`[Replicate Provider] Item ${t} is invalid, skipping`),null}).filter(Boolean);console.log("[Replicate Provider] Extracted URLs:",r);let s={id:t,status:"success",urls:r,usage:{credits_consumed:this.calculateCredits(e.model)}};return console.log("[Replicate Provider] ========== FINAL SUCCESS RESULT =========="),console.log("[Replicate Provider] Result:",JSON.stringify(s,null,2)),s}throw console.log("[Replicate Provider] ========== OUTPUT FORMAT ERROR =========="),console.log("[Replicate Provider] Expected array with length > 0, got:",typeof o,o),Error(`Invalid output format from Replicate API. Expected array, got: ${typeof o}`)}catch(r){console.log("[Replicate Provider] ========== ERROR OCCURRED =========="),console.error("[Replicate Provider] Error type:",typeof r),console.error("[Replicate Provider] Error message:",r instanceof Error?r.message:"Unknown error"),console.error("[Replicate Provider] Full error:",r),console.error("[Replicate Provider] Error stack:",r instanceof Error?r.stack:"No stack");let e={id:t,status:"failed",error:r instanceof Error?r.message:"Unknown error",usage:{credits_consumed:0}};return console.log("[Replicate Provider] ========== FINAL ERROR RESULT =========="),console.log("[Replicate Provider] Error result:",JSON.stringify(e,null,2)),e}}calculateCredits(e){return"black-forest-labs/flux-krea-dev"===e?25:20}isModelSupported(e){return["black-forest-labs/flux-krea-dev"].includes(e)}getSupportedModels(){return["black-forest-labs/flux-krea-dev"]}}let n=new a},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1179,3057,5474,4209,5397,964,5698,7125,9686],()=>r(5653));module.exports=o})();