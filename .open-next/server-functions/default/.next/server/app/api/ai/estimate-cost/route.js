(()=>{var e={};e.id=8885,e.ids=[8885],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29589:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{POST:()=>d});var s=r(73675),n=r(78740),a=r(91179),o=r(58246),u=r(53908),c=r(94685),l=r(30354);async function d(e){try{let t,{model:r,type:i,prompt:s,options:n}=await e.json();if(!r||!i)return(0,o.YS)("Missing required parameters: model, type");let a=await (0,u.TG)();if(!a)return(0,o.j7)(-2,"Authentication required");let d=await (0,c.om)(r);if(!d)return(0,o.YS)(`Model ${r} not found or inactive`);let p=await (0,l.mP)(a),_={};try{switch(i){case"text":let e=s?function(e){if(!e)return 0;let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.ceil(t+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}(s):0,a=n?.max_tokens||1e3;t=await (0,c.lC)(r,e,a),_={input_tokens:e,estimated_output_tokens:a,total_tokens:e+a,credits_per_1k_tokens:d.credits_per_unit,calculation:`(${e} + ${a}) / 1000 * ${d.credits_per_unit}`};break;case"image":let o=n?.variants||1;t=d.credits_per_unit*o,_={base_cost:d.credits_per_unit,variants:o,calculation:`${d.credits_per_unit} * ${o}`};break;case"video":t=d.credits_per_unit,_={base_cost:d.credits_per_unit,calculation:`${d.credits_per_unit} (fixed cost per video)`};break;default:t=d.credits_per_unit,_={base_cost:d.credits_per_unit}}}catch(e){return(0,o.YS)("Failed to calculate cost")}let f=p.left_credits>=t,m=f?0:t-p.left_credits;return(0,o.DQ)({model:{id:d.model_id,name:d.model_name,type:d.model_type,provider:d.provider},cost_estimate:{estimated_credits:t,breakdown:_,unit_type:d.unit_type},user_credits:{available:p.left_credits,can_afford:f,shortfall:m,is_pro:p.is_pro||!1},recommendations:function(e,t,r){let i=[];return r.left_credits<t&&i.push("积分不足，建议充值或选择成本更低的模型"),"text"===e.model_type?t>10&&i.push("对于简单对话，可以考虑使用 gemini-2.5-flash-lite 以节省积分"):"image"===e.model_type&&t>60&&i.push("对于一般图像生成，可以考虑使用 flux-pro-1.1 以节省积分"),r.left_credits<100&&i.push("积分余额较低，建议及时充值以避免服务中断"),!r.is_pro&&r.left_credits<=10&&i.push("新用户建议先尝试低成本模型熟悉功能"),i}(d,t,p)})}catch(e){return console.error("Cost estimation failed:",e),(0,o.YS)("Cost estimation failed")}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/estimate-cost/route",pathname:"/api/ai/estimate-cost",filename:"route",bundlePath:"app/api/ai/estimate-cost/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/estimate-cost/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:_,workUnitAsyncStorage:f,serverHooks:m}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:f})}},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>i.T});var i=r(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,t,r)=>{"use strict";function i(e){return a(0,"ok",e||[])}function s(){return a(0,"ok")}function n(e){return a(-1,e)}function a(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>n,j7:()=>a,rn:()=>s})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,t,r)=>{"use strict";e.exports=r(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94685:(e,t,r)=>{"use strict";r.d(t,{lC:()=>l,b:()=>d,om:()=>u,zQ:()=>c,kl:()=>_,Kp:()=>o,nv:()=>a,oY:()=>f,c9:()=>m,it:()=>p});var i=r(7279);function s(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let i=Object.values(e).filter(Boolean);if(i.length>0)return i[0]}return""}function n(e,t="en"){return{...e,model_name:s(e.model_name_i18n||e.model_name,t),description:s(e.description_i18n||e.description,t)}}async function a(e="en"){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(s)throw s;return(r||[]).map(t=>n(t,e))}async function o(e,t="en"){let r=(0,i.A)(),{data:s,error:a}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(a)throw a;return(s||[]).map(e=>n(e,t))}async function u(e,t="en"){let r=(0,i.A)(),{data:s,error:a}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return a?null:s?n(s,t):null}async function c(e,t="en"){let r=(0,i.A)(),{data:s,error:a}=await r.from("ai_models").select("*").eq("model_id",e).single();return a?null:s?n(s,t):null}async function l(e,t,r){let s=(0,i.A)(),{data:n,error:a}=await s.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(a)throw a;return n||1}async function d(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").insert(e).select().single();if(s)throw s;return r}async function p(e,t){let r=(0,i.A)(),{data:s,error:n}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return n?null:s}async function _(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return s?null:r}async function f(e,t=1,r=50){let s=(0,i.A)(),{data:n,error:a}=await s.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(a)throw a;return n||[]}async function m(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(s)throw s;return r||[]}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[1179,3057,5474,4209,5397,7125],()=>r(29589));module.exports=i})();