"use strict";(()=>{var e={};e.id=6384,e.ids=[6384],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},57936:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>I,routeModule:()=>_,serverHooks:()=>A,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{POST:()=>g});var o=t(73675),a=t(78740),u=t(91179),i=t(58246),n=t(53908),l=t(56605),d=t(94685),c=t(88661),p=t(49241),f=t(30354);async function g(e){try{let r,{request_id:t,task_id:s}=await e.json();if(console.log(`[Result API] Received request - request_id: ${t}, task_id: ${s}`),!t&&!s)return(0,i.YS)("Missing required parameter: request_id or task_id");let o=await (0,n.TG)();if(!o)return(0,i.j7)(-2,"Authentication required");let a=s;if(t){if(console.log(`[Result API] Looking up usage record for request_id: ${t}`),!(r=await (0,d.kl)(t)))return console.log(`[Result API] No usage record found for request_id: ${t}`),(0,i.YS)("Request not found");if(console.log("[Result API] Found usage record:",r),r.user_uuid!==o)return(0,i.YS)("Access denied");r.response_data&&r.response_data.data&&r.response_data.data.id?(a=r.response_data.data.id,console.log(`[Result API] Extracted GRSAI task ID from usage: ${a}`)):r.response_data&&r.response_data.id&&(a=r.response_data.id,console.log(`[Result API] Extracted GRSAI task ID from usage (direct): ${a}`))}if(!a&&t&&(console.log(`[Result API] Using request_id as task_id: ${t}`),a=t),!a)return console.log("[Result API] No task ID available"),(0,i.YS)("Task ID not found");try{console.log(`[Result API] Querying GRSAI result for task ID: ${a}`);let e=new l.g,u=await e.getResult(a);if(console.log("[Result API] GRSAI result response:",u),0!==u.code)return console.log(`[Result API] GRSAI API error: code=${u.code}, msg=${u.msg}`),(0,i.YS)(`GRSAI API error: ${u.msg}`);let n=u.data;if(console.log("[Result API] Task data:",n),!r&&s){console.log("[Result API] Direct task query without usage record");let e=m(s),r={id:s,type:e,status:q(n.status),progress:n.progress,result:x(n,e),error:"failed"===n.status?{reason:n.failure_reason||"error",detail:n.error||"Unknown error"}:void 0};return(0,i.DQ)(r)}let g=r?m(r.model_id):m(a),_={id:t||a,type:g,status:q(n.status),progress:n.progress,result:x(n,g),error:"failed"===n.status?{reason:n.failure_reason||"error",detail:n.error||"Unknown error"}:void 0};if("success"===_.status&&n.url){if(console.log("[Result API] Task completed successfully, starting credit deduction and file transfer"),r&&"success"!==r.status)try{await (0,f.Ty)({user_uuid:o,model_id:r.model_id,request_id:t,credits:r.credits_consumed}),console.log(`[Result API] Credits deducted: ${r.credits_consumed} for request ${t}`)}catch(e){console.error("[Result API] Failed to deduct credits:",e)}try{let e=(0,p.XW)(n);if(console.log("[Result API] URLs to transfer:",e),e.length>0){let s=r?.model_id?.includes("veo")?"video":"image",o=await (0,p.fh)(e,s,t);console.log("[Result API] Transfer results:",o);let a=o.filter(e=>e.success);if(a.length>0){let e={};a.forEach(r=>{r.url&&(e[r.originalUrl]=r.url)});let s=(0,p.xO)(n,e);console.log("[Result API] Updated URLs in response data"),_.result=x(s,g),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...s},completed_at:(0,c.iq)()})}else console.log("[Result API] File transfer failed, using original URLs"),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...n},completed_at:(0,c.iq)()})}}catch(e){console.error("[Result API] File transfer error:",e),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...n},completed_at:(0,c.iq)()})}}else if(r&&r.status!==_.status){if("failed"===_.status&&_.error?.reason==="error"&&"failed"!==r.status)try{await (0,f.jL)({user_uuid:o,model_id:r.model_id,request_id:t,credits:r.credits_consumed,original_trans_no:`${t}_deduction`}),console.log(`[Result API] Credits refunded: ${r.credits_consumed} for failed request ${t}`)}catch(e){console.error("[Result API] Failed to refund credits:",e)}await (0,d.it)(t,{status:"success"===_.status?"success":"failed"===_.status?"failed":"pending",response_data:{...r.response_data,...n},completed_at:"success"===_.status||"failed"===_.status?(0,c.iq)():void 0})}return(0,i.DQ)(_)}catch(e){return console.error("Failed to query GRSAI result:",e),(0,i.YS)("Failed to query result")}}catch(e){return console.error("Request processing failed:",e),(0,i.YS)("Request processing failed")}}function m(e){return e.includes("veo")?"video":e.includes("flux")||e.includes("image")||e.includes("sora")||e.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)?"image":"text"}function q(e){switch(e){case"succeeded":return"success";case"failed":return"failed";case"running":return"running";default:return"pending"}}function x(e,r){if("succeeded"!==e.status||!e.url)return;let t=function(e){try{let r=new URL(e).pathname.toLowerCase().split(".").pop();if(r&&["png","jpg","jpeg","webp","gif","bmp","svg","tiff","ico"].includes(r))return"image";if(r&&["mp4","avi","mov","wmv","flv","webm","mkv","m4v","3gp"].includes(r))return"video"}catch(e){}return"unknown"}(e.url),s=!1;if("video"===r||"image"!==r&&"multimodal"!==r&&"video"===t)return{video:{url:e.url}};{let r=[],t=new Set;return e.url&&!t.has(e.url)&&(r.push({url:e.url,width:e.width||1024,height:e.height||1024}),t.add(e.url)),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&!t.has(e.url)&&(r.push({url:e.url,width:e.width||1024,height:e.height||1024}),t.add(e.url))}),{images:r}}}let _=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai/result/route",pathname:"/api/ai/result",filename:"route",bundlePath:"app/api/ai/result/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/result/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:R,serverHooks:A}=_;function I(){return(0,u.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:R})}},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{e.exports=require("http2")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91043:e=>{e.exports=require("@aws-sdk/client-s3")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,964,7125,9686],()=>t(57936));module.exports=s})();