(()=>{var e={};e.id=837,e.ids=[837],e.modules={7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(54209);function n(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,s.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var s={};r.r(s),r.d(s,{GET:()=>c});var n=r(73675),i=r(78740),o=r(91179),u=r(58246),a=r(94685);async function c(e){try{let t,r=new URL(e.url),s=r.searchParams.get("type"),n=r.searchParams.get("locale")||"en",i=(t=s?await (0,a.Kp)(s,n):await (0,a.nv)(n)).reduce((e,t)=>(e[t.model_type]||(e[t.model_type]=[]),e[t.model_type].push(t),e),{});return(0,u.DQ)({models:t,grouped:i,total:t.length})}catch(e){return console.error("Failed to fetch models:",e),(0,u.YS)("Failed to fetch models")}}let l=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/models/route",pathname:"/api/ai/models",filename:"route",bundlePath:"app/api/ai/models/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/ai/models/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:_}=l;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45590:()=>{},52367:()=>{},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58246:(e,t,r)=>{"use strict";function s(e){return o(0,"ok",e||[])}function n(){return o(0,"ok")}function i(e){return o(-1,e)}function o(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>i,j7:()=>o,rn:()=>n})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,t,r)=>{"use strict";e.exports=r(44870)},74075:e=>{"use strict";e.exports=require("zlib")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94685:(e,t,r)=>{"use strict";r.d(t,{lC:()=>l,b:()=>d,om:()=>a,zQ:()=>c,kl:()=>_,Kp:()=>u,nv:()=>o,oY:()=>m,c9:()=>f,it:()=>p});var s=r(7279);function n(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let s=Object.values(e).filter(Boolean);if(s.length>0)return s[0]}return""}function i(e,t="en"){return{...e,model_name:n(e.model_name_i18n||e.model_name,t),description:n(e.description_i18n||e.description,t)}}async function o(e="en"){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(n)throw n;return(r||[]).map(t=>i(t,e))}async function u(e,t="en"){let r=(0,s.A)(),{data:n,error:o}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(o)throw o;return(n||[]).map(e=>i(e,t))}async function a(e,t="en"){let r=(0,s.A)(),{data:n,error:o}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return o?null:n?i(n,t):null}async function c(e,t="en"){let r=(0,s.A)(),{data:n,error:o}=await r.from("ai_models").select("*").eq("model_id",e).single();return o?null:n?i(n,t):null}async function l(e,t,r){let n=(0,s.A)(),{data:i,error:o}=await n.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(o)throw o;return i||1}async function d(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_model_usage").insert(e).select().single();if(n)throw n;return r}async function p(e,t){let r=(0,s.A)(),{data:n,error:i}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return i?null:n}async function _(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return n?null:r}async function m(e,t=1,r=50){let n=(0,s.A)(),{data:i,error:o}=await n.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(o)throw o;return i||[]}async function f(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(n)throw n;return r||[]}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,4209],()=>r(24393));module.exports=s})();