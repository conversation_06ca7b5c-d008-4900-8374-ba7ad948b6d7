(()=>{var e={};e.id=301,e.ids=[301],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,r,t)=>{"use strict";function s(e){return i(0,"ok",e||[])}function a(){return i(0,"ok")}function u(e){return i(-1,e)}function i(e,r,t){let s={code:e,message:r,data:t};return t&&(s.data=t),Response.json(s)}t.d(r,{DQ:()=>s,YS:()=>u,j7:()=>i,rn:()=>a})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69354:(e,r,t)=>{"use strict";t.d(r,{$0:()=>u,NW:()=>i,lU:()=>n});var s=t(7279),a=t(33102);async function u(e){let r=(0,s.A)(),{data:t,error:a}=await r.from("feedbacks").insert(e);if(a)throw a;return t}async function i(e=1,r=50){e<1&&(e=1),r<=0&&(r=50);let t=(e-1)*r,u=(0,s.A)(),{data:n,error:o}=await u.from("feedbacks").select("*").order("created_at",{ascending:!1}).range(t,t+r-1);if(o||!n||0===n.length)return[];let c=Array.from(new Set(n.map(e=>e.user_uuid))),d=await (0,a.QZ)(c);return n.map(e=>{let r=d.find(r=>r.uuid===e.user_uuid);return{...e,user:r}})}async function n(){let e=(0,s.A)(),{data:r,error:t}=await e.from("feedbacks").select("count",{count:"exact"});if(!t)return r[0].count}},73675:(e,r,t)=>{"use strict";e.exports=t(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99465:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>f,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>p});var a=t(73675),u=t(78740),i=t(91179),n=t(58246),o=t(88661),c=t(53908),d=t(69354);async function p(e){try{let{content:r,rating:t}=await e.json();if(!r)return(0,n.YS)("invalid params");let s={user_uuid:await (0,c.TG)(),content:r,rating:t||5,created_at:(0,o.iq)(),status:"created"};return await (0,d.$0)(s),(0,n.DQ)(s)}catch(e){return console.log("add feedback failed",e),(0,n.YS)("add feedback failed")}}let f=new a.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/add-feedback/route",pathname:"/api/add-feedback",filename:"route",bundlePath:"app/api/add-feedback/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/add-feedback/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:q}=f;function k(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,7125],()=>t(99465));module.exports=s})();