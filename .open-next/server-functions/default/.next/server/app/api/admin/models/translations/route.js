(()=>{var e={};e.id=6427,e.ids=[6427],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48263:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c,PUT:()=>l});var o=t(73675),i=t(78740),n=t(91179),a=t(58246),u=t(7279),d=t(53908);async function l(e){try{if(!await (0,d.TG)())return(0,a.YS)("Authentication required");let{modelId:r,translations:t}=await e.json();if(!r||!t)return(0,a.YS)("Missing required parameters: modelId, translations");let s=(0,u.A)(),{data:o,error:i}=await s.from("ai_models").update({model_name_i18n:t.name,description_i18n:t.description,updated_at:new Date().toISOString()}).eq("model_id",r).select().single();if(i)return console.error("Failed to update model translations:",i),(0,a.YS)("Failed to update translations");return(0,a.DQ)({model:o,message:"Translations updated successfully"})}catch(e){return console.error("Translation update error:",e),(0,a.YS)("Internal server error")}}async function c(e){try{let r,t,s=new URL(e.url).searchParams.get("modelId"),o=(0,u.A)().from("ai_models").select("model_id, model_name, model_name_i18n, description, description_i18n, model_type, provider");if(s?{data:r,error:t}=await o.eq("model_id",s).single():{data:r,error:t}=await o.eq("is_active",!0).order("model_type").order("model_name"),t)return console.error("Failed to fetch model translations:",t),(0,a.YS)("Failed to fetch translations");return(0,a.DQ)({models:s?[r]:r,total:s?1:r.length})}catch(e){return console.error("Translation fetch error:",e),(0,a.YS)("Internal server error")}}let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/models/translations/route",pathname:"/api/admin/models/translations",filename:"route",bundlePath:"app/api/admin/models/translations/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/admin/models/translations/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=p;function q(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},51931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=t(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,r,t)=>{"use strict";function s(e){return n(0,"ok",e||[])}function o(){return n(0,"ok")}function i(e){return n(-1,e)}function n(e,r,t){let s={code:e,message:r,data:t};return t&&(s.data=t),Response.json(s)}t.d(r,{DQ:()=>s,YS:()=>i,j7:()=>n,rn:()=>o})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,r,t)=>{"use strict";e.exports=t(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1179,3057,5474,4209,5397,7125],()=>t(48263));module.exports=s})();