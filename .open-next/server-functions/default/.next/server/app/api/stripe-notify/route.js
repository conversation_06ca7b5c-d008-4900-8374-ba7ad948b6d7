"use strict";(()=>{var e={};e.id=3977,e.ids=[3977],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},24073:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var o={};t.r(o),t.d(o,{POST:()=>d});var s=t(73675),n=t(78740),i=t(91179),p=t(69595),a=t(24608),u=t(58246);async function d(e){try{let r=process.env.STRIPE_PRIVATE_KEY,t=process.env.STRIPE_WEBHOOK_SECRET;if(!r||!t)throw Error("invalid stripe config");let o=new p.A(r),s=e.headers.get("stripe-signature"),n=await e.text();if(!s||!n)throw Error("invalid notify data");let i=await o.webhooks.constructEventAsync(n,s,t);if(console.log("stripe notify event: ",i),"checkout.session.completed"===i.type){let e=i.data.object;await (0,a.J)(e)}else console.log("not handle event: ",i.type);return(0,u.rn)()}catch(e){return console.log("stripe notify failed: ",e),Response.json({error:`handle stripe notify failed: ${e.message}`},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe-notify/route",pathname:"/api/stripe-notify",filename:"route",bundlePath:"app/api/stripe-notify/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/stripe-notify/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:f}=c;function y(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},58246:(e,r,t)=>{function o(e){return i(0,"ok",e||[])}function s(){return i(0,"ok")}function n(e){return i(-1,e)}function i(e,r,t){let o={code:e,message:r,data:t};return t&&(o.data=t),Response.json(o)}t.d(r,{DQ:()=>o,YS:()=>n,j7:()=>i,rn:()=>s})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73675:(e,r,t)=>{e.exports=t(44870)},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1179,4209,9595,4305],()=>t(24073));module.exports=o})();