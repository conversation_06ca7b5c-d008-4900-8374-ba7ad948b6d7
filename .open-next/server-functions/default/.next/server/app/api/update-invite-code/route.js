(()=>{var e={};e.id=5441,e.ids=[5441],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,t,r)=>{"use strict";function s(e){return n(0,"ok",e||[])}function i(){return n(0,"ok")}function u(e){return n(-1,e)}function n(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>u,j7:()=>n,rn:()=>i})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67691:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{POST:()=>c});var i=r(73675),u=r(78740),n=r(91179),o=r(33102),a=r(58246),p=r(53908);async function c(e){try{let{invite_code:t}=await e.json();if(!t)return(0,a.YS)("invalid params");if(t.length<2||t.length>16)return(0,a.YS)("invalid invite code, length must be between 2 and 16");let r=await (0,p.TG)();if(!r)return(0,a.YS)("no auth");let s=await (0,o.pX)(r);if(!s||!s.email)return(0,a.YS)("invalid user");if(s.invite_code===t)return(0,a.DQ)(s);let i=await (0,o.y_)(t);if(i){if(i.uuid!==r)return(0,a.YS)("invite code already exists");return(0,a.DQ)(i)}return await (0,o.XQ)(r,t),s.invite_code=t,(0,a.DQ)(s)}catch(e){return console.log("update invite code failed",e),(0,a.YS)("update invite code failed")}}let d=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/update-invite-code/route",pathname:"/api/update-invite-code",filename:"route",bundlePath:"app/api/update-invite-code/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/update-invite-code/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:v}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},73675:(e,t,r)=>{"use strict";e.exports=r(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,5397,7125],()=>r(67691));module.exports=s})();