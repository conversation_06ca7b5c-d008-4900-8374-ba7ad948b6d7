(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00454d178be4ce07ce841ef8e90957f7985e91d419":()=>s.T});var s=r(10061)},54467:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56713:(e,t,r)=>{"use strict";r.d(t,{QR:()=>o});var s=r(14584);let a={baseURL:"https://openspeech.bytedance.com",timeout:6e4,retryAttempts:3,retryDelay:1e3};class i{constructor(e,t){if(this.apiKey=e||process.env.VOLCENGINE_ACCESS_TOKEN||"",this.appId=t||process.env.VOLCENGINE_APP_ID||"",this.baseURL=a.baseURL,!this.apiKey||!this.appId)throw Error("Volcengine API key and App ID are required")}async synthesizeText(e){let t=(0,s.YJ)(),r={app:{appid:this.appId,token:"access_token",cluster:"volcano_tts"},user:{uid:"web_user"},audio:{voice_type:e.voice_type||"BV700_streaming",encoding:e.encoding||"mp3",rate:e.rate||24e3,speed_ratio:e.speed_ratio||1,volume_ratio:e.volume_ratio||1,pitch_ratio:e.pitch_ratio||1,emotion:e.emotion,language:e.language||"cn"},request:{reqid:t,text:e.text,text_type:"plain",operation:"query"}};return await this.makeRequest("/api/v1/tts","POST",r)}async submitAsyncTTS(e){let t=(0,s.YJ)(),r={appid:this.appId,reqid:t,text:e.text,format:e.format||"mp3",voice_type:e.voice_type||"BV701_streaming",sample_rate:e.sample_rate||24e3,volume:e.volume||1,speed:e.speed||1,pitch:e.pitch||1,enable_subtitle:e.enable_subtitle||0,callback_url:e.callback_url};return await this.makeRequest("/api/v1/tts_async/submit","POST",r,{"Resource-Id":"volc.tts_async.default"})}async queryAsyncTTS(e){let t=`/api/v1/tts_async/query?appid=${this.appId}&task_id=${e}`;return await this.makeRequest(t,"GET",null,{"Resource-Id":"volc.tts_async.default"})}async submitASRBigModel(e){let t=(0,s.YJ)(),r={appid:this.appId,reqid:t,url:e.url,language:e.language||"zh",use_itn:!1!==e.use_itn,use_capitalize:!1!==e.use_capitalize,max_lines:e.max_lines||1,callback_url:e.callback_url};return await this.makeRequest("/api/v3/auc/bigmodel/submit","POST",r,{"Resource-Id":"volc.bigasr.sauc"})}async queryASRBigModel(e){let t={appid:this.appId,id:e};return await this.makeRequest("/api/v3/auc/bigmodel/query","POST",t,{"Resource-Id":"volc.bigasr.sauc"})}async submitASRStandard(e,t="fast"){let r=(0,s.YJ)(),a={appid:this.appId,reqid:r,cluster:"standard"===t?"volcano_asr_standard":"volcano_asr",url:e.url,language:e.language||"zh-CN",use_itn:!1!==e.use_itn,use_capitalize:!1!==e.use_capitalize,callback_url:e.callback_url};return await this.makeRequest("/api/v1/auc/submit","POST",a,{"Resource-Id":"volc.auc.sauc"})}async queryASRStandard(e){let t={appid:this.appId,id:e};return await this.makeRequest("/api/v1/auc/query","POST",t,{"Resource-Id":"volc.auc.sauc"})}async uploadVoiceClone(e){let t={appid:this.appId,speaker_id:e.speaker_id,audios:[{audio_bytes:e.audio_bytes,audio_format:e.audio_format,text:e.text}],source:2,language:e.language||0,model_type:e.model_type||1};return await this.makeRequest("/api/v1/mega_tts/audio/upload","POST",t,{"Resource-Id":"volc.megatts.voiceclone"})}async queryVoiceCloneStatus(e){let t={appid:this.appId,speaker_id:e};return await this.makeRequest("/api/v1/mega_tts/status","POST",t,{"Resource-Id":"volc.megatts.voiceclone"})}async makeRequest(e,t="POST",r,s){let i=`${this.baseURL}${e}`,o={Authorization:`Bearer;${this.apiKey}`,"Content-Type":"application/json",...s},n={method:t,headers:o,signal:AbortSignal.timeout(a.timeout)};"POST"===t&&r&&(n.body=JSON.stringify(r)),console.log("=== Volcengine API Request Debug Info ==="),console.log("URL:",i),console.log("Method:",t),console.log("Headers:",JSON.stringify(o,null,2)),console.log("Body:",n.body),console.log("==========================================");let u=Error("Unknown error");for(let e=1;e<=a.retryAttempts;e++)try{let e=await fetch(i,n);if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let t=await e.json();return console.log("=== Volcengine API Response ==="),console.log("Response:",JSON.stringify(t,null,2)),console.log("==============================="),t}catch(t){u=t,console.log(`=== Attempt ${e} failed ===`),console.log("Error:",u.message),console.log("============================="),e<a.retryAttempts&&await new Promise(t=>setTimeout(t,a.retryDelay*e))}throw Error(`Volcengine API request failed after ${a.retryAttempts} attempts: ${u.message}`)}}function o(e,t){return new i(e,t)}},57975:e=>{"use strict";e.exports=require("node:util")},58246:(e,t,r)=>{"use strict";function s(e){return o(0,"ok",e||[])}function a(){return o(0,"ok")}function i(e){return o(-1,e)}function o(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>i,j7:()=>o,rn:()=>a})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68623:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(73675),i=r(78740),o=r(91179),n=r(58246),u=r(53908),c=r(56713);async function l(e){try{let{text:t,voice_type:r,options:s}=await e.json();if(!t)return(0,n.YS)("Missing required parameter: text");if(!await (0,u.TG)())return(0,n.j7)(-2,"Authentication required");if(t.length>1e5)return(0,n.YS)("Text length exceeds 100,000 characters limit for async TTS");let a=(0,c.QR)(),i={text:t,voice_type:r||"BV701_streaming",format:s?.format||"mp3",sample_rate:s?.sample_rate||24e3,volume:s?.volume||1,speed:s?.speed||1,pitch:s?.pitch||1,enable_subtitle:s?.enable_subtitle||0,callback_url:s?.enable_callback?s.callback_url:void 0},o=await a.submitAsyncTTS(i);if(2e7===o.code)return(0,n.DQ)({task_id:o.task_id,status:"submitted"});return(0,n.YS)(`Async TTS submission failed: ${o.message}`)}catch(e){return console.error("Volcengine Async TTS submission error:",e),(0,n.YS)(`Async TTS service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/volcengine/tts-async/submit/route",pathname:"/api/volcengine/tts-async/submit",filename:"route",bundlePath:"app/api/volcengine/tts-async/submit/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea/app/api/volcengine/tts-async/submit/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:y}=p;function g(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},73675:(e,t,r)=>{"use strict";e.exports=r(44870)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78443:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1179,3057,5474,4209,5397,7125],()=>r(68623));module.exports=s})();