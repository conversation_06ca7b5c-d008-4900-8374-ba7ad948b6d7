{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!privacy-policy|terms-of-service|api\\/|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "LVErFt4q-jqt7l5i7iJpr", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dv7dWAk9T2M+/LsPh/t8l/c14E4NhTVpbZj8PiBGmPM=", "__NEXT_PREVIEW_MODE_ID": "d0f4fdec5a555366623dff24ecd24aae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "429ed0bde5499d15e4426e7c8d6b50f19e6acb5013bd43e75b1659ae36650cdb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "11e8dd91d7009e680783f7681b6290360a01db3cb39e1ee7516767884704ffad"}}}, "functions": {}, "sortedMiddleware": ["/"]}