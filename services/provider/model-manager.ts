/**
 * 统一的模型管理器
 * 替代 data/ai-models-local.ts，提供统一的模型访问接口
 */

import { ALL_GRSAI_UNIFIED_MODELS, UnifiedModelConfig } from './grsai/unified-config';
import { REPLICATE_MODEL_INFO, ReplicateModelInfo } from './replicate/parameters/image-models';
import { addTranslationToModels } from './grsai/translation-helper';

// 统一的模型接口
export interface UnifiedModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: 'text' | 'image' | 'video' | 'multimodal';
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  description?: string;
  description_i18n?: Record<string, string>;
  model_name_i18n?: Record<string, string>;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 将 GRSAI UnifiedModelConfig 转换为 UnifiedModel 格式
 */
function convertGRSAIModelToUnified(model: UnifiedModelConfig, id: number): UnifiedModel {
  return {
    id,
    model_id: model.id,
    model_name: model.name,
    model_type: model.type as 'text' | 'image' | 'video' | 'multimodal',
    provider: model.provider.toLowerCase(),
    api_endpoint: model.apiEndpoint,
    credits_per_unit: model.creditsPerUnit,
    unit_type: model.unitType.toLowerCase(),
    is_active: model.isActive,
    description: model.description,
    max_input_size: model.maxInputSize,
    supported_features: model.supportedFeatures,
    icon: model.icon,
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  };
}

/**
 * 将 Replicate ReplicateModelInfo 转换为 UnifiedModel 格式
 */
function convertReplicateModelToUnified(model: ReplicateModelInfo): UnifiedModel {
  return {
    id: model.id,
    model_id: model.model_id,
    model_name: model.model_name,
    model_type: model.model_type,
    provider: model.provider,
    api_endpoint: model.api_endpoint,
    credits_per_unit: model.credits_per_unit,
    unit_type: model.unit_type,
    is_active: model.is_active,
    description: model.description,
    description_i18n: model.description_i18n,
    model_name_i18n: model.model_name_i18n,
    max_input_size: model.max_input_size,
    supported_features: model.supported_features,
    icon: model.icon,
    created_at: model.created_at,
    updated_at: model.updated_at
  };
}

/**
 * 获取所有模型（包括 GRSAI 和 Replicate）
 */
function getAllModels(): UnifiedModel[] {
  // 转换 GRSAI 模型
  const grsaiModels = ALL_GRSAI_UNIFIED_MODELS.map((model, index) => 
    convertGRSAIModelToUnified(model, 100 + index)
  );
  
  // 转换 Replicate 模型
  const replicateModels = [convertReplicateModelToUnified(REPLICATE_MODEL_INFO)];
  
  // 合并所有模型
  return [...grsaiModels, ...replicateModels];
}

/**
 * 处理本地化内容的工具函数
 */
export function getLocalizedContent(
  i18nContent: Record<string, string> | undefined,
  locale: string,
  fallbackLocale: string = 'en'
): string {
  if (!i18nContent) return '';
  return i18nContent[locale] || i18nContent[fallbackLocale] || Object.values(i18nContent)[0] || '';
}

/**
 * 处理模型的本地化内容
 */
function processModelWithLocalization(model: UnifiedModel, locale: string): UnifiedModel {
  return {
    ...model,
    model_name: getLocalizedContent(model.model_name_i18n, locale) || model.model_name,
    description: getLocalizedContent(model.description_i18n, locale) || model.description || ''
  };
}

/**
 * 获取所有活跃的AI模型
 */
export function getActiveAIModels(locale: string = 'en'): UnifiedModel[] {
  return getAllModels()
    .filter(model => model.is_active)
    .map(model => processModelWithLocalization(model, locale))
    .sort((a, b) => {
      // 先按模型类型排序，再按积分消耗排序
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

/**
 * 根据模型类型获取模型列表
 */
export function getAIModelsByType(type: string, locale: string = 'en'): UnifiedModel[] {
  return getAllModels()
    .filter(model => model.is_active && model.model_type === type)
    .map(model => processModelWithLocalization(model, locale))
    .sort((a, b) => a.credits_per_unit - b.credits_per_unit);
}

/**
 * 根据模型ID获取单个模型
 */
export function getAIModelById(modelId: string, locale: string = 'en'): UnifiedModel | null {
  const model = getAllModels().find(m => m.model_id === modelId && m.is_active);
  return model ? processModelWithLocalization(model, locale) : null;
}

/**
 * 根据模型ID获取单个模型（包括不活跃的模型）
 */
export function getAIModelByIdIncludeInactive(modelId: string, locale: string = 'en'): UnifiedModel | null {
  const model = getAllModels().find(m => m.model_id === modelId);
  return model ? processModelWithLocalization(model, locale) : null;
}

/**
 * 根据提供商获取模型列表
 */
export function getAIModelsByProvider(provider: string, locale: string = 'en'): UnifiedModel[] {
  return getAllModels()
    .filter(model => model.is_active && model.provider === provider)
    .map(model => processModelWithLocalization(model, locale))
    .sort((a, b) => {
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

// 为了向后兼容，导出带有 Local 前缀的函数
export const getLocalActiveAIModels = getActiveAIModels;
export const getLocalAIModelsByType = getAIModelsByType;
export const getLocalAIModelById = getAIModelById;
export const getLocalAIModelByIdIncludeInactive = getAIModelByIdIncludeInactive;
export const getLocalAIModelsByProvider = getAIModelsByProvider;

// 导出类型别名以保持兼容性
export type LocalAIModel = UnifiedModel;
