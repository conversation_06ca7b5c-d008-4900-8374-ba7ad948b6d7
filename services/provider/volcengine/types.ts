/**
 * 火山引擎语音服务类型定义
 */

export interface VolcengineTTSRequest {
  text: string;
  voice_type?: string;
  encoding?: string;
  rate?: number;
  speed_ratio?: number;
  volume_ratio?: number;
  pitch_ratio?: number;
  emotion?: string;
  language?: string;
}

export interface VolcengineTTSResponse {
  reqid: string;
  code: number;
  message: string;
  data?: string; // base64编码的音频数据
  addition?: {
    duration: string;
    frontend: string;
  };
}

export interface VolcengineAsyncTTSRequest {
  text: string;
  voice_type?: string;
  format?: string;
  sample_rate?: number;
  speed_ratio?: number;
  volume_ratio?: number;
  pitch_ratio?: number;
  emotion?: string;
  language?: string;
  callback_url?: string;
}

export interface VolcengineAsyncTTSResponse {
  reqid: string;
  code: number;
  message: string;
  task_id?: string;
}

export interface VolcengineASRRequest {
  url: string;
  language?: string;
  use_itn?: boolean;
  use_capitalize?: boolean;
  callback_url?: string;
}

export interface VolcengineASRResponse {
  reqid: string;
  code: number;
  message: string;
  task_id?: string;
  result?: {
    text: string;
    confidence: number;
    words?: Array<{
      word: string;
      start_time: number;
      end_time: number;
      confidence: number;
    }>;
  };
}

export interface VolcengineVoiceCloneRequest {
  text: string;
  voice_id: string;
  speed_ratio?: number;
  volume_ratio?: number;
  pitch_ratio?: number;
  emotion?: string;
  format?: string;
  sample_rate?: number;
}

export interface VolcengineVoiceCloneResponse {
  reqid: string;
  code: number;
  message: string;
  data?: string; // base64编码的音频数据
  addition?: {
    duration: string;
  };
}

export interface VolcengineVoiceCloneTrainRequest {
  voice_name: string;
  audio_files: string[]; // 音频文件URL列表
  text_files?: string[]; // 对应的文本文件URL列表
  callback_url?: string;
}

export interface VolcengineVoiceCloneTrainResponse {
  reqid: string;
  code: number;
  message: string;
  voice_id?: string;
  task_id?: string;
}

export interface VolcengineTaskStatusResponse {
  reqid: string;
  code: number;
  message: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  result?: any;
  error_detail?: string;
}
