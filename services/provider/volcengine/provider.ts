import { getUuid } from "@/lib/hash";
import { VOLCENGINE_CONFIG } from './config';
import type {
  VolcengineTTSRequest,
  VolcengineTTSResponse,
  VolcengineAsyncTTSRequest,
  VolcengineAsyncTTSResponse,
  VolcengineASRRequest,
  VolcengineASRResponse,
  VolcengineVoiceCloneRequest,
  VolcengineVoiceCloneResponse,
  VolcengineVoiceCloneTrainRequest,
  VolcengineVoiceCloneTrainResponse,
  VolcengineTaskStatusResponse
} from './types';

/**
 * 火山引擎语音服务提供商
 */
export class VolcengineProvider {
  private apiKey: string;
  private appId: string;
  private baseURL: string;

  constructor(apiKey?: string, appId?: string) {
    this.apiKey = apiKey || process.env.VOLCENGINE_ACCESS_TOKEN || '';
    this.appId = appId || process.env.VOLCENGINE_APP_ID || '';
    this.baseURL = VOLCENGINE_CONFIG.baseURL;
    
    if (!this.apiKey || !this.appId) {
      throw new Error('Volcengine API key and App ID are required');
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    data?: any,
    additionalHeaders?: Record<string, string>
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
      ...additionalHeaders
    };

    const requestOptions: RequestInit = {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined
    };

    console.log(`[Volcengine] Making ${method} request to: ${url}`);
    console.log(`[Volcengine] Request data:`, JSON.stringify(data, null, 2));

    let attempt = 0;
    while (attempt < VOLCENGINE_CONFIG.retryAttempts) {
      try {
        const response = await fetch(url, requestOptions);

        console.log(`[Volcengine] Response status: ${response.status}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`[Volcengine] HTTP Error ${response.status}:`, errorText);
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const result = await response.json();
        console.log(`[Volcengine] Response:`, JSON.stringify(result, null, 2));
        return result;
      } catch (error) {
        console.error(`[Volcengine] Request attempt ${attempt + 1} failed:`, error);
        attempt++;
        if (attempt >= VOLCENGINE_CONFIG.retryAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, VOLCENGINE_CONFIG.retryDelay * attempt));
      }
    }
  }

  /**
   * 短文本语音合成
   */
  async synthesizeText(request: VolcengineTTSRequest): Promise<VolcengineTTSResponse> {
    const reqid = getUuid();

    const payload = {
      app: {
        appid: this.appId,
        token: "access_token", // 根据文档，这里应该是固定字符串
        cluster: "volcano_tts"
      },
      user: {
        uid: "web_user"
      },
      audio: {
        voice_type: request.voice_type || "BV700_streaming",
        encoding: request.encoding || "mp3",
        rate: request.rate || 24000,
        speed_ratio: request.speed_ratio || 1.0,
        volume_ratio: request.volume_ratio || 1.0,
        pitch_ratio: request.pitch_ratio || 1.0,
        emotion: request.emotion,
        language: request.language || "cn"
      },
      request: {
        reqid: reqid,
        text: request.text,
        text_type: "plain",
        operation: "query"
      }
    };

    const response = await this.makeRequest('/api/v1/tts', 'POST', payload);
    return response as VolcengineTTSResponse;
  }

  /**
   * 长文本异步语音合成 - 提交任务
   */
  async submitAsyncTTS(request: VolcengineAsyncTTSRequest): Promise<VolcengineAsyncTTSResponse> {
    const reqid = getUuid();
    
    const payload = {
      appid: this.appId,
      reqid: reqid,
      text: request.text,
      format: request.format || "mp3",
      voice_type: request.voice_type || "BV701_streaming",
      sample_rate: request.sample_rate || 24000,
      speed_ratio: request.speed_ratio || 1.0,
      volume_ratio: request.volume_ratio || 1.0,
      pitch_ratio: request.pitch_ratio || 1.0,
      emotion: request.emotion,
      language: request.language || "cn",
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v1/tts/submit', 'POST', payload, {
      'Resource-Id': 'volc.tts.sauc'
    });
    return response as VolcengineAsyncTTSResponse;
  }

  /**
   * 查询异步任务状态
   */
  async getTaskStatus(taskId: string): Promise<VolcengineTaskStatusResponse> {
    const payload = {
      appid: this.appId,
      task_id: taskId
    };

    const response = await this.makeRequest('/api/v1/tts/query', 'POST', payload, {
      'Resource-Id': 'volc.tts.sauc'
    });
    return response as VolcengineTaskStatusResponse;
  }

  /**
   * 标准语音识别 - 提交任务
   */
  async submitASRStandard(request: VolcengineASRRequest, version: 'fast' | 'standard' = 'fast'): Promise<VolcengineASRResponse> {
    const reqid = getUuid();
    const cluster = version === 'standard' ? 'volcano_asr_standard' : 'volcano_asr';
    
    const payload = {
      appid: this.appId,
      reqid: reqid,
      cluster: cluster,
      url: request.url,
      language: request.language || "zh-CN",
      use_itn: request.use_itn !== false,
      use_capitalize: request.use_capitalize !== false,
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v1/auc/submit', 'POST', payload, {
      'Resource-Id': 'volc.auc.sauc'
    });
    return response as VolcengineASRResponse;
  }

  /**
   * 语音复刻 - 合成语音
   */
  async synthesizeWithClonedVoice(request: VolcengineVoiceCloneRequest): Promise<VolcengineVoiceCloneResponse> {
    const reqid = getUuid();

    const payload = {
      app: {
        appid: this.appId,
        token: "access_token",
        cluster: "volcano_tts"
      },
      user: {
        uid: "web_user"
      },
      audio: {
        voice_type: request.voice_id,
        encoding: request.format || "mp3",
        rate: request.sample_rate || 24000,
        speed_ratio: request.speed_ratio || 1.0,
        volume_ratio: request.volume_ratio || 1.0,
        pitch_ratio: request.pitch_ratio || 1.0,
        emotion: request.emotion
      },
      request: {
        reqid: reqid,
        text: request.text,
        text_type: "plain",
        operation: "query"
      }
    };

    const response = await this.makeRequest('/api/v1/tts', 'POST', payload);
    return response as VolcengineVoiceCloneResponse;
  }

  /**
   * 语音复刻 - 训练声音模型
   */
  async trainVoiceClone(request: VolcengineVoiceCloneTrainRequest): Promise<VolcengineVoiceCloneTrainResponse> {
    const reqid = getUuid();

    const payload = {
      appid: this.appId,
      reqid: reqid,
      voice_name: request.voice_name,
      audio_files: request.audio_files,
      text_files: request.text_files,
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v1/voice/train', 'POST', payload, {
      'Resource-Id': 'volc.voice.train'
    });
    return response as VolcengineVoiceCloneTrainResponse;
  }
}
