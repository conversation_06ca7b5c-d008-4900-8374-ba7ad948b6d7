import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { AIRequest, AIResponse } from "@/types/ai-model";
import {
  getActiveAIModelById,
  calculateModelCost,
  createAIModelUsage,
  updateAIModelUsageByTaskId
} from "@/models/ai-model";
import { ReplicateProvider } from "./provider";
import { transferMultipleFiles } from "../../file-transfer";
import type { ReplicateImageRequest } from "./types";

/**
 * Replicate 提供商配置
 */
export const REPLICATE_CONFIG = {
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000
} as const;



/**
 * 独立的 Replicate AI 服务
 * 完全独立于 GRSAI provider，实现相同的接口和生命周期管理
 */
export class ReplicateAIService {
  private replicateProvider: ReplicateProvider;

  constructor() {
    this.replicateProvider = new ReplicateProvider();
  }

  /**
   * 处理AI请求 - 与 GrsAIService 相同的接口
   */
  async processRequestFromClient(
    userUuid: string,
    request: AIRequest
  ): Promise<AIResponse> {
    const taskId = getUuid();
    
    console.log(`[Replicate AI Service] Processing request for user: ${userUuid}, model: ${request.model}`);
    
    // 获取模型配置
    const model = await getActiveAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 验证是否为 Replicate 模型
    if (model.provider !== 'replicate') {
      throw new Error(`Model ${request.model} is not a Replicate model`);
    }

    // 估算成本
    const estimatedCost = await this.estimateCost(request, model);
    
    console.log(`[Replicate AI Service] Estimated cost: ${estimatedCost} credits`);
    
    // 创建使用记录
    await createAIModelUsage({
      user_uuid: userUuid,
      model_id: request.model,
      task_id: taskId,
      provider: 'replicate',
      credits_consumed: estimatedCost,
      status: 'pending',
      request_params: request,
      started_at: getIsoTimestr()
    });

    console.log(`[Replicate AI Service] Created usage record: ${taskId}`);

    try {
      let response: AIResponse;

      switch (request.type) {
        case 'image':
          response = await this.handleImageGeneration(request, taskId);
          break;
        default:
          throw new Error(`Unsupported request type: ${request.type} for Replicate provider`);
      }

      // 更新使用记录
      if (response.status === 'success') {
        await updateAIModelUsageByTaskId(taskId, {
          status: 'success',
          output_size: this.calculateOutputSize(response),
          response_data: response,
          completed_at: getIsoTimestr()
        });
        
        console.log(`[Replicate AI Service] Updated usage record to success`);
      }

      return response;
    } catch (error) {
      console.error(`[Replicate AI Service] Request failed:`, error);
      
      // 更新失败记录
      await updateAIModelUsageByTaskId(taskId, {
        status: 'failed',
        error_reason: 'error',
        error_detail: error instanceof Error ? error.message : 'Unknown error',
        completed_at: getIsoTimestr()
      });

      throw error;
    }
  }

  /**
   * 处理图像生成
   */
  private async handleImageGeneration(request: AIRequest, taskId: string): Promise<AIResponse> {
    console.log(`[Replicate AI Service] Starting image generation for model: ${request.model}`);

    const model = await getActiveAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 构建 Replicate 请求
    const replicateRequest: ReplicateImageRequest = {
      model: request.model,
      prompt: request.prompt,
      options: {
        // 基础参数映射
        aspect_ratio: request.options?.aspectRatio,
        num_outputs: request.options?.variants || 1,
        output_format: request.options?.output_format || 'webp',
        output_quality: request.options?.output_quality || 90,
        
        // 高级参数
        guidance: request.options?.guidance,
        num_inference_steps: request.options?.num_inference_steps,
        prompt_strength: request.options?.prompt_strength,
        seed: request.options?.seed,
        disable_safety_checker: request.options?.disable_safety_checker,
        go_fast: request.options?.go_fast,
        megapixels: request.options?.megapixels,
        
        // img2img 支持
        image: request.options?.uploadedImages?.[0]
      }
    };

    console.log(`[Replicate AI Service] Calling Replicate API with request:`, replicateRequest);

    // 调用 Replicate API（异步模式）
    const replicateResponse = await this.replicateProvider.generateImage(replicateRequest);

    console.log(`[Replicate AI Service] Replicate response:`, replicateResponse);

    if (replicateResponse.status === 'failed') {
      throw new Error(replicateResponse.error || 'Replicate API failed');
    }

    // 更新使用记录，保存Replicate预测ID
    await updateAIModelUsageByTaskId(taskId, {
      external_request_id: replicateResponse.id,
      response_data: replicateResponse,
      status: 'pending'
    });

    // 对于异步任务，返回pending状态
    if (replicateResponse.status === 'starting' || replicateResponse.status === 'processing') {
      return {
        id: taskId,
        task_id: taskId,
        type: 'image',
        status: 'pending',
        progress: replicateResponse.progress || 0,
        usage: {
          credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
        }
      };
    }

    // 如果已经完成，处理结果
    if (replicateResponse.status === 'succeeded' && replicateResponse.urls) {
      // 转存文件到我们的存储
      console.log(`[Replicate AI Service] Transferring ${replicateResponse.urls.length} files to our storage`);

      const transferResults = await transferMultipleFiles(
        replicateResponse.urls,
        'image',
        taskId
      );

      // 检查转存结果
      const successfulTransfers = transferResults.filter(r => r.success);
      const failedTransfers = transferResults.filter(r => !r.success);

      if (failedTransfers.length > 0) {
        console.warn(`[Replicate AI Service] Some file transfers failed:`, failedTransfers);
      }

      if (successfulTransfers.length === 0) {
        throw new Error('All file transfers failed');
      }

      // 构建最终响应
      const images = successfulTransfers.map(transfer => ({
        url: transfer.url!,
        width: 1024, // Replicate 默认尺寸，可以根据实际情况调整
        height: 1024
      }));

      console.log(`[Replicate AI Service] Successfully transferred ${images.length} images`);

      return {
        id: taskId,
        task_id: taskId,
        type: 'image',
        status: 'success',
        result: {
          images
        },
        usage: {
          credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
        }
      };
    }

    // 如果到这里，说明是其他状态，视为失败
    return {
      id: taskId,
      task_id: taskId,
      type: 'image',
      status: 'failed',
      error: {
        reason: 'error',
        detail: 'Unexpected response from Replicate API'
      },
      usage: {
        credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
      }
    };
  }

  /**
   * 估算请求成本
   */
  private async estimateCost(request: AIRequest, model: any): Promise<number> {
    switch (request.type) {
      case 'image':
        const variants = request.options?.variants || 1;
        return model.credits_per_unit * variants;
      default:
        return model.credits_per_unit;
    }
  }

  /**
   * 计算输出大小
   */
  private calculateOutputSize(response: AIResponse): number {
    if (response.result?.images) {
      return response.result.images.length;
    }
    return 0;
  }

  /**
   * 检查模型是否支持
   */
  isModelSupported(modelId: string): boolean {
    return this.replicateProvider.isModelSupported(modelId);
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[] {
    return this.replicateProvider.getSupportedModels();
  }

  /**
   * 查询Replicate预测结果
   */
  async getReplicateResult(predictionId: string): Promise<any> {
    try {
      console.log(`[Replicate AI Service] Querying Replicate prediction: ${predictionId}`);

      const replicateResponse = await this.replicateProvider.getPrediction(predictionId);

      console.log(`[Replicate AI Service] Replicate prediction response:`, replicateResponse);

      return replicateResponse;
    } catch (error) {
      console.error(`[Replicate AI Service] Error querying Replicate prediction:`, error);
      throw error;
    }
  }
}
