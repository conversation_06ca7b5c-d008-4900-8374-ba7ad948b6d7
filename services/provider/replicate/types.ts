/**
 * Replicate API 请求和响应类型定义
 */

export interface ReplicateImageRequest {
  model: string;
  prompt: string;
  options?: {
    // 基础参数
    aspect_ratio?: string;
    image?: string;
    prompt_strength?: number;
    num_outputs?: number;
    num_inference_steps?: number;
    guidance?: number;
    seed?: number;
    output_format?: string;
    output_quality?: number;
    disable_safety_checker?: boolean;
    go_fast?: boolean;
    megapixels?: string;
  };
}

export interface ReplicateImageResponse {
  id: string;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  urls?: string[];
  output?: any;
  error?: string;
  progress?: number;
  logs?: string;
  usage?: {
    credits_consumed: number;
  };
}
