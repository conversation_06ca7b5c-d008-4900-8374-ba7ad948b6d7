/**
 * GRSAI 模型配置
 * 按照功能对模型进行分类，支持灵活的提供商切换
 */

export enum ModelType {
  TEXT = 'text',
  IMAGE = 'image', 
  VIDEO = 'video',
  MULTIMODAL = 'multimodal'
}

export enum Provider {
  GRSAI = 'grsai',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

export enum UnitType {
  TOKENS = 'tokens',
  IMAGES = 'images', 
  VIDEOS = 'videos'
}

import { ParameterConfig } from '../types';

export interface ModelConfig {
  id: string;
  name: string;
  type: ModelType;
  provider: Provider;
  apiEndpoint: string;
  creditsPerUnit: number;
  unitType: UnitType;
  isActive: boolean;
  description?: string;
  maxInputSize?: number;
  supportedFeatures?: string[];
  icon?: string;
  // 参数配置
  parameters?: ParameterConfig[];
  parameterGroups?: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}


export type GRSAIRegion = 'overseas' | 'domestic';
/**
 * GRSAI 模型配置
 */
export const GRSAI_MODELS: ModelConfig[] = [
  // 文本生成模型
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 10,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '高级对话模型，适合复杂任务和专业用途',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'analysis'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 8192,
        description: '最大输出长度',
        tooltip: '生成文本的最大token数量，影响回答长度',
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        description: '创造性程度',
        tooltip: '控制输出的随机性，0为最保守，1为最创新',
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        description: '核心采样',
        tooltip: '控制词汇选择范围，较小值使输出更集中',
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        description: '流式输出',
        tooltip: '启用后将实时显示生成过程',
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 5,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '快速对话模型，响应迅速，效率高',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'fast_response'],
    icon: '/imgs/icons/google.svg'
  },
  {
    id: 'gemini-2.5-flash-lite',
    name: 'Gemini 2.5 Flash Lite',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 2,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '轻量级对话模型，成本低廉，功能基础',
    maxInputSize: 64000,
    supportedFeatures: ['text_generation', 'basic_conversation'],
    icon: '/imgs/icons/google.svg'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 8,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 轻量版本，性能与成本平衡',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'conversation', 'reasoning'],
    icon: '/imgs/icons/openai.svg'
  },
  // 多模态模型
  {
    id: 'o4-mini-all',
    name: 'GPT-4o Mini All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 12,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o Mini 全功能版本，支持视觉等多模态能力',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'reasoning'],
    icon: '/imgs/icons/openai.svg'
  },
  {
    id: 'gpt-4o-all',
    name: 'GPT-4o All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 20,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 完整版本，具备所有高级功能和多模态支持',
    maxInputSize: 128000,
    supportedFeatures: ['text_generation', 'vision', 'multimodal', 'advanced_reasoning', 'code_generation'],
    icon: '/imgs/icons/openai.svg'
  },

  // 图像生成模型
  {
    id: 'flux-pro-1.1',
    name: 'Flux Pro 1.1',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 30,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Flux 技术 v1.1 专业图像生成',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'professional', 'flux_tech'],
    icon: '/imgs/icons/flux.svg'
  },
  {
    id: 'gpt-4o-image',
    name: 'GPT-4o Image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 40,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '使用 GPT-4o 架构的高质量图像生成',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'gpt_powered', 'versatile'],
    icon: '/imgs/icons/openai.svg'
  },
  {
    id: 'flux-kontext-pro',
    name: 'Flux Kontext Pro',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 45,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '上下文感知的专业级图像生成',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'context_aware', 'professional'],
    icon: '/imgs/icons/flux.svg'
  },
  {
    id: 'sora-image',
    name: 'Sora Image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 50,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '基于 Sora 技术的先进图像生成模型',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'high_quality', 'creative'],
    icon: '/imgs/icons/openai.svg'
  },
  {
    id: 'flux-pro-1.1-ultra',
    name: 'Flux Pro 1.1 Ultra',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 60,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '增强版 Flux Pro 超高质量图像生成',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'ultra_quality', 'enhanced_flux'],
    icon: '/imgs/icons/flux.svg'
  },
  {
    id: 'flux-kontext-max',
    name: 'Flux Kontext Max',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 80,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '最高质量的上下文感知图像生成',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'max_quality', 'context_aware'],
    icon: '/imgs/icons/flux.svg'
  },

  // 视频生成模型
  {
    id: 'veo3-fast',
    name: 'Veo3 Fast',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 100,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: 'Veo3 技术快速视频生成，结果迅速',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'fast', 'veo3_tech'],
    icon: '/imgs/icons/google.svg'
  },
  {
    id: 'veo3-pro',
    name: 'Veo3 Pro',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/api/ai/generate',
    creditsPerUnit: 200,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: '具备高级 Veo3 能力的专业视频生成',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'professional', 'advanced_veo3'],
    icon: '/imgs/icons/google.svg'
  }
];

/**
 * 根据模型类型获取模型列表
 */
export function getModelsByType(type: ModelType): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.type === type && model.isActive);
}

/**
 * 根据模型ID获取模型配置
 */
export function getModelById(id: string): ModelConfig | undefined {
  return GRSAI_MODELS.find(model => model.id === id);
}

/**
 * 获取所有活跃模型
 */
export function getActiveModels(): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.isActive);
}

/**
 * 根据提供商获取模型列表
 */
export function getModelsByProvider(provider: Provider): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.provider === provider && model.isActive);
}
