/**
 * GRSAI API 请求和响应类型定义
 */

export interface GRSAITextRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

export interface GRSAITextResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface GRSAIImageRequest {
  model: string;
  prompt: string;
  size?: string;
  variants?: number;
  urls?: string[];
  webHook?: string;
  cdn?: string;
}

export interface GRSAIImageResponse {
  id: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  data?: {
    urls: string[];
  };
  error?: string;
  progress?: number;
}

export interface GRSAIFluxRequest {
  model: string;
  prompt: string;
  image_size?: string;
  num_inference_steps?: number;
  guidance_scale?: number;
  seed?: number;
  urls?: string[];
  webHook?: string;
  cdn?: string;
}

export interface GRSAIFluxResponse {
  id: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  data?: {
    urls: string[];
  };
  error?: string;
  progress?: number;
}

export interface GRSAIVideoRequest {
  model: string;
  prompt: string;
  duration?: number;
  aspect_ratio?: string;
  urls?: string[];
  webHook?: string;
  cdn?: string;
}

export interface GRSAIVideoResponse {
  id: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  data?: {
    urls: string[];
  };
  error?: string;
  progress?: number;
}

export interface GRSAIResultResponse {
  id: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  data?: {
    urls: string[];
  };
  error?: string;
  progress?: number;
}
