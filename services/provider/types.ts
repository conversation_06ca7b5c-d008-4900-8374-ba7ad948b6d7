/**
 * 模型参数配置类型定义
 */

export interface ParameterConfig {
  name: string;
  type: 'number' | 'string' | 'select' | 'boolean' | 'range' | 'file';
  required: boolean;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{
    value: string;
    label?: string; // 可选，运行时从翻译文件获取
    description?: string; // 可选，运行时从翻译文件获取
  }>;
  description?: string; // 可选，运行时从翻译文件获取
  tooltip?: string; // 可选，运行时从翻译文件获取
  group: 'basic' | 'advanced' | 'expert';
  dependsOn?: string; // 依赖其他参数
  condition?: {
    field: string;
    value: any;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'not_empty';
  };
}

export interface ModelParameterConfig {
  modelId: string;
  version: string;
  provider: string;
  modelType: 'text' | 'image' | 'video' | 'multimodal';
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}

/**
 * 参数组标题映射
 */
export const PARAMETER_GROUP_TITLES = {
  basic: '基础设置',
  advanced: '高级设置',
  expert: '专家设置'
} as const;

/**
 * 通用的宽高比选项
 */
export const ASPECT_RATIO_OPTIONS = [
  { value: '1:1', label: '正方形 (1:1)', description: '适合社交媒体头像' },
  { value: '16:9', label: '横屏 (16:9)', description: '适合视频封面' },
  { value: '9:16', label: '竖屏 (9:16)', description: '适合手机壁纸' },
  { value: '4:3', label: '标准 (4:3)', description: '经典照片比例' },
  { value: '3:2', label: '照片 (3:2)', description: '相机默认比例' },
  { value: '21:9', label: '超宽 (21:9)', description: '电影级比例' },
  { value: '2:3', label: '竖版照片 (2:3)', description: '竖版相机比例' },
  { value: '4:5', label: 'Instagram (4:5)', description: 'Instagram竖版' },
  { value: '5:4', label: '经典横版 (5:4)', description: '经典横版比例' },
  { value: '3:4', label: '标准竖版 (3:4)', description: '标准竖版比例' }
];

/**
 * 通用的图片数量选项
 */
export const VARIANTS_OPTIONS = [
  { value: '1', label: '1张图片', description: '生成1张图片' },
  { value: '2', label: '2张图片', description: '生成2张图片' },
  { value: '3', label: '3张图片', description: '生成3张图片' },
  { value: '4', label: '4张图片', description: '生成4张图片' }
];

/**
 * 通用的输出格式选项
 */
export const OUTPUT_FORMAT_OPTIONS = [
  { value: 'webp', label: 'WebP', description: '现代格式，高压缩率' },
  { value: 'jpg', label: 'JPG', description: '通用格式，兼容性好' },
  { value: 'png', label: 'PNG', description: '无损压缩，文件较大' }
];

/**
 * 通用的图片尺寸选项（Replicate）
 */
export const MEGAPIXELS_OPTIONS = [
  { value: '0.25', label: '0.25 百万像素', description: '快速生成，较低分辨率' },
  { value: '1', label: '1 百万像素', description: '标准质量，平衡速度' }
];

/**
 * 通用的布尔值选项
 */
export const BOOLEAN_OPTIONS = [
  { value: 'true', label: '启用', description: '开启此功能' },
  { value: 'false', label: '禁用', description: '关闭此功能' }
];
