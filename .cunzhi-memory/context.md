# 项目上下文信息

- GRSAI提供商集成项目已完成，包括：1)14个AI模型配置(文本/图像/视频/多模态) 2)完整的API调用逻辑和异步任务处理 3)积分系统集成和合理定价 4)文件自动转存到R2存储 5)前端组件和测试页面(/ai-model-test) 6)数据库表结构和视图 7)按million tokens计费的文本模型
- 网站主题重新设计为"AI一站式工具平台"，包含对话AI模型、图片生成、视频生成、语音合成、语音复刻等功能模块，支持中英文双语
- 用户要求在 components/ai-dashboard/ 目录下实现响应式布局框架，包括：1) ResponsiveContainer 组件处理布局容器 2) useResponsiveStyles 和 useDeviceLayout hooks 3) 将布局逻辑、样式配置和业务逻辑完全分离 4) 严格限制改动范围在该目录下
- Modal组件开发教训：1.不要搞复杂的新旧版本兼容，直接用新版本 2.先检查是否存在专用翻译文件，是否应该创建专用的翻译文件，专用的翻译内容要放在专用文件如i18n/pages/ 3.使用const page = await get******Page(locale)获取数据和翻译，而不是useTranslations 4.组件要简洁，避免过度设计- 用户确认开始实施AI核心功能优化方案，从创建模型参数配置文件开始。用户提醒可以直接使用Supabase MCP工具。实施计划分三阶段：1)基础功能(单语言) 2)增强功能 3)多语言支持。当前开始第一阶段第一步：创建模型参数配置文件系统。
- 项目重构完成：1. 将config目录下的配置文件迁移到services/provider目录下对应的provider子目录中；2. 合并了重复的GRSAI配置，统一了参数结构；3. 删除了冗余代码和旧配置文件；4. 遵循简单有效的设计原则，每个provider目录包含完整的配置、模型、参数、服务代码；5. 合并了data/ai-models-local.ts和services/provider/grsai/models.ts中的重复内容，保留翻译内容在原文件中
- 删除了多余的data/ai-models-local.ts文件，将其功能迁移到services/provider/model-manager.ts中，实现了真正的统一模型管理。现在所有模型配置都在对应的provider目录下，GRSAI模型在services/provider/grsai/unified-config.ts中，Replicate模型在services/provider/replicate/parameters/image-models.ts中
