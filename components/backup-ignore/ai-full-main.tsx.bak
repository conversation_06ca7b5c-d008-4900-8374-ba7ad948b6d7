"use client";

import { useState, useEffect } from "react";
import { AIFullTypeSection } from "./ai-full-type-section";
import { AIFullConfigSection } from "./ai-full-config-section";
import { AIFullResultSection } from "./ai-full-result-section";
import { toast } from "sonner";

interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string;
  supported_features?: string[];
}

interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

interface AIModelModuleFullscreenProps {
  defaultModelType?: string;
}

export function AIFullMain({ defaultModelType = "image" }: AIModelModuleFullscreenProps) {
  const [activeTab, setActiveTab] = useState(defaultModelType);
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [prompt, setPrompt] = useState("");
  const [options, setOptions] = useState<any>({
    size: '1:1',
    aspectRatio: '1:1',
    variants: 1,
    temperature: 0.7,
    max_tokens: 1000,
    cdn: 'global'
  });
  const [loading, setLoading] = useState(false);
  const [costEstimate, setCostEstimate] = useState<any>(null);
  const [userCredits, setUserCredits] = useState<number>(0);
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    fetchUserCredits();
  }, []);

  useEffect(() => {
    if (selectedModel && prompt) {
      estimateCost();
    }
  }, [selectedModel, prompt, options]);

  // 当模型切换时，重置相关选项的默认值
  useEffect(() => {
    if (selectedModel) {
      const newOptions = { ...options };

      if (selectedModel.model_type === 'image') {
        if (!newOptions.size && !newOptions.aspectRatio) {
          newOptions.size = '1:1';
          newOptions.aspectRatio = '1:1';
        }
        if (!newOptions.variants) {
          newOptions.variants = 1;
        }
      } else if (selectedModel.model_type === 'text' || selectedModel.model_type === 'multimodal') {
        if (!newOptions.max_tokens) {
          newOptions.max_tokens = 1000;
        }
        if (!newOptions.temperature) {
          newOptions.temperature = 0.7;
        }
      }

      if (!newOptions.cdn) {
        newOptions.cdn = 'global';
      }

      setOptions(newOptions);
    }
  }, [selectedModel]);

  // 当切换tab时清空结果
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setGenerationResult(null);
    setIsGenerating(false);
    setSelectedModel(null);
    setPrompt("");
  };

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      if (data.code === 0) {
        setUserCredits(data.data.credits?.left_credits || 0);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };

  const estimateCost = async () => {
    if (!selectedModel || !prompt) return;

    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });
      const data = await response.json();
      if (data.code === 0) {
        setCostEstimate(data.data);
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  };

  const handleGenerate = async () => {
    if (!selectedModel || !prompt.trim()) {
      toast.error("请选择模型并输入提示词");
      return;
    }

    if (costEstimate && !costEstimate.user_credits.can_afford) {
      toast.error("积分不足，请充值后再试");
      return;
    }

    setLoading(true);
    setIsGenerating(true);
    setGenerationResult(null);

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: selectedModel.model_id,
          type: selectedModel.model_type,
          prompt,
          options
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        if (data.data.status === 'pending' || data.data.status === 'running') {
          setGenerationResult(data.data);
          // 使用task_id进行轮询
          pollResult(data.data.task_id);
        } else {
          setGenerationResult(data.data);
          toast.success("生成完成！");
          fetchUserCredits();
          setIsGenerating(false);
        }
      } else {
        toast.error(data.msg || "生成失败");
        setIsGenerating(false);
      }
    } catch (error) {
      toast.error("网络错误，请重试");
      setIsGenerating(false);
    } finally {
      setLoading(false);
    }
  };

  const pollResult = async (taskId: string) => {
    const maxAttempts = 60;
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ task_id: taskId })
        });

        const data = await response.json();

        if (data.code === 0) {
          setGenerationResult(data.data);

          if (data.data.status === 'success') {
            toast.success("生成完成！");
            fetchUserCredits();
            setIsGenerating(false);
            return;
          } else if (data.data.status === 'failed') {
            toast.error(`生成失败: ${data.data.error?.detail || '未知错误'}`);
            setIsGenerating(false);
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          toast.error("生成超时，请稍后查看结果");
        }
      } catch (error) {
        console.error('Polling error:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        }
      }
    };

    poll();
  };

  return (
    <div className="ai-dashboard-three-column">
      {/* 左侧工具栏 - 20% 宽度 */}
      <div className="ai-dashboard-sidebar p-4">
        <AIFullTypeSection
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* 中间配置区域 - 30% 宽度 */}
      <div className="ai-dashboard-main p-6">
        <AIFullConfigSection
          modelType={activeTab}
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          prompt={prompt}
          onPromptChange={setPrompt}
          options={options}
          onOptionsChange={setOptions}
          loading={loading}
          onGenerate={handleGenerate}
          costEstimate={costEstimate}
          userCredits={userCredits}
        />
      </div>

      {/* 右侧结果区域 - 50% 宽度 */}
      <div className="ai-dashboard-result p-4">
        <div className="ai-dashboard-result-content h-full">
          <AIFullResultSection
            generationResult={generationResult}
            modelType={activeTab}
          />
        </div>
      </div>
    </div>
  );
}
