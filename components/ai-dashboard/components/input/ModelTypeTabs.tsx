"use client";

import { Ta<PERSON>, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Image, Video } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../hooks/use-device-layout";  // 优化：导入设备布局hook
import type { ModelTypeConfig } from "../types";

interface ModelTypeTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  layout?: 'horizontal' | 'vertical';
  variant?: 'tabs' | 'buttons';
  isFullscreen?: boolean;
  typeButtonClassName?: string;
}

export function ModelTypeTabs({
  activeTab,
  onTabChange,
  layout = 'horizontal',
  variant = 'tabs',
  isFullscreen = false,
  typeButtonClassName = ''
}: ModelTypeTabsProps) {
  const t = useTranslations("ai-dashboard.tabs");
  const { isMobile, isSmallMobile, isDesktop } = useDeviceLayout();  // 优化：获取设备状态

  const modelTypes: ModelTypeConfig[] = [
    { value: 'text', label: t('text'), icon: MessageSquare, color: 'from-blue-500 to-purple-500' },
    { value: 'image', label: t('image'), icon: Image, color: 'from-green-500 to-teal-500' },
    { value: 'video', label: t('video'), icon: Video, color: 'from-orange-500 to-red-500' }
  ];

  // 如果是按钮模式，渲染垂直按钮列表
  if (variant === 'buttons') {
    return (
      <div className="space-y-3">
        {modelTypes.map(({ value, label, icon: Icon, color }) => (
          <Button
            key={value}
            variant={activeTab === value ? "default" : "ghost"}
            className={`${typeButtonClassName} transition-all duration-200 ${
              activeTab === value
                ? `bg-gradient-to-r ${color} text-white shadow-lg`
                : 'hover:bg-accent/50'
            }`}
            onClick={() => onTabChange(value)}
          >
            <div className={`p-2 rounded-lg ${activeTab === value ? 'bg-white/20' : 'bg-accent/20'}`}>
              <Icon className="w-4 h-4" />
            </div>
            {(!isDesktop || !isFullscreen) && <span className="font-medium">{label}</span>}
            {isFullscreen && isDesktop && (
              <span className="text-xs font-medium text-center leading-tight">{label.replace('Generate', '')}</span>
            )}
          </Button>
        ))}
      </div>
    );
  }

  // 默认标签页模式
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full max-w-full overflow-x-hidden">
      <TabsList className={`grid w-full max-w-full grid-cols-3 bg-gradient-to-r from-muted/50 to-muted/30 ${
        isSmallMobile ? 'mb-1 h-7' :  // 优化：超小屏幕最小下边距和高度
        isMobile ? 'mb-1 h-8' : 'mb-6 h-10'  // 优化：移动端减少下边距和高度
      }`}>
        {modelTypes.map(({ value, label, icon: Icon, color }) => (
          <TabsTrigger
            key={value}
            value={value}
            className={`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:${color} data-[state=active]:text-white ${
              isSmallMobile ? 'gap-1 text-xs px-1' :  // 优化：超小屏幕减少间距、字体和内边距
              isMobile ? 'gap-1.5 text-sm px-2' : 'gap-2 text-base px-3'
            }`}
          >
            <Icon className={`${
              isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕减少图标大小
              isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
            }`} />
            {isSmallMobile ? label.slice(0, 3) : label}  {/* 优化：超小屏幕简化文字 */}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
