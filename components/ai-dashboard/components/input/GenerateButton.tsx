"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Send } from "lucide-react";
import Icon from "@/components/icon";
import { useDeviceLayout } from "../../hooks/use-device-layout";
import { useButtonStyles } from "../../hooks/use-responsive-styles";
import { useTranslations } from "next-intl";
import type { AIModel, CostEstimate } from "../types";
import { BsStars } from "react-icons/bs";

interface GenerateButtonProps {
  loading: boolean;
  selectedModel: AIModel | null;
  prompt: string;
  costEstimate: CostEstimate | null;
  onGenerate: () => void;
}

export function GenerateButton({
  loading,
  selectedModel,
  prompt,
  costEstimate,
  onGenerate
}: GenerateButtonProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取超小屏幕状态
  const { className: buttonClassName } = useButtonStyles();
  const t = useTranslations("ai-dashboard.generator");

  const isDisabled = loading ||
    !selectedModel ||
    !prompt.trim() ||
    (costEstimate ? !costEstimate.user_credits.can_afford : false);

  return (
    <Button
      onClick={onGenerate}
      disabled={isDisabled}
      className={`${buttonClassName} w-full max-w-full font-semibold rounded-xl`}
      size={isSmallMobile ? "default" : isMobile ? "default" : "lg"}  // 优化：移动端使用默认尺寸
    >
      {loading ? (
        <>
        <Icon name="RiLoader5Fill" className="w-4 h-4 mr-1" />
          <Loader2 className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕最小图标
            isMobile ? 'w-4 h-4' : 'w-5 h-5'
          } ${isSmallMobile ? 'mr-1' : 'mr-2'} animate-spin`} />  {/* 优化：超小屏幕减少间距 */}
          <span className={isSmallMobile ? 'text-xs' : undefined}>  {/* 优化：超小屏幕减少字体 */}
            {isSmallMobile ? t("generating").slice(0, 6) + '...' : t("generating")}  {/* 优化：超小屏幕简化文字 */}
          </span>
        </>
      ) : (
        <>
        <BsStars  className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕最小图标
            isMobile ? 'w-4 h-4' : 'w-5 h-5'
          } ${isSmallMobile ? 'mr-1' : 'mr-2'}`} />  {/* 优化：超小屏幕减少间距 */}
          <span className={isSmallMobile ? 'text-xs' : undefined}>  {/* 优化：超小屏幕减少字体 */}
            {isSmallMobile ? t("start").slice(0, 4) : t("start")}  {/* 优化：超小屏幕简化文字 */}
          </span>
        </>
      )}
    </Button>
  );
}
