"use client";

import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../hooks/use-device-layout";  // 优化：导入设备布局hook

interface CreditsDisplayProps {
  userCredits: number;
}

export function CreditsDisplay({ userCredits }: CreditsDisplayProps) {
  const t = useTranslations("ai-dashboard.credits");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态

  return (
    <div className={`flex items-center text-muted-foreground bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30 w-full max-w-full ${
      isSmallMobile ? 'gap-1 p-1.5' :  // 优化：超小屏幕最小间距和内边距
      isMobile ? 'gap-2 p-2' : 'gap-3 p-3'
    }`}>  {/* 修复：移除overflow-x-hidden避免滚动条问题 */}
      <div>
        <span className={`font-medium text-foreground ${
          isSmallMobile ? 'text-xs' :  // 优化：超小屏幕最小字体
          isMobile ? 'text-xs' : 'text-sm'
        }`}>
          {isSmallMobile ? `${userCredits} ${t("credits")}` : `${t("current_balance")}: ${userCredits}`}  {/* 优化：超小屏幕简化文字 */}
        </span>
      </div>
    </div>
  );
}
