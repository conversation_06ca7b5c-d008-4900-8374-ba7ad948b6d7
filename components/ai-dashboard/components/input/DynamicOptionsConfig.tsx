"use client";

import { useEffect, useState, useMemo } from "react";
import { useTranslations, useLocale } from "next-intl";
import { AlertCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useDeviceLayout } from "../../hooks/use-device-layout";
import { ParameterGroup } from "./parameter-inputs/ParameterGroup";
import { ImageUploadInput } from "./parameter-inputs/ImageUploadInput";
import {
  getModelParameterConfig,
  getModelDefaultParameters
} from "@/services/provider/parameter-manager";
import {
  validateAllParameters,
  convertAllParametersForAPI
} from "@/lib/model-parameter-loader";
import { getAiModelTranslation } from "@/services/page";
import type { AIModel, GenerationOptions } from "../types";
import type { ParameterConfig } from "@/services/provider/types";

interface DynamicOptionsConfigProps {
  selectedModel: AIModel | null;
  options: GenerationOptions;
  onOptionsChange: (options: GenerationOptions) => void;
  modelsLoading?: boolean; // 添加模型加载状态
}

export function DynamicOptionsConfig({
  selectedModel,
  options,
  onOptionsChange,
  modelsLoading = false
}: DynamicOptionsConfigProps) {
  const t = useTranslations("ai-dashboard");
  const locale = useLocale();
  const { isMobile, isSmallMobile } = useDeviceLayout();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [translatedParameterConfig, setTranslatedParameterConfig] = useState<any>(null);

  // 获取模型参数配置
  const parameterConfig = useMemo(() => {
    if (!selectedModel?.model_id) return null;
    return getModelParameterConfig(selectedModel.model_id);
  }, [selectedModel?.model_id]);

  // 应用翻译到参数配置
  const applyTranslationToParameters = async (config: any, modelId: string) => {
    console.log('[applyTranslationToParameters] Function called with:', {
      config: !!config,
      modelId,
      provider: selectedModel?.provider,
      isGrsai: selectedModel?.provider === 'grsai'
    });

    if (!config || !selectedModel?.provider || selectedModel.provider !== 'grsai') {
      console.log('[applyTranslationToParameters] Early return - not grsai or missing config');
      return config;
    }

    try {
      const translations = await getAiModelTranslation(locale);
      const translationKey = `grsai.${modelId}`;
      const modelTranslation = translations.models[translationKey];

      if (!modelTranslation?.parameters) {
        return config;
      }

      console.log('[applyTranslationToParameters] Model translation found:', modelTranslation);
      console.log('[applyTranslationToParameters] Original config parameters:', config.parameters);

      const translatedConfig = {
        ...config,
        parameters: config.parameters.map((param: ParameterConfig) => {
          const paramTranslation = modelTranslation.parameters?.[param.name];
          console.log(`[applyTranslationToParameters] Processing param ${param.name}:`, {
            original: param,
            translation: paramTranslation
          });

          if (!paramTranslation) {
            console.log(`[applyTranslationToParameters] No translation for ${param.name}`);
            return param;
          }

          const translatedParam = {
            ...param,
            description: paramTranslation.description || param.description,
            tooltip: paramTranslation.tooltip || param.tooltip
          };

          console.log(`[applyTranslationToParameters] Translated param ${param.name}:`, translatedParam);

          // 处理选项翻译
          if (param.options && paramTranslation.options) {
            translatedParam.options = param.options.map(option => ({
              ...option,
              label: paramTranslation.options[option.value]?.label || option.label || option.value,
              description: paramTranslation.options[option.value]?.description || option.description
            }));
          }

          return translatedParam;
        })
      };

      console.log('[applyTranslationToParameters] Final translated config:', translatedConfig);

      return translatedConfig;
    } catch (error) {
      console.warn('Failed to apply parameter translations:', error);
      return config;
    }
  };

  // 当参数配置或语言变化时，应用翻译
  useEffect(() => {
    console.log('[DynamicOptionsConfig] useEffect triggered:', {
      parameterConfig: !!parameterConfig,
      modelId: selectedModel?.model_id,
      locale,
      provider: selectedModel?.provider
    });

    if (!parameterConfig || !selectedModel?.model_id) {
      console.log('[DynamicOptionsConfig] Missing parameterConfig or model_id, setting null');
      setTranslatedParameterConfig(null);
      return;
    }

    console.log('[DynamicOptionsConfig] About to apply translation to parameters...');
    applyTranslationToParameters(parameterConfig, selectedModel.model_id)
      .then(result => {
        console.log('[DynamicOptionsConfig] Translation applied, result:', result);
        setTranslatedParameterConfig(result);
      })
      .catch(error => {
        console.error('[DynamicOptionsConfig] Translation failed:', error);
        setTranslatedParameterConfig(parameterConfig);
      });
  }, [parameterConfig, selectedModel?.model_id, locale]);

  // 初始化默认值
  useEffect(() => {
    if (!selectedModel?.model_id || !translatedParameterConfig) return;

    setLoading(true);
    setError(null);

    try {
      const defaults = getModelDefaultParameters(selectedModel.model_id);
      console.log(`[DynamicConfig] Loading defaults for ${selectedModel.model_id}:`, defaults);

      // 对于模型切换，优先使用新模型的默认值，但保留图片上传等通用参数
      const mergedOptions = {
        ...defaults,
        // 保留图片上传相关的参数
        uploadedImages: options.uploadedImages || [],
        referenceImages: options.referenceImages || [],
        firstFrameUrl: options.firstFrameUrl
      };

      console.log(`[DynamicConfig] Merged options:`, mergedOptions);

      // 只有当值真的改变时才更新
      if (JSON.stringify(mergedOptions) !== JSON.stringify(options)) {
        console.log(`[DynamicConfig] Options changed, updating...`);
        onOptionsChange(mergedOptions);
      }
    } catch (err) {
      console.error('Failed to load model defaults:', err);
      setError('加载模型默认参数失败');
    } finally {
      setLoading(false);
    }
  }, [selectedModel?.model_id, translatedParameterConfig]);

  // 参数值变化处理
  const handleParameterChange = (name: string, value: any) => {
    console.log(`[DynamicConfig] Parameter changed: ${name}`, value);
    const newOptions = { ...options, [name]: value };
    console.log(`[DynamicConfig] New options:`, newOptions);
    onOptionsChange(newOptions);

    // 实时验证
    if (selectedModel?.model_id && parameterConfig) {
      const validation = validateAllParameters(selectedModel.model_id, newOptions);
      setValidationErrors(validation.errors);
    }
  };

  // 如果没有选择模型
  if (!selectedModel) {
    return (
      <div className={`flex items-center justify-center ${
        isSmallMobile ? 'py-4' : 'py-6'
      } text-muted-foreground`}>
        <p className={isSmallMobile ? 'text-sm' : 'text-base'}>
          请先选择一个AI模型
        </p>
      </div>
    );
  }

  // 如果模型不支持参数配置
  if (!parameterConfig) {
    return (
      <Alert className="border-orange-500/20 bg-orange-500/10">
        <AlertCircle className="h-4 w-4 text-orange-500" />
        <AlertDescription className={isSmallMobile ? 'text-sm' : 'text-base'}>
          模型 <code className="font-mono">{selectedModel.model_id}</code> 暂不支持参数配置，
          将使用默认设置。
        </AlertDescription>
      </Alert>
    );
  }

  // 加载中状态
  if (loading) {
    return (
      <div className={`flex items-center justify-center ${
        isSmallMobile ? 'py-4' : 'py-6'
      }`}>
        <Loader2 className={`animate-spin mr-2 ${
          isSmallMobile ? 'w-4 h-4' : 'w-5 h-5'
        }`} />
        <span className={`text-muted-foreground ${
          isSmallMobile ? 'text-sm' : 'text-base'
        }`}>
          加载参数配置中...
        </span>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className={isSmallMobile ? 'text-sm' : 'text-base'}>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  // 如果翻译配置还没准备好，显示加载状态（但不在模型加载时显示）
  if (!translatedParameterConfig && !modelsLoading) {
    return (
      <div className={`flex items-center justify-center ${
        isSmallMobile ? 'py-4' : 'py-6'
      }`}>
        <Loader2 className={`animate-spin mr-2 ${
          isSmallMobile ? 'w-4 h-4' : 'w-5 h-5'
        }`} />
        <span className={`text-muted-foreground ${
          isSmallMobile ? 'text-sm' : 'text-base'
        }`}>
          加载参数翻译中...
        </span>
      </div>
    );
  }

  // 如果模型正在加载或翻译配置还没准备好，显示空状态
  if (!translatedParameterConfig) {
    return (
      <div className={`text-center text-muted-foreground ${
        isSmallMobile ? 'text-sm py-4' : 'text-base py-6'
      }`}>
        请选择一个模型来配置参数
      </div>
    );
  }

  // 按组分类参数
  const parametersByGroup = {
    basic: translatedParameterConfig.parameters.filter(p =>
      translatedParameterConfig.parameterGroups.basic.includes(p.name)
    ),
    advanced: translatedParameterConfig.parameters.filter(p =>
      translatedParameterConfig.parameterGroups.advanced.includes(p.name)
    ),
    expert: translatedParameterConfig.parameters.filter(p =>
      translatedParameterConfig.parameterGroups.expert.includes(p.name)
    )
  };

  // 检查是否支持图片上传
  const supportsImageUpload = selectedModel.supported_features?.includes('image_upload');

  return (
    <div className={`w-full max-w-full overflow-x-hidden ${
      isSmallMobile ? 'space-y-2' : isMobile ? 'space-y-3' : 'space-y-4'
    }`}>
      {/* 图片上传功能 - 支持的模型显示 */}
      {supportsImageUpload && (
        <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden ${
          isSmallMobile ? 'px-2 py-1.5' : isMobile ? 'px-2.5 py-1.5' : 'px-3 py-2'
        }`}>
          <ImageUploadInput
            config={{
              name: 'uploadedImages',
              type: 'file',
              required: false,
              description: selectedModel.model_type === 'video' ? '首帧图片' :
                          selectedModel.model_type === 'image' ? '参考图片' : '图片上传',
              tooltip: '上传图片作为生成的参考或输入',
              group: 'basic'
            }}
            value={options.uploadedImages}
            onChange={(value) => handleParameterChange('uploadedImages', value)}
            error={validationErrors.uploadedImages}
            modelType={selectedModel.model_type}
          />
        </div>
      )}

      {/* 基础设置 */}
      {parametersByGroup.basic.length > 0 && (
        <ParameterGroup
          groupName="basic"
          parameters={parametersByGroup.basic}
          values={options}
          onChange={handleParameterChange}
          errors={validationErrors}
          defaultCollapsed={false}
          modelType={selectedModel.model_type}
        />
      )}

      {/* 高级设置 */}
      {parametersByGroup.advanced.length > 0 && (
        <ParameterGroup
          groupName="advanced"
          parameters={parametersByGroup.advanced}
          values={options}
          onChange={handleParameterChange}
          errors={validationErrors}
          defaultCollapsed={true}
          modelType={selectedModel.model_type}
        />
      )}

      {/* 专家设置 */}
      {parametersByGroup.expert.length > 0 && (
        <ParameterGroup
          groupName="expert"
          parameters={parametersByGroup.expert}
          values={options}
          onChange={handleParameterChange}
          errors={validationErrors}
          defaultCollapsed={true}
          modelType={selectedModel.model_type}
        />
      )}

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-4">
          <summary className="cursor-pointer text-sm text-muted-foreground">
            调试信息 (仅开发环境)
          </summary>
          <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
            {JSON.stringify({
              modelId: selectedModel.model_id,
              configVersion: parameterConfig.version,
              parameterCount: parameterConfig.parameters.length,
              currentValues: options,
              validationErrors,
              apiValues: convertAllParametersForAPI(selectedModel.model_id, options)
            }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
}
