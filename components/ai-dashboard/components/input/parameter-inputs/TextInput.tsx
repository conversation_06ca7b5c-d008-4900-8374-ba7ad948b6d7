"use client";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { HelpCircle } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/services/provider/types";

interface TextInputProps {
  config: ParameterConfig;
  value: string | undefined;
  onChange: (value: string) => void;
  error?: string;
  multiline?: boolean;
}

export function TextInput({ config, value, onChange, error, multiline = false }: TextInputProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();

  const currentValue = value || '';
  const placeholder = config.default ? config.default.toString() : '';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const inputClassName = `
    bg-gradient-to-r from-background to-muted/30 
    border-border/50 focus:border-border rounded-xl
    ${error ? 'border-destructive focus:border-destructive' : ''}
    ${isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'}
  `;

  return (
    <div className="space-y-2">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {config.description}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 输入框 */}
      {multiline ? (
        <Textarea
          value={currentValue}
          onChange={handleChange}
          placeholder={placeholder}
          className={`${inputClassName} min-h-[80px] resize-y`}
          rows={3}
        />
      ) : (
        <Input
          type="text"
          value={currentValue}
          onChange={handleChange}
          placeholder={placeholder}
          className={`${inputClassName} ${
            isSmallMobile ? 'h-8' : isMobile ? 'h-9' : 'h-10'
          }`}
        />
      )}

      {/* 字符计数（如果有最大长度限制） */}
      {config.max && (
        <div className={`flex justify-end text-muted-foreground ${
          isSmallMobile ? 'text-xs' : 'text-xs'
        }`}>
          <span className={currentValue.length > config.max ? 'text-destructive' : ''}>
            {currentValue.length} / {config.max}
          </span>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}

      {/* 长度提示 */}
      {(config.min !== undefined || config.max !== undefined) && !error && (
        <p className={`text-muted-foreground ${
          isSmallMobile ? 'text-xs' : 'text-xs'
        }`}>
          {config.min !== undefined && config.max !== undefined
            ? `长度: ${config.min} - ${config.max} 字符`
            : config.min !== undefined
            ? `最少 ${config.min} 字符`
            : `最多 ${config.max} 字符`
          }
        </p>
      )}
    </div>
  );
}
