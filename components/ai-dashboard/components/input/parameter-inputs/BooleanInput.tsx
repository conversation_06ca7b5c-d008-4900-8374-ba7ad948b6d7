"use client";

import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/services/provider/types";

interface BooleanInputProps {
  config: ParameterConfig;
  value: boolean | undefined;
  onChange: (value: boolean) => void;
  error?: string;
}

export function BooleanInput({ config, value, onChange, error }: BooleanInputProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();

  const currentValue = value !== undefined ? value : (config.default || false);

  return (
    <div className="space-y-2">
      {/* 开关控件和标签 */}
      <div className={`flex items-center justify-between ${
        isSmallMobile ? 'gap-2' : 'gap-3'
      }`}>
        <div className="flex items-center gap-2 flex-1">
          <Label 
            className={`font-medium text-foreground cursor-pointer ${
              isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
            }`}
            onClick={() => onChange(!currentValue)}
          >
            {config.description}
            {config.required && <span className="text-destructive ml-1">*</span>}
          </Label>
          
          {config.tooltip && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className={`text-muted-foreground cursor-help ${
                    isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                  }`} />
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="text-sm">{config.tooltip}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {/* 开关 */}
        <Switch
          checked={currentValue}
          onCheckedChange={onChange}
          className={`${error ? 'border-destructive' : ''}`}
        />
      </div>

      {/* 状态显示 */}
      <div className={`flex items-center justify-between ${
        isSmallMobile ? 'text-xs' : 'text-sm'
      }`}>
        <span className={`text-muted-foreground ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          当前状态: 
        </span>
        <span className={`font-medium ${
          currentValue ? 'text-green-600 dark:text-green-400' : 'text-gray-500'
        } ${isSmallMobile ? 'text-xs' : 'text-sm'}`}>
          {currentValue ? '启用' : '禁用'}
        </span>
      </div>

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}
    </div>
  );
}
