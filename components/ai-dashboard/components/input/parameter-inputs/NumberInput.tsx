"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { HelpCircle } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/services/provider/types";

interface NumberInputProps {
  config: ParameterConfig;
  value: number | undefined;
  onChange: (value: number | undefined) => void;
  error?: string;
}

export function NumberInput({ config, value, onChange, error }: NumberInputProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    if (inputValue === '') {
      onChange(undefined);
      return;
    }

    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      onChange(numValue);
    }
  };

  const displayValue = value !== undefined ? value.toString() : '';
  const placeholder = config.default !== undefined ? config.default.toString() : '';

  return (
    <div className="space-y-2">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {config.description}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 输入框 */}
      <Input
        type="number"
        min={config.min}
        max={config.max}
        step={config.step}
        value={displayValue}
        onChange={handleChange}
        placeholder={placeholder}
        className={`
          bg-gradient-to-r from-background to-muted/30 
          border-border/50 focus:border-border rounded-xl
          ${error ? 'border-destructive focus:border-destructive' : ''}
          ${isSmallMobile ? 'text-xs h-8' : isMobile ? 'text-sm h-9' : 'text-sm h-10'}
        `}
      />

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}

      {/* 范围提示 */}
      {(config.min !== undefined || config.max !== undefined) && !error && (
        <p className={`text-muted-foreground ${
          isSmallMobile ? 'text-xs' : 'text-xs'
        }`}>
          {config.min !== undefined && config.max !== undefined
            ? `范围: ${config.min} - ${config.max}`
            : config.min !== undefined
            ? `最小值: ${config.min}`
            : `最大值: ${config.max}`
          }
          {config.step && ` (步长: ${config.step})`}
        </p>
      )}
    </div>
  );
}
