"use client";

import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { HelpCircle } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/services/provider/types";
import { useState } from "react";

interface RangeInputProps {
  config: ParameterConfig;
  value: number | undefined;
  onChange: (value: number) => void;
  error?: string;
}

export function RangeInput({ config, value, onChange, error }: RangeInputProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();
  const [inputValue, setInputValue] = useState<string>('');

  const currentValue = value !== undefined ? value : (config.default || config.min || 0);
  const min = config.min || 0;
  const max = config.max || 100;
  const step = config.step || 1;

  const handleSliderChange = (values: number[]) => {
    const newValue = values[0];
    onChange(newValue);
    setInputValue(''); // 清空输入框状态
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputVal = e.target.value;
    setInputValue(inputVal);
  };

  const handleInputBlur = () => {
    if (inputValue !== '') {
      const numValue = parseFloat(inputValue);
      if (!isNaN(numValue)) {
        const clampedValue = Math.max(min, Math.min(max, numValue));
        onChange(clampedValue);
      }
    }
    setInputValue('');
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    }
  };

  const displayValue = inputValue !== '' ? inputValue : currentValue.toString();

  return (
    <div className="space-y-3">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {config.description}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 滑块和数值输入 */}
      <div className={`flex items-center ${isSmallMobile ? 'gap-2' : 'gap-3'}`}>
        {/* 滑块 */}
        <div className="flex-1">
          <Slider
            value={[currentValue]}
            onValueChange={handleSliderChange}
            min={min}
            max={max}
            step={step}
            className={`w-full ${error ? 'accent-destructive' : ''}`}
          />
        </div>

        {/* 数值输入框 */}
        <Input
          type="number"
          min={min}
          max={max}
          step={step}
          value={displayValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          className={`
            w-20 text-center bg-gradient-to-r from-background to-muted/30 
            border-border/50 focus:border-border rounded-xl
            ${error ? 'border-destructive focus:border-destructive' : ''}
            ${isSmallMobile ? 'text-xs h-7 w-16' : isMobile ? 'text-sm h-8 w-18' : 'text-sm h-9 w-20'}
          `}
        />
      </div>

      {/* 范围显示 */}
      <div className={`flex justify-between text-muted-foreground ${
        isSmallMobile ? 'text-xs' : 'text-xs'
      }`}>
        <span>{min}</span>
        <span className="font-medium text-foreground">
          {currentValue}
          {config.step && config.step < 1 && ` (步长: ${config.step})`}
        </span>
        <span>{max}</span>
      </div>

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}
    </div>
  );
}
