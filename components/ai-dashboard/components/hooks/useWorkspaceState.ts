"use client";

import { useState, useEffect } from "react";
import type { WorkspaceGenerationResult } from "../types";

export function useWorkspaceState() {
  // 从环境变量获取默认模型类型，如果没有设置则默认为image
  const defaultModelType = process.env.NEXT_PUBLIC_DEFAULT_MODEL_TYPE || "text";
  const [activeTab, setActiveTab] = useState(defaultModelType);
  const [generationResult, setGenerationResult] = useState<WorkspaceGenerationResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // 当切换tab时清空结果
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setGenerationResult(null);
    setIsGenerating(false);
  };

  return {
    activeTab,
    generationResult,
    isGenerating,
    handleTabChange,
    setGenerationResult,
    setIsGenerating
  };
}
