// components/examples/ModalExamples.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function ModalExamples() {
  const openModal = useModalStore((state) => state.openModal);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>全局模态框系统示例</CardTitle>
          <CardDescription>
            基于 Zustand 的类型安全模态框管理系统，支持懒加载和响应式设计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            
            {/* 登录模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">登录模态框</h3>
              <p className="text-sm text-muted-foreground">
                支持 Google 和 GitHub 登录，响应式设计
              </p>
              <Button
                onClick={() => openModal('login', {})}
                className="w-full"
              >
                打开登录模态框
              </Button>
            </div>

            {/* 定价模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">定价模态框</h3>
              <p className="text-sm text-muted-foreground">
                展示不同套餐选项，支持初始套餐设置
              </p>
              <div className="space-y-2">
                <Button
                  onClick={() => openModal('pricing', { initialPlan: 'free' })}
                  variant="outline"
                  className="w-full"
                >
                  免费套餐
                </Button>
                <Button
                  onClick={() => openModal('pricing', { initialPlan: 'pro', source: 'example' })}
                  className="w-full"
                >
                  专业套餐
                </Button>
              </div>
            </div>

            {/* 反馈模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">反馈模态框</h3>
              <p className="text-sm text-muted-foreground">
                收集用户反馈，支持评分和社交链接
              </p>
              <Button
                onClick={() => openModal('feedback', {
                  socialLinks: [
                    { name: 'GitHub', url: 'https://github.com' },
                    { name: 'Discord', url: 'https://discord.com' }
                  ]
                })}
                variant="outline"
                className="w-full"
              >
                打开反馈模态框
              </Button>
            </div>

            {/* AI 模型模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">AI 模型模态框</h3>
              <p className="text-sm text-muted-foreground">
                AI 模型配置和详情（开发中）
              </p>
              <div className="space-y-2">
                <Button
                  onClick={() => openModal('aiModel', { modelType: 'text' })}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  文本模型
                </Button>
                <Button
                  onClick={() => openModal('aiModel', { modelType: 'image', modelId: 'dall-e-3' })}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  图像模型
                </Button>
              </div>
            </div>

            {/* 用户资料模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">用户资料模态框</h3>
              <p className="text-sm text-muted-foreground">
                用户信息管理（开发中）
              </p>
              <Button
                onClick={() => openModal('userProfile', { tab: 'profile' })}
                variant="outline"
                className="w-full"
              >
                打开用户资料
              </Button>
            </div>

            {/* 设置模态框 */}
            <div className="space-y-2">
              <h3 className="font-semibold">设置模态框</h3>
              <p className="text-sm text-muted-foreground">
                应用程序设置（开发中）
              </p>
              <Button
                onClick={() => openModal('settings', { section: 'appearance' })}
                variant="outline"
                className="w-full"
              >
                打开设置
              </Button>
            </div>

          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">基本用法</h4>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`import { useModalStore } from '@/stores/modalStore';

const openModal = useModalStore((state) => state.openModal);

// 打开登录模态框
openModal('login', {});

// 打开定价模态框并设置初始套餐
openModal('pricing', { initialPlan: 'pro', source: 'header' });`}
            </pre>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">特性</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>✅ TypeScript 类型安全</li>
              <li>✅ 懒加载优化性能</li>
              <li>✅ 响应式设计（桌面/移动端）</li>
              <li>✅ 全局状态管理</li>
              <li>✅ 易于扩展新模态框</li>
              <li>✅ 自动处理 z-index 层级</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
