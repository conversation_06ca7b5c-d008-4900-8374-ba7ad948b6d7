"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Copy, Check } from "lucide-react";
import { ResponsiveImageContainer } from "@/components/ui/responsive-image-container";
import { PromptShowcaseProps } from "@/types/blocks/showcase";
import { useCarouselControl, useCopyPrompt } from "./hooks";


export default function PromptShowcase({
  section,
  enableCarousel = false,
  layout = 'vertical',
  imagePosition = 'left',
  cardWidth = 'auto',
  autoplayDelay = 0,
  initialDelay = 1000,
  transitionDuration = 500
}: PromptShowcaseProps) {
  const totalCards = section.items?.length || 0;

  // 使用自定义 hooks
  const carousel = useCarouselControl({
    totalItems: totalCards,
    autoplayDelay,
    initialDelay,
    enabled: enableCarousel
  });

  const copyPrompt = useCopyPrompt();

  if (section.disabled) {
    return null;
  }

  // 渲染单个卡片的函数
  const renderCard = (item: any, index: number) => {
    const actualIndex = enableCarousel ? carousel.currentIndex : index;

    if (layout === 'horizontal') {
      // 水平布局
      return (
        <Card
          key={index}
          className="group overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full"
        >
          <div className={`flex flex-col md:flex-row ${imagePosition === 'right' ? 'md:flex-row-reverse' : ''} h-full`}>
            {/* 图片区域 */}
            {item.image && (
              <div className="relative w-full md:w-1/2">
                <ResponsiveImageContainer
                  src={item.image.src}
                  alt={item.image.alt || item.title || ''}
                  aspectRatio="aspect-[4/3] md:aspect-[3/4]"
                  className="w-full"
                />
                {item.category && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="outline" className="bg-background/80 backdrop-blur-sm text-xs">
                      {item.category}
                    </Badge>
                  </div>
                )}
              </div>
            )}

            {/* 内容区域 */}
            <div className="flex-1 flex flex-col">
              {item.title && (
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-semibold line-clamp-2">
                    {item.title}
                  </CardTitle>
                  {item.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {item.description}
                    </p>
                  )}
                </CardHeader>
              )}

              <CardContent className="flex-1 flex flex-col space-y-2 pt-0">
                {/* 标签区域 */}
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="text-xs px-2 py-0.5"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Prompt 显示区域 */}
                <div className="flex-1">
                  <div className="bg-muted/50 rounded-lg p-2 border border-border/50 h-20 overflow-y-auto">
                    <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                      {item.prompt}
                    </p>
                  </div>

                  {/* 复制按钮 */}
                  <Button
                    onClick={() => copyPrompt.copyPrompt(item.prompt, actualIndex)}
                    variant="outline"
                    size="sm"
                    className="w-full mt-2"
                  >
                    {copyPrompt.isCopied(actualIndex) ? (
                      <>
                        <Check className="w-3 h-3 mr-1" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-3 h-3 mr-1" />
                        复制 Prompt
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </div>
          </div>
        </Card>
      );
    } else {
      // 垂直布局（原有布局）
      return (
        <Card
          key={index}
          className="group overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full"
        >
          {/* 主要图片区域 */}
          {item.image && (
            <div className="relative">
              <ResponsiveImageContainer
                src={item.image.src}
                alt={item.image.alt || item.title || ''}
                aspectRatio="aspect-[4/3]"
                className="w-full"
              />
              {item.category && (
                <div className="absolute top-2 right-2">
                  <Badge variant="outline" className="bg-background/80 backdrop-blur-sm text-xs">
                    {item.category}
                  </Badge>
                </div>
              )}
            </div>
          )}

          {item.title && (
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-semibold line-clamp-2">
                {item.title}
              </CardTitle>
              {item.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {item.description}
                </p>
              )}
            </CardHeader>
          )}

          <CardContent className="flex-1 flex flex-col space-y-2 pt-0">
            {/* 标签区域 */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                  <Badge
                    key={tagIndex}
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {tag}
                  </Badge>
                ))}
                {item.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    +{item.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Prompt 显示区域 */}
            <div className="flex-1">
              <div className="bg-muted/50 rounded-lg p-2 border border-border/50 h-20 overflow-y-auto">
                <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                  {item.prompt}
                </p>
              </div>

              {/* 复制按钮 */}
              <Button
                onClick={() => copyPrompt.copyPrompt(item.prompt, actualIndex)}
                variant="outline"
                size="sm"
                className="w-full mt-2"
              >
                {copyPrompt.isCopied(actualIndex) ? (
                  <>
                    <Check className="w-3 h-3 mr-1" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-3 h-3 mr-1" />
                    复制 Prompt
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      {enableCarousel ? (
        // 卡片轮播模式（淡入淡出）
        <div className="relative">
          <div className="relative min-h-[600px]">
            {section.items?.map((item, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity ${
                  index === carousel.currentIndex ? 'opacity-100' : 'opacity-0'
                }`}
                style={{
                  transitionDuration: `${transitionDuration}ms`
                }}
              >
                <div className={`relative ${
                  cardWidth === '50%'
                    ? 'max-w-[50%] mx-auto'
                    : cardWidth === '100%'
                    ? 'w-full'
                    : 'max-w-4xl mx-auto'
                }`}>
                  {renderCard(item, index)}

                  {/* 轮播指示器 - 在卡片内部底部 */}
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
                    {section.items?.map((_, dotIndex) => (
                      <button
                        key={dotIndex}
                        className={`w-2 h-2 rounded-full transition-all cursor-pointer hover:scale-125 ${
                          dotIndex === carousel.currentIndex ? 'bg-primary' : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                        }`}
                        onClick={() => carousel.setCurrentIndex(dotIndex)}
                        aria-label={`切换到第 ${dotIndex + 1} 张图片`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        // 网格布局
        <div className={`grid gap-6 ${
          cardWidth === '50%'
            ? 'grid-cols-1 md:grid-cols-2'
            : cardWidth === '100%'
            ? 'grid-cols-1'
            : layout === 'horizontal'
            ? 'grid-cols-1 lg:grid-cols-2'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {section.items?.map((item, index) => renderCard(item, index))}
        </div>
      )}
    </section>
  );
}
