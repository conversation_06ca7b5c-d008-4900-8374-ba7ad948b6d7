import { useState, useEffect, useCallback, useRef } from 'react';

// 轮播控制 Hook
interface UseCarouselControlProps {
  totalItems: number;
  autoplayDelay: number;
  initialDelay?: number;
  enabled: boolean;
}

interface UseCarouselControlReturn {
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
  goToNext: () => void;
  goToPrevious: () => void;
  resetAutoplay: () => void;
}

export function useCarouselControl({
  totalItems,
  autoplayDelay,
  initialDelay = 0,
  enabled
}: UseCarouselControlProps): UseCarouselControlReturn {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const initialTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清除定时器
  const clearAutoplay = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 清除初始延迟定时器
  const clearInitialTimeout = useCallback(() => {
    if (initialTimeoutRef.current) {
      clearTimeout(initialTimeoutRef.current);
      initialTimeoutRef.current = null;
    }
  }, []);

  // 启动自动播放
  const startAutoplay = useCallback(() => {
    if (enabled && autoplayDelay > 0 && totalItems > 1 && isInitialized) {
      clearAutoplay();
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % totalItems);
      }, autoplayDelay);
    }
  }, [enabled, autoplayDelay, totalItems, isInitialized, clearAutoplay]);

  // 重置自动播放
  const resetAutoplay = useCallback(() => {
    clearAutoplay();
    startAutoplay();
  }, [clearAutoplay, startAutoplay]);

  // 手动设置当前索引并重置自动播放
  const handleSetCurrentIndex = useCallback((index: number) => {
    setCurrentIndex(index);
    resetAutoplay();
  }, [resetAutoplay]);

  // 下一张
  const goToNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % totalItems;
    handleSetCurrentIndex(nextIndex);
  }, [currentIndex, totalItems, handleSetCurrentIndex]);

  // 上一张
  const goToPrevious = useCallback(() => {
    const prevIndex = currentIndex === 0 ? totalItems - 1 : currentIndex - 1;
    handleSetCurrentIndex(prevIndex);
  }, [currentIndex, totalItems, handleSetCurrentIndex]);

  // 初始化轮播
  useEffect(() => {
    if (enabled && totalItems > 1) {
      clearInitialTimeout();
      if (initialDelay > 0) {
        initialTimeoutRef.current = setTimeout(() => {
          setIsInitialized(true);
        }, initialDelay);
      } else {
        setIsInitialized(true);
      }
    }

    return () => {
      clearAutoplay();
      clearInitialTimeout();
    };
  }, [enabled, totalItems, initialDelay, clearAutoplay, clearInitialTimeout]);

  // 当初始化完成时启动自动播放
  useEffect(() => {
    if (isInitialized) {
      startAutoplay();
    } else {
      clearAutoplay();
    }
  }, [isInitialized, startAutoplay, clearAutoplay]);

  // 当 totalItems 变化时重置索引
  useEffect(() => {
    if (currentIndex >= totalItems) {
      setCurrentIndex(0);
    }
  }, [totalItems, currentIndex]);

  return {
    currentIndex,
    setCurrentIndex: handleSetCurrentIndex,
    goToNext,
    goToPrevious,
    resetAutoplay
  };
}



// 复制 Prompt Hook
interface UseCopyPromptReturn {
  copiedIndex: number | null;
  copyPrompt: (prompt: string, index: number) => Promise<void>;
  isCopied: (index: number) => boolean;
}

export function useCopyPrompt(): UseCopyPromptReturn {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const copyPrompt = useCallback(async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      // 2秒后重置复制状态
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
      // 可以在这里添加错误提示
    }
  }, []);

  const isCopied = useCallback((index: number) => {
    return copiedIndex === index;
  }, [copiedIndex]);

  return {
    copiedIndex,
    copyPrompt,
    isCopied
  };
}
