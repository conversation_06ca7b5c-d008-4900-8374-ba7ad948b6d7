"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { Copy, Check, ArrowRight } from "lucide-react";
import Image from "next/image";
import { ComparisonShowcaseProps } from "@/types/blocks/showcase";


export default function ComparisonShowcase({
  section,
  enableCarousel = false,
  cardWidth = 'auto',
  autoplayDelay = 0
}: ComparisonShowcaseProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // 卡片轮播设置（淡入淡出）
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const totalCards = section.items?.length || 0;

  // 自动播放
  useEffect(() => {
    if (enableCarousel && autoplayDelay > 0 && totalCards > 1) {
      const interval = setInterval(() => {
        setCurrentCardIndex((prev) => (prev + 1) % totalCards);
      }, autoplayDelay);
      return () => clearInterval(interval);
    }
  }, [enableCarousel, autoplayDelay, totalCards]);

  if (section.disabled) {
    return null;
  }

  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  // 渲染单个卡片的函数
  const renderCard = (item: any, index: number) => {
    return (
      <Card
        key={index}
        className="overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col max-w-4xl mx-auto w-full"
      >
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between gap-2">
            <CardTitle className="text-xl font-semibold line-clamp-2 flex-1">
              {item.title}
            </CardTitle>
            {item.category && (
              <Badge variant="outline" className="shrink-0">
                {item.category}
              </Badge>
            )}
          </div>
          {item.description && (
            <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
              {item.description}
            </p>
          )}
        </CardHeader>

        <CardContent className="flex-1 flex flex-col space-y-6">
          {/* 图片对比区域 */}
          <div className="space-y-4">
            {/* 移动端：垂直布局 */}
            <div className="block md:hidden space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">处理前</p>
                <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                  <Image
                    src={item.beforeImage.src}
                    alt={item.beforeImage.alt}
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>

              <div className="flex justify-center">
                <ArrowRight className="w-6 h-6 text-muted-foreground rotate-90" />
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">处理后</p>
                <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                  <Image
                    src={item.afterImage.src}
                    alt={item.afterImage.alt}
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>
            </div>

            {/* 桌面端：水平布局 */}
            <div className="hidden md:flex items-center gap-4">
              <div className="flex-1 space-y-2">
                <p className="text-sm font-medium text-muted-foreground">处理前</p>
                <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                  <Image
                    src={item.beforeImage.src}
                    alt={item.beforeImage.alt}
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>

              <div className="flex-shrink-0">
                <ArrowRight className="w-6 h-6 text-muted-foreground" />
              </div>

              <div className="flex-1 space-y-2">
                <p className="text-sm font-medium text-muted-foreground">处理后</p>
                <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                  <Image
                    src={item.afterImage.src}
                    alt={item.afterImage.alt}
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Prompt 内容区域 */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-foreground">使用的 Prompt</p>
            <div className="bg-muted/50 rounded-lg p-4 border border-border/50">
              <p className="text-sm font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                {item.prompt}
              </p>
            </div>
          </div>

          {/* 标签区域 */}
          {item.tags && item.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {item.tags.slice(0, 4).map((tag: string, tagIndex: number) => (
                <Badge
                  key={tagIndex}
                  variant="secondary"
                  className="text-xs px-2 py-1"
                >
                  {tag}
                </Badge>
              ))}
              {item.tags.length > 4 && (
                <Badge variant="secondary" className="text-xs px-2 py-1">
                  +{item.tags.length - 4}
                </Badge>
              )}
            </div>
          )}

          {/* 复制按钮 */}
          <Button
            onClick={() => handleCopyPrompt(item.prompt, index)}
            variant="outline"
            size="sm"
            className="w-full transition-all duration-200 hover:bg-primary hover:text-primary-foreground"
          >
            {copiedIndex === index ? (
              <>
                <Check className="w-4 h-4 mr-2" />
                已复制
              </>
            ) : (
              <>
                <Copy className="w-4 h-4 mr-2" />
                复制 Prompt
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      {enableCarousel ? (
        // 卡片轮播模式（淡入淡出）
        <div className="relative">
          <div className="relative min-h-[800px]">
            {section.items?.map((item, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-300 ${
                  index === currentCardIndex ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <div className={`relative ${
                  cardWidth === '50%'
                    ? 'max-w-[50%] mx-auto'
                    : cardWidth === '100%'
                    ? 'w-full'
                    : 'max-w-4xl mx-auto'
                }`}>
                  {renderCard(item, index)}

                  {/* 轮播指示器 - 在卡片内部底部 */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-1">
                    {section.items?.map((_, dotIndex) => (
                      <button
                        key={dotIndex}
                        className={`w-2 h-2 rounded-full transition-all ${
                          dotIndex === currentCardIndex ? 'bg-primary' : 'bg-muted-foreground/30'
                        }`}
                        onClick={() => setCurrentCardIndex(dotIndex)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        // 网格布局
        <div className={`grid gap-8 ${
          cardWidth === '50%'
            ? 'grid-cols-1 lg:grid-cols-2'
            : cardWidth === '100%'
            ? 'grid-cols-1'
            : 'grid-cols-1 max-w-none'
        }`}>
          {section.items?.map((item, index) => renderCard(item, index))}
        </div>
      )}
    </section>
  );
}