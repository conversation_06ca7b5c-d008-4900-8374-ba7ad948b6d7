import { cn } from '@/lib/utils';

interface ResponsiveImageContainerProps {
  src: string;
  alt: string;
  aspectRatio?: string;
  className?: string;
  imageClassName?: string;
}

export function ResponsiveImageContainer({
  src,
  alt,
  aspectRatio = 'aspect-[4/3]',
  className,
  imageClassName
}: ResponsiveImageContainerProps) {
  return (
    <div className={cn(
      'relative w-full overflow-hidden bg-muted/20',
      aspectRatio,
      className
    )}>
      <img
        src={src}
        alt={alt}
        className={cn(
          'w-full h-full object-contain transition-transform duration-300 hover:scale-105',
          imageClassName
        )}
      />
    </div>
  );
}
