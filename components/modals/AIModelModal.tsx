// components/modals/AIModelModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface AIModelModalProps {
  modelType?: 'text' | 'image' | 'video' | 'audio';
  modelId?: string;
}

export default function AIModelModal({ 
  modelType = 'text',
  modelId 
}: AIModelModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);

  return (
    <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>AI 模型详情</DialogTitle>
          <DialogDescription>
            查看和配置 AI 模型设置
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <p>模型类型: {modelType}</p>
          {modelId && <p>模型ID: {modelId}</p>}
          <p className="text-muted-foreground">
            此模态框正在开发中...
          </p>
          <Button onClick={closeModal} className="w-full">
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
