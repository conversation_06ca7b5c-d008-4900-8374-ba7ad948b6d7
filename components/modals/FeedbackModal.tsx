// components/modals/FeedbackModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Send, MessageCircle, Star } from "lucide-react";

interface FeedbackModalProps {
  socialLinks?: Array<{
    name: string;
    url: string;
    icon?: string;
  }>;
}

export default function FeedbackModal({ socialLinks = [] }: FeedbackModalProps) {
  const t = useTranslations();
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  
  const [feedback, setFeedback] = useState('');
  const [rating, setRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!feedback.trim()) return;
    
    setIsSubmitting(true);
    try {
      // 这里可以添加提交反馈的API调用
      console.log('提交反馈:', { feedback, rating });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      closeModal();
    } catch (error) {
      console.error('提交反馈失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const content = (
    <div className="space-y-6">
      {/* 评分 */}
      <div>
        <label className="text-sm font-medium mb-2 block">
          您对我们的服务满意吗？
        </label>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              onClick={() => setRating(star)}
              className={`p-1 rounded transition-colors ${
                star <= rating
                  ? 'text-yellow-500'
                  : 'text-gray-300 hover:text-yellow-400'
              }`}
            >
              <Star className="h-6 w-6 fill-current" />
            </button>
          ))}
        </div>
      </div>

      {/* 反馈内容 */}
      <div>
        <label className="text-sm font-medium mb-2 block">
          请告诉我们您的想法
        </label>
        <Textarea
          placeholder="您的建议、问题或反馈..."
          value={feedback}
          onChange={(e) => setFeedback(e.target.value)}
          rows={4}
          className="resize-none"
        />
      </div>

      {/* 社交链接 */}
      {socialLinks.length > 0 && (
        <div>
          <label className="text-sm font-medium mb-2 block">
            或者通过其他方式联系我们
          </label>
          <div className="flex gap-2 flex-wrap">
            {socialLinks.map((link, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => window.open(link.url, '_blank')}
              >
                <MessageCircle className="h-4 w-4 mr-1" />
                {link.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* 提交按钮 */}
      <Button
        onClick={handleSubmit}
        disabled={!feedback.trim() || isSubmitting}
        className="w-full"
      >
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            提交中...
          </>
        ) : (
          <>
            <Send className="h-4 w-4 mr-2" />
            提交反馈
          </>
        )}
      </Button>
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>反馈与建议</DialogTitle>
            <DialogDescription>
              您的反馈对我们非常重要，帮助我们提供更好的服务
            </DialogDescription>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>反馈与建议</DrawerTitle>
          <DrawerDescription>
            您的反馈对我们非常重要，帮助我们提供更好的服务
          </DrawerDescription>
        </DrawerHeader>
        <div className="px-4">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              稍后再说
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
