// components/modals/UserProfileModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface UserProfileModalProps {
  userId?: string;
  tab?: 'profile' | 'settings' | 'billing';
}

export default function UserProfileModal({ 
  userId,
  tab = 'profile'
}: UserProfileModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);

  return (
    <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>用户资料</DialogTitle>
          <DialogDescription>
            查看和编辑用户信息
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <p>当前标签: {tab}</p>
          {userId && <p>用户ID: {userId}</p>}
          <p className="text-muted-foreground">
            此模态框正在开发中...
          </p>
          <Button onClick={closeModal} className="w-full">
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
