// components/modals/SettingsModal.tsx
'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";

interface SettingsModalProps {
  section?: 'general' | 'appearance' | 'notifications' | 'privacy';
}

export default function SettingsModal({ 
  section = 'general'
}: SettingsModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);

  return (
    <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置</DialogTitle>
          <DialogDescription>
            配置应用程序设置
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <p>当前部分: {section}</p>
          <p className="text-muted-foreground">
            此模态框正在开发中...
          </p>
          <Button onClick={closeModal} className="w-full">
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
